function initVideo(moduleId, layout, options) {
    window['initFunc' + moduleId] = function () {
        var wh = $(window).width()
        if ((options.PlayMode == 0 && options.ShowType == 0) || options.PlayMode == 1 || wh < 787) {
            if ($('#module_' + moduleId + ' .videogiant-container .videoPlayBtn').length > 0) {
                $('#module_' + moduleId + ' .videogiant-container').off().on('click', function () {
                    if (options.PlayMode == 1) {
                        openVideoDialog($('#module_' + moduleId + ' .videoWrapper').html(),options);
                    } else {
                        // 以下标签在有封面图的情况下，点击播放按钮触发标签的视频自动播放
                        if ($('#module_' + moduleId + ' .videogiant-container video').length > 0) {
                            $('#module_' + moduleId + ' .videogiant-container video').trigger('play');
                        } else if ($('#module_' + moduleId + ' .videogiant-container embed').length > 0) {
                            $('#module_' + moduleId + ' .videogiant-container embed').attr('flashvars', "isAutoPlay=true");
                            var videoHtml = $('#module_' + moduleId + ' .videoWrapper').html().trim();
                            $('#module_' + moduleId + ' .videoWrapper').html(videoHtml);
                        }
                        $(this).find('.videoPlayBtn').hide();
                        $(this).find('.videoCoverPic').hide();
                    }
                })
            }
        }
        //非点击播放的
        if (options.PlayMode == 0) {
            if (wh > 787) {
                if (options.ShowType == 1) {
                    $('#module_' + moduleId + ' .videogiant-container .videoWrapper video').removeAttr('controls')
                    $('#module_' + moduleId + ' .videogiant-container .videoPlayBtn').hide()
                    $('#module_' + moduleId + ' .videogiant-container').hover(function () {
                        if (!$(this).find('.videoCoverPic').hasClass('playModeBox')) $(this).find('.videoCoverPic').css({ 'visibility': 'hidden', 'opacity': '0' })
                        $(this).find('.videoWrapper video')[0].play()
                    }, function () {
                        if (!$(this).find('.videoCoverPic').hasClass('playModeBox')) $(this).find('.videoCoverPic').css({ 'visibility': 'visible', 'opacity': '1' })
                        $(this).find('.videoWrapper video')[0].pause()
                        //$(this).find('.videoWrapper video')[0].currentTime = 0
                    })
                } else if (options.ShowType == 2) {
                    $('#module_' + moduleId + ' .videogiant-container .videoPlayBtn').hide()
                    $('#module_' + moduleId + ' .videogiant-container .videoWrapper video')[0].currentTime = 0
                    $('#module_' + moduleId + ' .videogiant-container .videoWrapper video')[0].play()
                }
            } else {
               if(!$('#module_' + moduleId + ' .videogiant-container').hasClass('iframeBox')){
                    $('#module_' + moduleId + ' .videogiant-container .videoWrapper video').attr('controls', true)
                    $('#module_' + moduleId + ' .videogiant-container .videoWrapper video')[0].pause()
                    $('#module_' + moduleId + ' .videogiant-container .videoWrapper video')[0].currentTime = 0
               }
               $('#module_' + moduleId + ' .videogiant-container .videoPlayBtn').show()
            }
        }
        if(options.CanDesign && options.CanEditFront && wh < 787 && options.videoCoverPic == ''){
            $('#module_' + moduleId + ' .videogiant-container .videoPlayBtn').hide()
        }
    }
    $(window).off('resize.video' + moduleId).on('resize.video' + moduleId, function () {
        window['initFunc' + moduleId]();
    });
    window['initFunc' + moduleId]();
}

// 视频弹出播放窗口
function openVideoDialog(obj,options) {
    if (obj) {
        var width = '960px'
        var height = '540px'
        if(options.PopupType == 1){
            width = '100%'
            height = '100%'
        }else if(options.PopupType == 2){
            width = options.PopupSizeWidth + 'px'
            height = options.PopupSizeHeight+ 'px'
        }


        var html = '';
        html += '<div id="bgVideoMask">';
        html += '<div class="popupBgForWin"></div>';
        html += '<div class="videoDialog">';
        html += '<div class="dialogVideoBox">';
        html += '<div class="cancelDialogVideo"></div>';
        html += '<div class="clearmb">';
        html += '<div class="playVideo">';
        html += obj;
        html += '</div></div></div></div></div>';

        $('body').append(html);

        if ($('#bgVideoMask video').length > 0) {
            $('#bgVideoMask video').trigger('play');
        } else if ($('#bgVideoMask embed').length > 0) {
            $('#bgVideoMask embed').attr('flashvars', "isAutoPlay=true");
            var videoHtml = $('#bgVideoMask .playVideo').html().trim();
            $('#bgVideoMask .playVideo').html(videoHtml);
        }

        $('#bgVideoMask').find('.popupBgForWin').css({ 'position': 'fixed', 'background-color': '#000', 'opacity': '.7', '-webkit-transition': 'all 3s', 'transition': 'all 3s', 'z-index': '9499', 'margin': '0', 'padding': '0', 'left': '0', 'top': '0', 'right': '0', 'bottom': '0', 'margin': 'auto', 'overflow': 'hidden', 'height': '100%' });
        $('#bgVideoMask').find('.videoDialog').css({ 'position': 'fixed', 'z-index': '10000', 'left': '0', 'top': '0', 'right': '0', 'bottom': '0', 'margin': 'auto', 'width': width, 'height': height });
        $('#bgVideoMask').find('.dialogVideoBox').css({ 'position': 'relative', 'height': '100%' });
        $('#bgVideoMask').find('.cancelDialogVideo').css({ 'position': 'absolute', 'z-index': '10001', 'cursor': 'pointer', 'right': '20px', 'background': 'url(/skinp/modules/ModuleVideoGiant/image/vbg.png) -127px -142px no-repeat', 'margin-top': '14px', 'width': '34px', 'height': '34px' });

        $('#bgVideoMask').find('.clearmb').css({ 'overflow': 'hidden', 'background-color': '#000', 'width': width, 'height': height });
        if(options.PopupType == 2){
            $('#bgVideoMask').find('.playVideo').css({ 'position': 'absolute', 'z-index': '9999', 'width': '100%', 'height': height, 'top': '50%', 'left': '50%', 'margin': 'auto', 'transform': 'translate(-50%,-50%)'});

        }else{
            $('#bgVideoMask').find('.playVideo').css({ 'position': 'absolute', 'z-index': '9999', 'width': '100%', 'height': '0', 'top': '0', 'bottom': '0', 'margin': 'auto', 'padding-bottom': '55.95%' });
        }
        $('#bgVideoMask').find('iframe').css({ 'width': '100%', 'height': '100%', 'position': 'absolute' });
        $('#bgVideoMask').find('embed').css({ 'width': '100%', 'height': '100%', 'position': 'absolute' });
        $('#bgVideoMask').find('object').css({ 'width': '100%', 'height': '100%', 'position': 'absolute' });
        $('#bgVideoMask').find('video').css({ 'width': '100%', 'height': '100%', 'position': 'absolute' });

        if (window.innerWidth < 769) {
            $('#bgVideoMask').find('.popupBgForWin').css({ 'opacity': '1' })
            $('#bgVideoMask').find('.videoDialog').css({ 'width': '100%', 'height': '100%' });
            $('#bgVideoMask').find('.clearmb').css({ 'width': '100%', 'height': '100%' });
        }

        $(window).on('resize', function () {
            if (window.innerWidth > 768) {
                $('#bgVideoMask').find('.popupBgForWin').css({ 'opacity': '.7' })
                $('#bgVideoMask').find('.videoDialog').css({ 'width': width, 'height': height });
                $('#bgVideoMask').find('.clearmb').css({ 'width': width, 'height': height });
            } else {
                $('#bgVideoMask').find('.popupBgForWin').css({ 'opacity': '1' })
                $('#bgVideoMask').find('.videoDialog').css({ 'width': '100%', 'height': '100%' });
                $('#bgVideoMask').find('.clearmb').css({ 'width': '100%', 'height': '100%' });
            }
        });

        $('#bgVideoMask .cancelDialogVideo').off().on('click', function () {
            $(this).parents('#bgVideoMask').remove();
        })
    }
}