function hoverBoxGiantInit(moduleid, options) {
    if (window.CanDesign != 'True') {
        $('#module_' + moduleid + ' .ModuleHoverBoxContainer .ModuleSubContainer').addClass('ModuleSubContainerHover');
        $('#module_' + moduleid + ' .ModuleSubContainerHover').hover(function () {
            $('#module_' + moduleid + ' .ModuleHoverBoxItem_2').css({ "opacity": "1", "display": "block" })
            if (!$('#module_' + moduleid + ' .ModuleHoverBoxItem_2').hasClass(options.Animation)) $('#module_' + moduleid + ' .ModuleHoverBoxItem_2').addClass(options.Animation)
            initFuncCommon($('#module_' + moduleid + ' .ModuleHoverBoxItem_2'))
        }, function () {
            if (options.Animation == 'switchAnimateNone') {
                $('#module_' + moduleid + ' .ModuleHoverBoxItem_2').hide()

            }
        })
    }
    function initFuncCommon(obj) {
        obj.find('*').each(function (idx, el) {
            if ($(this).is('.ModuleItem')) {
                var moduleid = ($(this).attr('id') || '').replace(/module_/i, '');
                var initFunc = window['initFunc' + moduleid];
                var initSwiperFunc = window['initSwiperFunc' + moduleid];
                var initSwiperFuncSiteGalleryByMobile = window['initSwiperFunc' + moduleid + 'SiteGalleryByMobile'];
                var news121Multiple = window['news121Multiple' + moduleid];
                if (typeof initFunc == 'function') {
                    initFunc()
                }
                if (typeof initSwiperFunc == 'function') {
                    initSwiperFunc()
                }
                if (typeof initSwiperFuncSiteGalleryByMobile == 'function') {
                    initSwiperFuncSiteGalleryByMobile();
                }
                if (typeof news121Multiple == 'function') {
                    news121Multiple()
                }
            }
        });
    }

}