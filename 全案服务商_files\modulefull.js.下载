function initFullGiant(ModuleID, options) {
    $(document).ready(function () {
        window['initFunc' + ModuleID] = function () {
            if (window.CanDesign === "True") {
                // 编辑状态下，动态改变窗体padding-right
                if (top.$('#moduleHelperFull').length > 0 && window.innerWidth > 767) {
                    if (top.$('#moduleHelperFull').hasClass('moduleHelperFullHide')) {
                        top.$("#pageframe").css({ 'width': '100%', 'padding-right': '40px' });
                    } else {
                        top.$("#pageframe").css({ 'width': '100%', 'padding-right': '260px' });
                    }
                }
            }
            // 当swiper实例化已经存在，先执行update，以防止端切换时无更新
            if (window['fullSwiper' + ModuleID]) window['fullSwiper' + ModuleID].update();

            // 当为移动端且滚屏不需要应用时，需销毁swiper
            if ($('#module_' + ModuleID + ' .IsNotApplyMobile').length > 0 && window.innerWidth < 768) {
                if (window['fullSwiper' + ModuleID]) window['fullSwiper' + ModuleID].destroy(true, true);
                window['fullSwiper' + ModuleID] = '';
                return;
            }

            // 当已经存在实例化则不重新实例化swiper
            if (window['fullSwiper' + ModuleID]) return;

            var fullSwiperStartScroll, fullSwiperTouchStart, fullSwiperTouchCurrent;
            window['fullSwiper' + ModuleID] = new Swiper('#module_' + ModuleID + ' .ModuleFullContainer' + ModuleID, {
                direction: options.Direction ? options.Direction : 'vertical',  // 滑动方向，水平(horizontal)、垂直(vertical)
                observer: true,         // 修改swiper自己或子元素时，自动初始化swiper
                observeParents: true,   // 修改swiper的父元素时，自动初始化swiper
                speed: options.FullSpeedTime ? Number(options.FullSpeedTime) : 1400,    // slider自动滑动开始到结束的时间（单位ms）
                pagination: '.swiper-pagination-full-' + ModuleID, // 分页器
                paginationType: options.PaginationType == 2 || options.PaginationType == 3 ? 'custom' : 'bullets',  // 分页器样式类型
                paginationClickable: true,  // 点击分页器的指示点分页器会控制Swiper切换
                paginationHide: false,      // true时点击Swiper会隐藏/显示分页器
                keyboardControl: true,      // true时，能使用键盘方向键控制slide滑动
                mousewheelControl: true,    // true时，能使用鼠标滚轮控制slide滑动
                grabCursor: (window.CanDesign !== 'True'), // true时，鼠标覆盖Swiper时指针会变成手掌形状，拖动时指针会变成抓手形状
                noSwiping: (window.CanDesign === 'True'),  // true时，可以在slide上（或其他元素）增加类名'swiper-no-swiping'，使该slide无法拖动，希望文字被选中时可以考虑使用。该类名可通过noSwipingClass修改。
                onInit: function (swiper) {   // 初始化后执行
                    replaceSlideVideo($('#module_' + ModuleID + ' .ModuleFullItem.swiper-slide-active'));
                    // 当在编辑状态下，需与板块管理做切卡关联
                    if (window.CanDesign === "True") {
                        if (top.$('#moduleHelperFull').length > 0) {
                            top.$('#moduleHelperFull #sectorManagementBox .sectorManagementItem').removeClass('active')
                            top.$('#moduleHelperFull #sectorManagementBox .sectorManagementItem').eq(swiper.realIndex).addClass('active')
                        }
                    }
                    // 当不在编辑状态下，执行动画
                    initAnimation($('#module_' + ModuleID + ' .ModuleFullItem'), window.CanDesign !== "True" ? 'hidden' : 'visible');
                    // 当不在编辑状态下，执行动画
                    if (window.CanDesign !== "True") {
                        execAnimation($('#module_' + ModuleID + ' .ModuleFullItem.swiper-slide-active'));
                    }

                    $('#module_' + ModuleID + ' .swiper-slide-active').addClass('outAm');
                },
                paginationBulletRender: function (swiper, index, className) {   // 渲染分页器小点。这个参数允许完全自定义分页器的指示点。接受指示点索引和指示点类名作为参数。
                    var html = '<span class="' + className + '"></span>';
                    if (options.PaginationType == 1) {
                        html = '<p class="' + className + '">'
                        html += '<span class="' + className + '-graphic"></span>';
                        html += '<span  class="' + className + '-graphic-current"></span></p>';
                    } else if (options.PaginationType == 4) html = '<span class="' + className + '">' + (index < 9 ? '0' : '') + (index + 1) + '</span>';
                    return html;
                },
                paginationCustomRender: function (swiper, current, total) { // 自定义特殊类型分页器，当分页器类型设置为自定义时可用
                    if (options.PaginationType == 2) {
                        $('#module_' + ModuleID + ' .swiper-pagination-full .swiper-pagination-current').html((swiper.realIndex + 1));
                    } else if (options.PaginationType == 3) {
                        $('#module_' + ModuleID + ' .swiper-pagination-custom').removeClass('swiper-pagination-custom-active');
                        $('#module_' + ModuleID + ' .swiper-pagination-custom').eq((swiper.realIndex + 1)).addClass('swiper-pagination-custom-active');
                    }
                },
                onScroll: function (swiper) {
                    bindSwlidScroll(ModuleID, swiper.activeIndex > 0, !swiper.isEnd);
                },
                onTransitionStart: function (swiper) { // 过渡开始时触发
                    //滚屏-退出动画
                    $('#module_' + ModuleID + ' .outAm .ModuleItem').each(function () {
                        var jqobj = $(this);
                        var animate = $(jqobj).attr('out-effect');
                        var duration = $(jqobj).attr('out-duration');
                        var delay = $(jqobj).attr('out-delay');
                        var oldAm = $(jqobj).css("animation-name");
                        if (animate != null) {
                            $(jqobj).attr("animation", animate);
                            $(jqobj).css({ "animation-name": animate, "animation-duration": duration, "animation-delay": delay }).one(
                                'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function () {
                                    $(jqobj).attr("animation", oldAm);
                                    $(jqobj).css({ "animation-name": "none", "animation-duration": "none", "animation-delay": "none" });
                                }
                            );
                        }
                    });
                    $('#module_' + ModuleID + ' .ModuleFullItem').removeClass('outAm');
                    $('#module_' + ModuleID + ' .swiper-slide-active').addClass('outAm');

                    /**
                     * 滑动指示器显示方式
                     * 类型1，仅第一帧显示
                     * 类型2，到最后一帧前一直显示
                     */
                    if (options.ScrollShowType == 1) {
                        if (swiper.realIndex > 0) $('#module_' + ModuleID + ' .scroll-indicator').hide();
                        else $('#module_' + ModuleID + ' .scroll-indicator').show();
                    } else {
                        if (swiper.isEnd) {
                            $('#module_' + ModuleID + ' .scroll-indicator').hide()
                        }
                        else $('#module_' + ModuleID + ' .scroll-indicator').show();
                    }

                    $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn').removeClass('scrolltop')
                    $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn .arrow-down').removeClass('scrolltop')
                    $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-text').html('SCROLL')
                    if (swiper.isEnd) {
                        /**是否显示置顶 */
                        if (options.ScrollShowTop == 1) {
                            $('#module_' + ModuleID + ' .scroll-indicator').show();
                            if ($('#module_' + ModuleID + ' .scroll-indicator').hasClass('scroll-indicator-type-2')) {
                                $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn .arrow-down').addClass('scrolltop')
                            } else {
                                $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn').addClass('scrolltop')
                                $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-text').html('TOP')
                            }
                        } else {
                            $('#module_' + ModuleID + ' .scroll-indicator').hide()
                        }
                    }


                    // 当在编辑状态下，需与板块管理做切卡关联
                    if (window.CanDesign === "True") {
                        if (top.$('#moduleHelperFull').length > 0) {
                            top.$('#moduleHelperFull #sectorManagementBox .sectorManagementItem').removeClass('active')
                            top.$('#moduleHelperFull #sectorManagementBox .sectorManagementItem').eq(swiper.realIndex).addClass('active')
                        }
                    }
                    handleModuleGridFloat(swiper.realIndex);
                },
                onTransitionEnd: function (swiper) {   // 过渡结束时触发，人为中断过渡不会触发这个函数
                    replaceSlideVideo($('#module_' + ModuleID + ' .ModuleFullItem.swiper-slide-active'));
                    initFuncCommon($('#module_' + ModuleID + ' .ModuleFullItem.swiper-slide-active'));
                    // 当不在编辑状态下，执行动画
                    if (window.CanDesign !== "True") {
                        execAnimation($('#module_' + ModuleID + ' .ModuleFullItem.swiper-slide-active'));
                    }
                    bindSwlidScroll(ModuleID, swiper.activeIndex > 0, !swiper.isEnd);
                }
            });

            bindSwlidScroll(ModuleID, false, true);

            $('#module_' + ModuleID + ' .full-swiper-button-prev').off().on('click', function () {
                window['fullSwiper' + ModuleID].slidePrev();
                return false;
            });
            $('#module_' + ModuleID + ' .full-swiper-button-next').off().on('click', function () {
                window['fullSwiper' + ModuleID].slideNext();
                return false;
            });
            $('#module_' + ModuleID + ' .swiper-pagination-custom').off().on('click', function () {
                if ($(this).index() != window['fullSwiper' + ModuleID].realIndex) {
                    window['fullSwiper' + ModuleID].slideTo($(this).index(), 1400);
                    $('#module_' + ModuleID + ' .swiper-pagination-custom').removeClass('swiper-pagination-custom-active');
                    $('#module_' + ModuleID + ' .swiper-pagination-custom').eq(($(this).index() + 1)).addClass('swiper-pagination-custom-active');
                    // 当在编辑状态下，需与板块管理做切卡关联
                    if (window.CanDesign === "True") {
                        if (top.$('#moduleHelperFull').length > 0) {
                            top.$('#moduleHelperFull #sectorManagementBox .sectorManagementItem').removeClass('active')
                            top.$('#moduleHelperFull #sectorManagementBox .sectorManagementItem').eq(window['fullSwiper' + ModuleID].realIndex).addClass('active')
                        }
                    }
                    /**
                     * 滑动指示器显示方式
                     * 类型1，仅第一帧显示
                     * 类型2，到最后一帧前一直显示
                     */
                    if (options.ScrollShowType == 1) {
                        if (window['fullSwiper' + ModuleID].realIndex > 0) $('#module_' + ModuleID + ' .scroll-indicator').hide();
                        else $('#module_' + ModuleID + ' .scroll-indicator').show();
                    } else {
                        $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn').removeClass('scrolltop')
                        $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn .arrow-down').removeClass('scrolltop')
                        $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-text').html('SCROLL')
                        if (window['fullSwiper' + ModuleID].isEnd) {
                            $('#module_' + ModuleID + ' .scroll-indicator').hide()
                        }
                        else $('#module_' + ModuleID + ' .scroll-indicator').show();
                    }
                    $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn').removeClass('scrolltop')
                    $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn .arrow-down').removeClass('scrolltop')
                    $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-text').html('SCROLL')
                    if (window['fullSwiper' + ModuleID].isEnd) {
                        /**是否显示置顶 */
                        if (options.ScrollShowTop == 1) {
                            $('#module_' + ModuleID + ' .scroll-indicator').show();
                            if ($('#module_' + ModuleID + ' .scroll-indicator').hasClass('scroll-indicator-type-2')) {
                                $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn .arrow-down').addClass('scrolltop')
                            } else {
                                $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-btn').addClass('scrolltop')
                                $('#module_' + ModuleID + ' .scroll-indicator .scroll-indicator-text').html('TOP')
                            }
                        } else {
                            $('#module_' + ModuleID + ' .scroll-indicator').hide()
                        }
                    }
                    return false;
                }
            });
            $('#module_' + ModuleID + ' .scroll-indicator').off().on('click', function () {
                if ($(this).find('.scrolltop').length > 0) {
                    window['fullSwiper' + ModuleID].slideTo(0);
                } else {
                    window['fullSwiper' + ModuleID].slideNext();
                }
            });
        }
        $(window).off('resize.full' + ModuleID).on('resize.full' + ModuleID, function () {
            window['initFunc' + ModuleID]();
        });
        window['initFunc' + ModuleID]();
    });

    function bindSwlidScroll(ModuleID, isNotFirst, isNotEnd) {
        $('#module_' + ModuleID).off('scroll', '.ModuleFullItem');
        var curSlide = $('#module_' + ModuleID + ' .ModuleFullItem.swiper-slide-active');
        // 当前帧存在滚动条时，取消swiper滚轮及手势切换事件
        if (curSlide[0].scrollHeight > curSlide[0].clientHeight) {
            if (!curSlide.hasClass('slideOverflow')) {
                window['fullSwiper' + ModuleID].disableMousewheelControl();
                window['fullSwiper' + ModuleID].disableTouchControl();
            }
            curSlide.off().scroll(function () {
                // 当前帧不为最后一帧且滚动条到最下方，或不为第一帧且滚动条到顶端时，开启swiper滚轮及手势切换事件
                if (
                    (($(this)[0].scrollTop + $(this)[0].clientHeight + 5) >= $(this)[0].scrollHeight && isNotEnd)
                    || ($(this)[0].scrollTop <= 0 && isNotFirst)
                ) {
                    window['fullSwiper' + ModuleID].enableMousewheelControl();
                    window['fullSwiper' + ModuleID].enableTouchControl();
                }
            });
        }
    }

    // 调用当前帧内子模块的初始化方法
    function initFuncCommon($obj) {
        $obj.find('.ModuleItem').each(function () {
            var moduleid = ($(this).attr('id') || '').replace(/module_/i, '');
            var initFunc = window['initFunc' + moduleid];
            if (typeof initFunc == 'function') initFunc();
        });
    }

    // 初始化当前帧内需执行动画的子模块
    function initAnimation($obj, status) {
        status = status ? status : 'hidden'
        $obj.each(function () {
            if (!$(this).hasClass('swiper-slide-active')) {
                $(this).find('.ModuleItem').each(function () {
                    if ($(this).hasClass('wow')) $(this).css('visibility', status);
                });
            }
        });
    }

    // 执行当前帧内需执行动画的子模块
    function execAnimation($obj) {
        runAllAnimate($obj.find('.ModuleItem'));
        $obj.find('.ModuleItem').each(function () {
            var _this = $(this);
            if (_this.hasClass('wow')) {
                setTimeout(function () {
                    _this.css('visibility', 'visible');
                }, 20);
            }
        });
        initAnimation($obj.parent().find('.ModuleFullItem'), 'hidden');
    }

    // 替换当前帧视频背景链接
    function replaceSlideVideo(obj) {
        if (!$(obj).find('.fullBgVideo').hasClass('noBgVideo')) {
            var winWidth = $(window).width(),
                video = $(obj).find('.fullBgVideo .bgVideo');
            if (winWidth <= 769) {
                //手机端 使用图片背景 替换视频
                if (video.length > 0) {
                    var imageSrc = video.attr('imgsrc');
                    $(obj).css({ 'background-image': 'url("' + imageSrc + '")', });
                    video.attr('src', '');
                    $(obj).find('.fullBgVideo').hide();
                }
            } else {
                if (video.length > 0) {
                    video.prop('muted', 'muted');
                }
                if (!video.attr('src')) video.attr('src', video.attr('vsrc'));
                $(obj).css({ 'background-image': 'none', });
                $(obj).find('.fullBgVideo').show();
            }
        }
    }

    // 处理分栏置顶悬浮
    function handleModuleGridFloat(swiperIndex) {
        var grid = $('#HeaderZone .ModuleItem .GridCanFloat');
        //不在页头 或者不能悬浮时，并不为分栏模块
        if (grid.length < 1 && !/modulegrid/i.test(grid.attr('class'))) return false;

        grid = grid.closest('.ModuleItem');
        var moduleID = grid.attr('id').replace('module_', '');

        //导航的特殊处理
        var navFloat = grid.find('.ModuleNavGiant');
        if (navFloat.length > 0) {
            var navid = navFloat.closest('.ModuleItem');
            navid = navid.attr('id');
            //悬浮时 如果滚动页面 并且 二级菜单显示 要强制隐藏二级菜单
            if (navid != '') $('#' + navid + '.moduleNavFloatSubMenu').remove();
        }

        var floattype = grid.attr('floattype');
        var floattop = grid.attr('floattop');
        var floatbgtype = grid.attr('floatbgtype');
        var floatbgcolor = grid.attr('floatbgcolor');
        if (floattype == 1) {
            //到顶部的时候
            if (swiperIndex < 1) {
                grid.find('.BodyCenter.BodyCenter' + moduleID).removeAttr('style')
                if (window.CanDesign !== "True") {
                    grid.addClass("GridFloat")
                    grid.css("cssText", "top:" + floattop + "px !important;");
                } else {
                    grid.removeClass("GridFloat")
                    if (grid.hasClass('StaticModule')) grid.css('position', 'static');
                }
            } else {
                if (floatbgtype == 1) grid.find('.BodyCenter.BodyCenter' + moduleID).css('background-color', floatbgcolor)
                if (window.CanDesign !== "True") {
                    grid.css("cssText", "top:0px !important;");
                } else {
                    grid.addClass("GridFloat")
                    grid.css("cssText", "top:0px !important;");
                }
            }
        }
    }
}