function initModuleshare(moduleId, layout,LayoutSetting) {
    $(window).load(function(){
        loadShare()
    })
    if(LayoutSetting == '1'){
        $('#module_'+ moduleId +' .social-share .social-share-icon').css('display','block');
        $('#module_'+ moduleId +' .social-share .social-share-icon').addClass('mvertical')
    }
    $('#module_'+ moduleId +' .icon-default-WeChat').click(function(){
        $('#module_'+ moduleId +' .mshareMask').show();
        //如果是在微信中分享的话
        if(navigator.userAgent!=undefined && navigator.userAgent.toLowerCase().indexOf('micromessenger')>-1)
        {
            $('#module_'+ moduleId +' .share-wx-tips').show();
            return;
        }
        if($('#module_'+ moduleId +' .wechat-qrcode').hasClass('hasimg')){
            $('#module_'+ moduleId +' .wechat-qrcode').css("visibility","visible");
        }else{
            $('#module_'+ moduleId +' .share-wx-qrcode-img').css("visibility","visible")
            var qrurl = window.location.host + window.location.pathname
            var params = {text:window.location.protocol+'//'+qrurl,width:224,height:224};
            if($('#module_'+ moduleId +' .wx-qrcode').html() == "") $('#module_'+ moduleId +' .wx-qrcode').qrcode(params);
            if($(window).width()<786) createwxqrcode()
      }
    })
    $('#module_' + moduleId +' .mshareMask').click(function(){
        $('#module_'+ moduleId +' .mshareMask').hide()
        $('#module_'+ moduleId +' .share-wx-qrcode-img').css("visibility","hidden")
        $('#module_'+ moduleId +' .share-wx-tips').hide()
        $('#module_'+ moduleId +' .wechat-qrcode').css("visibility","hidden")
    })
    function createwxqrcode(){
        var wxqrcode = document.querySelector('#module_'+ moduleId +' .share-wx-qrcode-img');// 需要绘制的部分的 (原生）dom 对象 ，注意容器的宽度不要使用百分比，使用固定宽度，避免缩放问题

        var width = wxqrcode.offsetWidth + 60;  // 获取(原生）dom 宽度
        var height = wxqrcode.offsetHeight; // 获取(原生）dom 高
        var offsetTop = wxqrcode.offsetTop;  //元素距离顶部的偏移量

        var canvas = document.createElement('canvas');  //创建canvas 对象
        var context = canvas.getContext('2d');
        var scaleBy = 1;  //获取像素密度的方法 (也可以采用自定义缩放比例)
        canvas.width = width * scaleBy;   //这里 由于绘制的dom 为固定宽度，居中，所以没有偏移
        canvas.height = (height + offsetTop) * scaleBy;  // 注意高度问题，由于顶部有个距离所以要加上顶部的距离，解决图像高度偏移问题
        console.log( canvas)
        context.scale(scaleBy, scaleBy);
        var opts = {
            useCORS:true,
            allowTaint:true,//允许加载跨域的图片
            tainttest:true, //检测每张图片都已经加载完成
            scale:scaleBy, // 添加的scale 参数
            canvas:canvas, //自定义 canvas
            //logging: true, //日志开关
            width:width, //dom 原始宽度
            height:height //dom 原始高度
        };
        html2canvas(wxqrcode,opts).then(function (canvas) {
            var dataUrl = canvas.toDataURL();
            var newImg = document.createElement("img");
            newImg.src =  dataUrl;
            $('#module_'+ moduleId +' .wechat-qrcode').addClass('hasimg');
            $('#module_'+ moduleId +' .wechat-qrcode').html(newImg);
            $('#module_'+ moduleId +' .wechat-qrcode').css("visibility","visible");
            //$("#module_'+ moduleId +' .share-wx-qrcode-img").remove();
        })
    }
}
