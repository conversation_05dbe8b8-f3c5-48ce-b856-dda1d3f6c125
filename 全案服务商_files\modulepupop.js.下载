function initPupopGiant(ModuleID, options) {
	options = options || {};
	var moduleObj = $("#module_" + ModuleID);
	var timerShow = null;
	var timerHide = null;
	setcustomstyle(moduleObj)
	if ($('#HeaderZone').is(':hidden') && window.CanDesign != "True" && window.CanEditFront != "True") {
		/**
		 * 非编辑模式下特殊处理，判断是否头部隐藏，不然显示不了，头部没输出就先不管了
		 * 正常状态下，放到内容区就行了，但是在编辑状态下，子模块的showurl又不是ALL了，导致子模块
		 * 没跟着弹窗模块全站输出
		 */
		var module = moduleObj.clone();
		moduleObj.remove();
		module.appendTo('body');
		moduleObj = $("#module_" + ModuleID);
		initPupopOpen();
	} else {
		initPupopOpen();
	}

	function initPupopOpen() {
		if ($("#module_" + ModuleID).find('.pupopClose').length > 0) {
			$("#module_" + ModuleID).find('.pupopClose').off().on('click', function () {
				$("#module_" + ModuleID).hide();
				closeFunc()
				setTimeout(function () {
					$('#moduleHelper').hide();
				}, 10);
				// 判断是否设置自动打开
				if (options.OpenMode && options.OpenMode > 0 && (window.CanDesign != "True" && window.CanEditFront != "True")) {
					setTimingOpen();
				}
			})
		}

		// 如果是自动弹出类型进入处理
		if (options.OpenMode && Number(options.OpenMode) == 1 && (window.CanDesign != "True" && window.CanEditFront != "True")) {
			var sitePupopTiming = getCookie('SitePupopTiming' + ModuleID);
			// 判断是否第一次进入，或状态为重置更新，或当前时间大于等于设定时间则设置定时打开
			if (!sitePupopTiming || Number(options.SettingUpdate) > 0 || Number(new Date().getTime()) >= Number(sitePupopTiming)) {
				setTimeout(function () {
					moduleObj.show();
					if (Number(options.AutoOffTime) > 0) {
						setTimeout(function () {
							moduleObj.hide();
							setTimingOpen();
						}, Number(options.AutoOffTime) * 1000);
					}
				}, Number(options.DelayedTime) * 1000);
			} else if (sitePupopTiming) {
				// 当已打开过一次并且当前时间未到设置间隔时间则开启间隔时间与当前时间的差的定时
				setTimeout(function () {
					moduleObj.show();
					if (Number(options.AutoOffTime) > 0) {
						setTimeout(function () {
							moduleObj.hide();
							setTimingOpen();
						}, Number(options.AutoOffTime) * 1000);
					}
				}, Number(sitePupopTiming) - Number(new Date().getTime()));
			}
			setTimeout(function () {
				window["initFunc"+ModuleID]()
			}, 10)

		}
	}

	function setTimingOpen() {
		var delayedTimeMs = 0;
		if (timerShow != null) window.clearTimeout(timerShow);
		if (timerHide != null) window.clearTimeout(timerHide);
		switch (Number(options.FrequencyUnit)) {
			case 0:
				delayedTimeMs = Number(options.Frequency) * 1000;	// 秒
				break;
			case 1:
				delayedTimeMs = Number(options.Frequency) * 60 * 1000;	// 分
				break;
			case 2:
				delayedTimeMs = Number(options.Frequency) * 60 * 60 * 1000;	// 时
				break;
			case 3:
				delayedTimeMs = Number(options.Frequency) * 60 * 60 * 24 * 1000;	// 天
				break;
			case 4:
				delayedTimeMs = Number(options.Frequency) * 60 * 60 * 24 * 30 * 1000;	// 月
				break;
			default:
				delayedTimeMs = 0;
				break;
		}
		if (delayedTimeMs > 0) {
			SetCookie('SitePupopTiming' + ModuleID, Number(new Date().getTime()) + Number(delayedTimeMs));
			timerShow = window.setTimeout(function () {
				moduleObj.show();
				if (Number(options.AutoOffTime) > 0) {
					timerHide = setTimeout(function () {
						moduleObj.hide();
						setTimingOpen();
					}, Number(options.AutoOffTime) * 1000);
				}
			}, delayedTimeMs);
		}
	}
	window["initFunc"+ModuleID] = function () {
		var subModuleIDs = moduleObj.find('.ModuleItem')
		$(subModuleIDs).each(function (index, element) {
			var moduleid = $(element).attr('id').replace('module_', '')
			var moduletype = $(element).attr('moduletype')
			//if (moduletype == 'ModuleSlideV2Giant' && $(element).find('.layout-107').length > 0) return;
			var initFunc = window['initFunc' + moduleid];
			var initSwiperFuncSiteGalleryByMobile = window['initSwiperFunc' + moduleid + 'SiteGalleryByMobile'];
			var news121Multiple = window['news121Multiple' + moduleid];
			var initSwiperFunc = window["initSwiperFunc" + moduleid]
			if (typeof initSwiperFunc == 'function') {
				initSwiperFunc()
			}
			if (typeof initFunc == 'function') {
				initFunc()
			}
			if (typeof initSwiperFuncSiteGalleryByMobile == 'function') {
				initSwiperFuncSiteGalleryByMobile();
			}
			if (typeof news121Multiple == 'function') {
				news121Multiple()
			}
		});
	}
	function closeFunc() {
		var subModuleIDs = moduleObj.find('.ModuleItem')
		$(subModuleIDs).each(function (index, element) {
			var moduleid = $(element).attr('id').replace('module_', '')
			var moduletype = $(element).attr('moduletype')
			if ($(element).find('.ModuleVideoGiant').length > 0) {
				if ($(element).find('iframe').length > 0) {
					var osrc = $(element).find('iframe').attr('src')
					$(element).find('iframe').attr('_src', osrc)
					$(element).find('iframe').attr('src', '')
					setTimeout(function () {
						$(element).find('iframe').attr('src', osrc)
					}, 10)
				} else {
					var video = $(element).find('video')[0];
					video.pause();
				}
			}
		})
	}

	function setcustomstyle(moduleObj) {
		if (window.CanDesign == "True" && window.CanEditFront == "True") {
			if (moduleObj.find('.ModulePupopContainer').attr('openmode') == 2) {
				moduleObj.find('.ModulePupopContainer').css('cssText','bottom:unset !importatn;right: unset !important;top:calc(50% + 0px) !important;left: calc(50% + 0px) !important;transform: translate(-50%, -50%) !important')
				moduleObj.find('.ModulePupopGiant').css('cssText','background-color:rgba(0, 0, 0, .6) !important')
			}

		}
	}
}