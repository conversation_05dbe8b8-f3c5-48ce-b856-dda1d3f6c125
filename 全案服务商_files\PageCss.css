/* cache file /mnt/yzproc/YZ-Site/caches/csscache/7/78819/78819_1211927_1_.cache exists */
/*md5:d1d9952a6149a79887ba993acb3f40fd*/
/*time: 2025-06-06 13:32:15*/
/*url:www.deemchina.com/*/
#pagebody.siteStyle .BodyContainer{
background:none;background-color:#141414;
background-repeat:no-repeat ;
background-position:center;
background-size:auto;
background-Attachment:scroll;
}
.ModuleGridContainer[gridswidthmode="2"]{max-width:1640px;width:auto;}
.BodyMain2Zone{display:none;}
.BodyMain3Zone{display:none;}
.BodyMain4Zone{display:none;}
@media only screen and (max-width: 767px){
	.ModuleOnLineServiceGiant.layout-107{display: none;}
}
@media only screen and (min-width: 768px){
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-hidden{
	    display: none;
	}
	.ModuleOnLineServiceGiant.layout-107 .qrcodetips{overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		word-break: break-all;
		-webkit-box-orient: vertical;text-align: center; margin-top:4px;color:#666}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant{position: absolute; top: 0px;border: 1px solid #e6e6e6;border-radius: 8px; background-color: #fff;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container.pos-right .online-service-giant{right: 0; margin-right: 52px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container.pos-left .online-service-giant{left: 0; margin-left: 52px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-IM{width: 191px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-contact{min-width: 132px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-qrcode{width: 120px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{position:relative;height:50px;width: 50px;border-left: 1px solid #e6e6e6;border-right: 1px solid #e6e6e6;text-align: center; line-height: 50px; background-color: #fff;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container div.online-service-giant-btn:before{content: '';position: absolute;top: 0;left: 0;transform: translateX(11px); display: block; width: 56%;height: 1px; background: #e6e6e6;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container div.online-service-giant-btn:nth-child(2):before,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container div.online-service-giant-btn:nth-last-child(2):before{height: 0;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .online-service-giant-IM,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .online-service-giant-contact .contact-item{padding: 11px 5px 11px 16px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .online-service-giant-qrcode .qrcode-content{position: relative;padding: 10px;text-align: center;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .online-service-giant-qrcode div.qrcode-content+.qrcode-content:before{content: '';position: absolute;top: 0;left: 0;transform: translateX(10px); display: block; width: 100px;height: 1px; background: #e6e6e6;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .online-service-giant-contact div.contact-item+.contact-item{border-top: 1px solid #e6e6e6;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .IM-list .IM-item{width: 48%; display: inline-block;vertical-align: top;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .IM-list .IM-item .IM-item-link i{vertical-align: middle;margin-right: 5px;}

	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .service-title,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .contact-item-name{color: #000; font-size: 14px;font-family: 'Microsoft YaHei';margin-bottom: 6px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-IM .IM-item a,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .contact-item-content{color: #666; font-size: 12px;font-family: 'Microsoft YaHei';}

	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-qrcode .qrcode-content .qrcode-img{width: 98px;height: 98px;}

	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-child(2){border-top: 1px solid #e6e6e6;border-radius: 8px 8px 0 0; }
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(3){border-radius: 0 0 8px 8px;    border-bottom: 1px solid #e6e6e6; }
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){margin-top: 4px;border: 1px solid #e6e6e6;border-radius: 8px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn i.iconfont{font-size: 23px;color: #aeaeae;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-Contacts-btn i.iconfont,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-QrCode-btn i.iconfont{font-size: 22px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-IM-btn i.iconfont{font-size: 32px;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-ToTop-btn i.iconfont{font-weight: bold;}
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant .IM-list .IM-item .IM-item-link i,
	.ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn.service-active i.iconfont{color:#1e88e5;}
	.ModuleOnLineServiceGiant.layout-107 .IM-item-link{
		font-size: 14px;
		width: 80px;
		overflow: hidden;
		display: block;
	}
}
.module_672014114 .ModuleHead .HeadCenter{float:none;}
#module_672014114 {
padding:0px;
}
#module_672014114 {
position:absolute;
z-index:4001;
top:260px;
left:0px;
width:100%;
height:154px;
}
#module_672014114 .ModuleHead672014114 {
display:none;
}
#module_672014114 .BodyCenter.BodyCenter672014114 {
background:none;background-color:rgba(0,0,0,0);
}
#module_672014114 >.module_672014114 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_672014114 >.module_672014114{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_672014114 .BodyCenter.BodyCenter672014114 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_672014114 .BodyCenter.BodyCenter672014114 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_672014114 >.module_672014114 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_672014114 >.module_672014114 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_672014114 >.module_672014114 {box-shadow: 0px 0px 0px 0px #ccc}
#module_672014114:hover {
border:none;
}
#module_672014114:hover >.module_672014114 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-child(2){border-top-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-child(2){border-top-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-child(2){border-top-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{border-right-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{border-right-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{border-right-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(3){border-bottom-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(3){border-bottom-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(3){border-bottom-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{border-left-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{border-left-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn{border-left-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-top-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-top-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-top-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-right-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-right-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-right-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-bottom-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-bottom-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-bottom-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-left-color:#e1e1e1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-left-style:solid;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-btn:nth-last-child(2){border-left-width:1px;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container div.online-service-giant-btn::before{background-color:#d1d1d1;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-btn i{color:#fff;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-btn{background-color:#f10215;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-ToTop-btn i{color:#fff;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-ToTop-btn{background-color:#f10215;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-btn.service-active i{color:#fff;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-btn.service-active{background-color:#f10215;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-ToTop-btn.service-active i{color:#fff;}
#module_672014114 .ModuleOnLineServiceGiant.layout-107 .online-service-giant-container .online-service-giant-ToTop-btn.service-active{background-color:#f10215;}
#FreeMainZone{height:414px}
.ModulePupopGiant.layout-101 {height: 100%; background-color: rgba(0, 0, 0, .6);}
.ModulePupopGiant.layout-101 .row {margin-right: 0px; margin-left: 0px;}
.ModulePupopGiant.layout-101 .ModuleSubContainer {width: 100%; position: relative;}
.ModulePupopGiant.layout-101 .ModuleSubContainer .addnewhelper {white-space: unset;}
.ModulePupopGiant.layout-101 .ModulePupopContainer {
    width:100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    margin: auto;
    padding-right: 0;
    padding-left: 0;
}
.ModulePupopGiant.layout-101 .IsAdvertisement {position: fixed;}
.ModulePupopGiant.layout-101 .IsAdvertisement .ModulePupopContainer {
    position: static;
    top: 0 !important;
    left: 0 !important;
    transform: unset !important;
}
.ModulePupopGiant.layout-101 .pupopClose {
    position: absolute;
    z-index: 99999;
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 0;
    text-align: center;
    cursor: pointer;
    overflow: hidden;
}
.ModulePupopGiant.layout-101 .pupopClose .iconfont {font-size: 30px; color: #fff; position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);}
.ModulePupopGiant.layout-101 .ModulePupopContainer .ModuleSubPupopBox {height: auto; overflow-y: auto;}
.ModulePupopGiant.layout-101 .ModulePuponArrow { position: absolute;
    left: 50%;
    transform: translatex(-50%);
    border: 9px solid transparent;
    width: 0;
    height: 0;
}
.ModulePupopGiant.layout-101 .ModulePuponArrow.PosTop {
    border-bottom-color: #e8e8e8;
    border-bottom-width: 12px;
    top:-20px;
}

.ModulePupopGiant.layout-101 .ModulePuponArrow.PosBottom {
    border-top-color: #e8e8e8;
    border-top-width: 12px;
    bottom:-20px;
}

@media only screen and (max-width: 767px) {
    .ModulePupopGiant.layout-101 .ModuleSubContainer {display: block;}
    .ModulePupopGiant.layout-101 .IsAdvertisement .ModulePupopContainer {max-width: 100vw;}
}.module_599627630 .ModuleHead .HeadCenter{float:none;}
#module_599627630 {
padding:0px;
}
#module_599627630 {
position:fixed;
z-index:100000;
top:0px;
left:0px;
width:100%;
height: auto;
}
#module_599627630 .ModuleHead599627630 {
display:none;
}
#module_599627630 .BodyCenter.BodyCenter599627630 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627630 >.module_599627630 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627630 >.module_599627630{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627630 .BodyCenter.BodyCenter599627630 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627630 .BodyCenter.BodyCenter599627630 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627630 >.module_599627630 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627630 >.module_599627630 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627630 >.module_599627630 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627630:hover {
border:none;
}
#module_599627630:hover >.module_599627630 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627630 .ModulePupopContainer{top:calc(50% + 0px);left:calc(50% + 0px);transform:translate(-50%, -50%);} #module_599627630 .IsAdvertisement{top:calc(50% + 0px);left:calc(50% + 0px);transform:translate(-50%, -50%);}
#module_599627630 .ModulePupopContainer{background-color:rgba(0,0,0,0);}
#module_599627630 .ModulePupopContainer{background-image:none;}
#module_599627630 .ModulePupopContainer{background-repeat:no-repeat;}
#module_599627630 .ModulePupopContainer{background-position:0% 0%;}
#module_599627630 .ModulePupopContainer{background-attachment:scroll;}
#module_599627630 .ModulePupopContainer{}
#module_599627630 .ModulePupopContainer{padding-top:0px;}
#module_599627630 .ModulePupopContainer{padding-bottom:0px;}
#module_599627630 .ModulePupopContainer{padding-left:0px;}
#module_599627630 .ModulePupopContainer{padding-right:0px;}
#module_599627630 .ModulePupopContainer{border-top-color:#e1e1e1;}
#module_599627630 .ModulePupopContainer{border-top-style:solid;}
#module_599627630 .ModulePupopContainer{border-top-width:0px;}
#module_599627630 .ModulePupopContainer{border-bottom-color:#e1e1e1;}
#module_599627630 .ModulePupopContainer{border-bottom-style:solid;}
#module_599627630 .ModulePupopContainer{border-bottom-width:0px;}
#module_599627630 .ModulePupopContainer{border-left-color:#e1e1e1;}
#module_599627630 .ModulePupopContainer{border-left-style:solid;}
#module_599627630 .ModulePupopContainer{border-left-width:0px;}
#module_599627630 .ModulePupopContainer{border-right-color:#e1e1e1;}
#module_599627630 .ModulePupopContainer{border-right-style:solid;}
#module_599627630 .ModulePupopContainer{border-right-width:0px;}
#module_599627630 .ModulePupopContainer{border-top-left-radius:0px;}
#module_599627630 .ModulePupopContainer{border-top-right-radius:0px;}
#module_599627630 .ModulePupopContainer{border-bottom-left-radius:0px;}
#module_599627630 .ModulePupopContainer{border-bottom-right-radius:0px;}
#module_599627630 .ModulePupopContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627630 .pupopClose{top:-30px;right:-30px;left:auto;transform:none;}
#module_599627630 .pupopClose{width:calc(18px + 3px); height:calc(18px + 3px);}
#module_599627630 .pupopClose .iconfont{font-size:16px;}
#module_599627630 .pupopClose .iconfont{color:rgba(255, 255, 255, 1);}
#module_599627630 .pupopClose{background-color:rgba(255, 255, 255, 0);}
#module_599627630 .pupopClose{background-image:none;}
#module_599627630 .pupopClose{background-repeat:no-repeat;}
#module_599627630 .pupopClose{background-position:0% 0%;}
#module_599627630 .pupopClose{background-attachment:scroll;}
#module_599627630 .pupopClose{}
#module_599627630 .pupopClose{border-top-color:#e1e1e1;}
#module_599627630 .pupopClose{border-top-style:solid;}
#module_599627630 .pupopClose{border-top-width:0px;}
#module_599627630 .pupopClose{border-bottom-color:#e1e1e1;}
#module_599627630 .pupopClose{border-bottom-style:solid;}
#module_599627630 .pupopClose{border-bottom-width:0px;}
#module_599627630 .pupopClose{border-left-color:#e1e1e1;}
#module_599627630 .pupopClose{border-left-style:solid;}
#module_599627630 .pupopClose{border-left-width:0px;}
#module_599627630 .pupopClose{border-right-color:#e1e1e1;}
#module_599627630 .pupopClose{border-right-style:solid;}
#module_599627630 .pupopClose{border-right-width:0px;}
#module_599627630 .pupopClose{border-top-left-radius:0px;}
#module_599627630 .pupopClose{border-top-right-radius:0px;}
#module_599627630 .pupopClose{border-bottom-left-radius:0px;}
#module_599627630 .pupopClose{border-bottom-right-radius:0px;}
#module_599627630 .ModulePupopGiant{background-color:rgba(0, 0, 0, 0.58);}
#module_599627630 .ModulePuponArrow.PosTop {border-bottom-color:;} #module_599627630 .ModulePuponArrow.PosBottom {border-top-color:;}
#module_599627630{position: fixed !important; top: 0 !important; left: 0 !important; z-index: 100000 !important; display: none;width: 100% !important; height: 100% !important;}.ModulePupopContainer.ModulePupopContainer599627630 {max-width:1200px;}
.IsAdvertisement .ModulePupopContainer.ModulePupopContainer599627630 {width:1200px;}
#Sub599627630_1.ModuleSubPupopBox {height: auto !important; max-height: calc(100vh - 100px);}
/*视频模块*/
.ModuleVideoGiant.layout-101 video{ display: block;}
.ModuleVideoGiant.layout-101 .videogiant-container{position: relative; text-align:center;}
.ModuleVideoGiant.layout-101 .videogiant-container .videoWrapper{height: auto;}
.ModuleVideoGiant.layout-101 .iframeBox .videoWrapper{padding-bottom: 56%;}
.ModuleVideoGiant.layout-101 .videogiant-container iframe,
.ModuleVideoGiant.layout-101 .videogiant-container embed,
.ModuleVideoGiant.layout-101 .videogiant-container object{ position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
.ModuleVideoGiant.layout-101 .videoPlayBtn{
    position: absolute;
    width: 60px;
    height: 60px;
    background: url('/skinp/modules/ModuleVideoGiant/image/vbg.png') -116px -196px no-repeat;
    top: 50%;
    left: 50%;
    margin-top: -30px;
    margin-left: -30px;
    z-index: 11;
    -webkit-transition: all .6s cubic-bezier(.215,.61,.355,1) 0s;
    transition: all .6s cubic-bezier(.215,.61,.355,1) 0s;
    visibility: visible;
    cursor: pointer;
}
.ModuleVideoGiant.layout-101 .videogiant-container:hover .videoPlayBtn{
    transform: scale(1.4);
    -webkit-transform:scale(1.4);
    -moz-transform:scale(1.4);
    -o-transform:scale(1.4);
}
.ModuleVideoGiant.layout-101 .videoCoverPic{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    cursor: pointer;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    /* background-color: #fff; */
    -webkit-transition: all .46s ease;
    transition: all .46s ease;
}
.ModuleVideoGiant.layout-101 .playModeBox {opacity: 0;}
/*兼容ios、safari border-radius*/
.ModuleVideoGiant.layout-101{transform:rotate(0);}.module_599627675 .ModuleHead .HeadCenter{float:none;}
#module_599627675 {
padding:0px;
}
#module_599627675 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627675 .ModuleHead599627675 {
display:none;
}
#module_599627675 .BodyCenter.BodyCenter599627675 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627675 >.module_599627675 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627675 >.module_599627675{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627675 .BodyCenter.BodyCenter599627675 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627675 .BodyCenter.BodyCenter599627675 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627675 >.module_599627675 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627675 >.module_599627675 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627675 >.module_599627675 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627675:hover {
border:none;
}
#module_599627675:hover >.module_599627675 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627675 .videoTitle{font-size:14px;}
#module_599627675 .videoTitle{font-family:微软雅黑,Microsoft YaHei;}
#module_599627675 .videoTitle{color:#333;}
#module_599627675 .videoTitle{font-weight:bold;}
#module_599627675 .videoTitle{text-decoration:none;}
#module_599627675 .videoTitle{font-style:normal;}
#module_599627675 .videoTitle{text-align:left;}
#module_599627675 .videoTitle{margin-top:8px;}
#module_599627675 .videoTitle{margin-bottom:2px;}
#module_599627675 .videoTitle{margin-left:0px;}
#module_599627675 .videoTitle{margin-right:0px;}
#module_599627675 .videoDescrib{font-size:13px;}
#module_599627675 .videoDescrib{font-family:微软雅黑,Microsoft YaHei;}
#module_599627675 .videoDescrib{color:#999;}
#module_599627675 .videoDescrib{font-weight:normal;}
#module_599627675 .videoDescrib{text-decoration:none;}
#module_599627675 .videoDescrib{font-style:normal;}
#module_599627675 .videoDescrib{text-align:left;}
#module_599627675 .videoBox:hover .videoTitle{font-size:14px;}
#module_599627675 .videoBox:hover .videoTitle{font-family:微软雅黑,Microsoft YaHei;}
#module_599627675 .videoBox:hover .videoTitle{color:#333;}
#module_599627675 .videoBox:hover .videoTitle{font-weight:bold;}
#module_599627675 .videoBox:hover .videoTitle{text-decoration:none;}
#module_599627675 .videoBox:hover .videoTitle{font-style:normal;}
#module_599627675 .videoBox:hover .videoDescrib{font-size:12px;}
#module_599627675 .videoBox:hover .videoDescrib{font-family:微软雅黑,Microsoft YaHei;}
#module_599627675 .videoBox:hover .videoDescrib{color:#999;}
#module_599627675 .videoBox:hover .videoDescrib{font-weight:normal;}
#module_599627675 .videoBox:hover .videoDescrib{text-decoration:none;}
#module_599627675 .videoBox:hover .videoDescrib{font-style:normal;}

.ModuleGridGiant.layout-101 .ModuleGridItem {
    padding: 0;
    min-height: 0.1px;
}

.ModuleGridGiant.layout-101 .row {
    margin-right: 0px;
    margin-left: 0px;
}

.ModuleGridGiant.layout-101 .ModuleGridCol {
    padding: 0px;
}

.ModuleGridGiant.layout-101 .ModuleGridContainer {
    margin-right: auto;
    margin-left: auto;
    padding-right: 0;
    padding-left: 0;
}
.ModuleGridGiant.layout-101{height: 100%;position: relative;}
.ModuleGridGiant.layout-101 .ModuleSubContainer{position: relative;}
.ModuleGridGiant.layout-101 .gridBgVideo{display: flex; position: absolute;top: 0;left: 0;width:100%;z-index: 0;}
.ModuleGridGiant.layout-101 .gridBgVideo .bgVideoMask{position: absolute;top: 0;left: 0;right: 0;bottom: 0;background-color: rgb(0,0,0);}
.ModuleGridGiant.layout-101 .gridBgVideo .bgVideo{width: 100%; height: 100%; object-fit: cover; object-position: center center;}
.ModuleGridGiant.layout-101 .gridBgVideo.noBgVideo{display: none;}

@media only screen and (max-width: 767px) { 
    .ModuleGridGiant.layout-101 .ModuleGridItem {
        height: 100% !important;
    }
}.module_599627615 .ModuleHead .HeadCenter{float:none;}
#module_599627615 {
padding:0px;
}
#module_599627615 {
position:static;
z-index:4000;
top:0px;
left:0px;
width:100%;
height: auto;
}
#module_599627615 .ModuleHead599627615 {
display:none;
}
#module_599627615 .BodyCenter.BodyCenter599627615 {
background:none;background-color:rgb(15, 36, 62);
}
#module_599627615 >.module_599627615 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:rgba(12, 12, 12, 0.79);
border-bottom-width:1px;
}
#module_599627615 >.module_599627615{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627615 .BodyCenter.BodyCenter599627615 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627615 .BodyCenter.BodyCenter599627615 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627615 >.module_599627615 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627615 >.module_599627615 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627615 >.module_599627615 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627615:hover {
border:none;
}
#module_599627615:hover >.module_599627615 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627615 .ModuleSubContainer{background-color:transparent;}
#module_599627615 .ModuleSubContainer{background-image:none;}
#module_599627615 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627615 .ModuleSubContainer{background-position:0% 0%;}
#module_599627615 .ModuleSubContainer{background-attachment:scroll;}
#module_599627615 .ModuleSubContainer{}
#module_599627615 {!bgVideo!}{bgVideoUrl:}
#module_599627615 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627615 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627615 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627615 {}
.ModuleImageGiant.layout-101 .BodyCenter{
	text-align:center;
}
.ModuleImageGiant.layout-101 img{
	margin: auto;
    max-width: 100%;
	border: none;
}

.module_599627674 .ModuleHead .HeadCenter{float:none;}
#module_599627674 {
padding:0px;
}
#module_599627674 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627674 .ModuleHead599627674 {
display:none;
}
#module_599627674 .BodyCenter.BodyCenter599627674 {
background:none;background-color:rgb(15, 36, 62);
}
#module_599627674 >.module_599627674 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627674 >.module_599627674{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627674 .BodyCenter.BodyCenter599627674 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627674 .BodyCenter.BodyCenter599627674 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627674 >.module_599627674 {
margin-top:3.4146%;
margin-left:5.3659%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627674 >.module_599627674 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627674 >.module_599627674 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627674:hover {
border:none;
}
#module_599627674:hover >.module_599627674 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627674 img{border-top-color:transparent;}
#module_599627674 img{border-top-style:none;}
#module_599627674 img{border-top-width:0px;}
#module_599627674 img{border-right-color:transparent;}
#module_599627674 img{border-right-style:none;}
#module_599627674 img{border-right-width:0px;}
#module_599627674 img{border-bottom-color:transparent;}
#module_599627674 img{border-bottom-style:none;}
#module_599627674 img{border-bottom-width:0px;}
#module_599627674 img{border-left-color:transparent;}
#module_599627674 img{border-left-style:none;}
#module_599627674 img{border-left-width:0px;}
#module_599627674 img{border-top-left-radius:0px;}
#module_599627674 img{border-top-right-radius:0px;}
#module_599627674 img{border-bottom-left-radius:0px;}
#module_599627674 img{border-bottom-right-radius:0px;}
#module_599627674 .BodyCenter{text-align: center;}

.ModuleNavGiant.layout-105 .main-nav-content {
  position: relative;
  width: 100%;
  height: auto;
  margin: 0 auto;
  line-height: 0;
  overflow:hidden;
}
.ModuleNavGiant.layout-105 .aroundMune{
   position: absolute;
   display: flex;
   align-items: center;
   right: 0px;
   padding:0 10px;
   height: 100%;
   z-index: 5;
   transform: rotateY(90deg);
}
.ModuleNavGiant.layout-105 .aroundMune.active{
  transform: rotateY(0deg);
}
.ModuleNavGiant.layout-105 .moveMenuRight, .ModuleNavGiant.layout-105 .moveMenuLeft{
   display: inline-block;
   width: 36px;
   height: 36px;
   line-height: 36px;
   font-size: 16px;
   color:#666;
   text-align: center;
   border:1px solid rgba(0,0,0,.1);
   border-radius: 5px;
   cursor: pointer;
}
.ModuleNavGiant.layout-105 .main-nav-content .moveMenuRight{
  background-color: rgba(238,238,238,.6);
}
.ModuleNavGiant.layout-105 .main-nav-content .moveMenuLeft{
   display:none;
   background-color: rgba(238,238,238,.6);
   margin-right: 10px

}
.ModuleNavGiant.layout-105 .moveMenuRight:hover, .ModuleNavGiant.layout-105 .moveMenuLeft:hover{
   color:#fff;
   background-color:rgba(204,204,204,.6);
}
.ModuleNavGiant.layout-105 .main-nav-content >.blank-solve{
  width: auto;
  display: inline-block;
  transition: all .4s;
  position: relative;
  left: 0;
}
.ModuleNavGiant.layout-105 .main-nav-item-group {
  box-sizing: border-box;
  -moz-box-sizing:border-box; 
 -webkit-box-sizing:border-box;
  height: 80px;
  line-height: 80px;
  text-align: center;
  float: left;
}
.ModuleNavGiant.layout-105 .main-nav-item {
  box-sizing: content-box;
  -moz-box-sizing:content-box; 
 -webkit-box-sizing:content-box;
  padding: 0 25px;
  color: #000000;
  display: inline-block;
  position: relative;
  z-index: 2;
}
.ModuleNavGiant.layout-105 .main-nav-item::before {
  content: '';
  position: absolute;
  width: 0%;
  height: 50%;
  left: 50%;
  transform: translateY(-50%);
   -ms-transform:translateY(-50%);
  -moz-transform:translateY(-50%); 
  -webkit-transform:translateY(-50%);
  -o-transform:translateY(-50%); 
  top: 50%;
  z-index: -1;
  opacity: 0;
  transition: all 0.5s;
  -moz-transition: all 0.5s;
  -webkit-transition: all 0.5s; 
  -o-transition: all 0.5s;
}

.ModuleNavGiant.layout-105 .main-nav-item-hover {
  color: #fff;
}
.ModuleNavGiant.layout-105 .main-nav-item-hover::before {
  width: 100%;
  left: 0;
  opacity: 1;
}
.ModuleNavGiant.layout-105 .sub-nav-item-group {
  z-index: 3;
  height: 200px;
  bottom: -220px;
  left: -49px;
  width: 192px;
  padding-top: 20px;
  text-align: center;
  display: none;
}
.ModuleNavGiant.layout-105 .sub-nav-item-box {
  width: 100%;
  position: relative;
}
/*
.ModuleNavGiant.layout-105 .sub-nav-item-box ::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  margin-left: -5px;
  width: 0px;
  height: 0px;
  border: 10px solid transparent;
  border-bottom-color: #fff;
}
*/
.ModuleNavGiant.layout-105 .sub-nav-item-group{
	position: relative;
}
.ModuleNavGiant.layout-105 .arrow{
	top:13px;
	width:15px;
	height:15px;
	background:#fff;
	position: absolute;
	left: 50%;
  margin-left: -7.5px;
	z-index:-1;
	transform:rotate(45deg);
  -ms-transform:rotate(45deg);
  -moz-transform:rotate(45deg); 
  -webkit-transform:rotate(45deg);
  -o-transform:rotate(45deg); 
	transition: all 0.5s;
  -moz-transition: all 0.5s;
  -webkit-transition: all 0.5s; 
  -o-transition: all 0.5s;
}

.ModuleNavGiant.layout-105 .sub-nav-item:first-child {
  border-top: none!important;
}
.ModuleNavGiant.layout-105 .sub-nav-item:last-child {
  border-bottom: none!important;
}
.ModuleNavGiant.layout-105 .sub-nav-item {
  display: block;
  transition: all 0.5s;
  -moz-transition: all 0.5s;
  -webkit-transition: all 0.5s; 
  -o-transition: all 0.5s;
  font-size: 12px;
  padding-left: 0;
  background-color: #fff;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
}
.ModuleNavGiant.layout-105 .sub-nav-item .inner{
  display: block;
  height: 100%;
  transition: all 0.5s;
  -moz-transition: all 0.5s;
  -webkit-transition: all 0.5s; 
  -o-transition: all 0.5s;
  }
.ModuleNavGiant.layout-105 .sub-nav-item:hover {
  background-color: #f7f7f7;
}
.ModuleNavGiant.layout-105 .sub-nav-item:hover .inner {
    padding-left: 10px;
}
.ModuleNavGiant.layout-105 .sub-nav-item-group.current {
  display: block;
}
.ModuleNavGiant.layout-105 .main-nav-item-group.current .main-nav-item {
  color: #fff;
}
.ModuleNavGiant.layout-105 .main-nav-item-group.current .main-nav-item::after {
  content: '';
  width: 100%;
  left: 0;
  top: 20px;
  height: 40px;
  position: absolute;
  z-index: -1;
  background: #e91e63;
  border-radius: 20px;
}
.ModuleNavGiant.layout-105 .clearfix:after{content:'.';clear: both;display:block;width:0;height:0;overflow: hidden;visibility: hidden;}
.ModuleNavGiant.layout-105 .menu{
    font-size: 12px;
    opacity: 0.6;
    float: right;
    margin-left: 8px;
    margin-top: 1px;
    transform:rotate(0deg);
    transition: all .3s linear;
    -webkit-transform:rotate(0deg);
    -webkit-transition: all .3s linear;
}
.ModuleNavGiant.layout-105 .menuUp{
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    margin-top: -1px;
}
.ModuleNavGiant.layout-105 .sub-nav-item-group.sub-menu{
    display: none !important;
}.module_599627617 .ModuleHead .HeadCenter{float:none;}
#module_599627617 {
padding:0px;
}
#module_599627617 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627617 .ModuleHead599627617 {
display:none;
}
#module_599627617 .BodyCenter.BodyCenter599627617 {
background:none;background-color:rgb(15, 36, 62);
}
#module_599627617 >.module_599627617 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:rgb(38, 38, 38);
border-left-width:1px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627617 >.module_599627617{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627617 .BodyCenter.BodyCenter599627617 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627617 .BodyCenter.BodyCenter599627617 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627617 >.module_599627617 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627617 >.module_599627617 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627617 >.module_599627617 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627617:hover {
border:none;
}
#module_599627617:hover >.module_599627617 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627617 .main-nav-item-group{height:80px;line-height:80px;} #module_599627617 .main-nav-content{height:80px}
#module_599627617 .main-nav-content{background-color:rgba(0,0,0,0);}
#module_599627617 .main-nav-content{background-image:none;}
#module_599627617 .main-nav-content{background-repeat:no-repeat;}
#module_599627617 .main-nav-content{}
#module_599627617 .main-nav-content{}
#module_599627617 .main-nav-content{}
#module_599627617 .main-nav-content{text-align:right;}
#module_599627617 .main-nav-item{font-size:14px;}
#module_599627617 .main-nav-item{font-family:微软雅黑,Microsoft YaHei;}
#module_599627617 .main-nav-item{color:rgb(127, 127, 127);}
#module_599627617 .main-nav-item{font-weight:normal;}
#module_599627617 .main-nav-item{text-decoration:none;}
#module_599627617 .main-nav-item{font-style:normal;}
#module_599627617 .main-nav-item{width:auto;}
#module_599627617 .main-nav-item{padding-left:25px;padding-right:25px;}
#module_599627617 .main-nav-item{background-image:none;}
#module_599627617 .main-nav-item{background-color:transparent;}
#module_599627617 .main-nav-item{background-repeat:no-repeat;}
#module_599627617 .main-nav-item{}
#module_599627617 .main-nav-item{}
#module_599627617 .main-nav-item{}
#module_599627617 .main-nav-item-group{border-right-color:#f2f2f2;}
#module_599627617 .main-nav-item-group{border-right-style:solid;}
#module_599627617 .main-nav-item-group{border-right-width:0px;}
#module_599627617 .sub-nav-item-group{width:120px;}
#module_599627617 .sub-nav-item{height:38px;line-height:38px;} #module_599627617 .sub-nav-item .inner {line-height: calc(100% + (38px - 10px));}
#module_599627617 .sub-nav-item .inner{font-size:12px;}
#module_599627617 .sub-nav-item .inner{font-family:微软雅黑,Microsoft YaHei;}
#module_599627617 .sub-nav-item .inner{color:rgb(38, 38, 38);}
#module_599627617 .sub-nav-item .inner{font-weight:normal;}
#module_599627617 .sub-nav-item .inner{text-decoration:none;}
#module_599627617 .sub-nav-item .inner{font-style:normal;}
#module_599627617 .sub-nav-item .inner,#module_599627617 .arrow,#module_599627617 .sub-normal-color{background-color:rgb(248, 248, 248);}
#module_599627617 .sub-nav-item .inner{background-image:none;}
#module_599627617 .sub-nav-item .inner{background-repeat:no-repeat;}
#module_599627617 .sub-nav-item .inner{}
#module_599627617 .sub-nav-item .inner{}
#module_599627617 .sub-nav-item .inner{}
#module_599627617 .sub-nav-item{border-bottom-color:rgb(216, 216, 216);}
#module_599627617 .sub-nav-item{border-bottom-style:solid;}
#module_599627617 .sub-nav-item{border-bottom-width:1px;}
#module_599627617 .sub-nav-item{margin-bottom:0px;}
#module_599627617 .main-nav-item-hover{font-size:14px;}
#module_599627617 .main-nav-item-hover{font-family:微软雅黑,Microsoft YaHei;}
#module_599627617 .main-nav-item-hover{color:#fff;}
#module_599627617 .main-nav-item-hover{font-weight:normal;}
#module_599627617 .main-nav-item-hover{text-decoration:none;}
#module_599627617 .main-nav-item-hover{font-style:normal;}
#module_599627617 .main-nav-item-hover::before{background-color:rgb(251, 188, 44);}
#module_599627617 .main-nav-item-hover::before{background-image:none;}
#module_599627617 .main-nav-item-hover::before{background-repeat:no-repeat;}
#module_599627617 .main-nav-item-hover::before{}
#module_599627617 .main-nav-item-hover::before{}
#module_599627617 .main-nav-item-hover::before{}
#module_599627617 .sub-nav-item:hover .inner{font-size:12px;}
#module_599627617 .sub-nav-item:hover .inner{font-family:微软雅黑,Microsoft YaHei;}
#module_599627617 .sub-nav-item:hover .inner{color:rgb(242, 242, 242);}
#module_599627617 .sub-nav-item:hover .inner{font-weight:normal;}
#module_599627617 .sub-nav-item:hover .inner{text-decoration:none;}
#module_599627617 .sub-nav-item:hover .inner{font-style:normal;}
#module_599627617 .sub-nav-item:hover .inner,#module_599627617 .sub-hover-color{background-color:rgba(251, 188, 44, 0.87);}
#module_599627617 .sub-nav-item:hover .inner{background-image:none;}
#module_599627617 .sub-nav-item:hover .inner{background-repeat:no-repeat;}
#module_599627617 .sub-nav-item:hover .inner{}
#module_599627617 .sub-nav-item:hover .inner{}
#module_599627617 .sub-nav-item:hover .inner{}


.module_599627730 .ModuleHead .HeadCenter{float:none;}
#module_599627730 {
padding:0px;
}
#module_599627730 {
position:fixed;
z-index:100000;
top:0px;
left:0px;
width:100%;
height: auto;
}
#module_599627730 .ModuleHead599627730 {
display:none;
}
#module_599627730 .BodyCenter.BodyCenter599627730 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627730 >.module_599627730 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627730 >.module_599627730{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627730 .BodyCenter.BodyCenter599627730 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627730 .BodyCenter.BodyCenter599627730 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627730 >.module_599627730 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627730 >.module_599627730 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627730 >.module_599627730 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627730:hover {
border:none;
}
#module_599627730:hover >.module_599627730 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627730 .ModulePupopContainer{top:calc(50% + 0px);left:calc(50% + 0px);transform:translate(-50%, -50%);} #module_599627730 .IsAdvertisement{top:calc(50% + 0px);left:calc(50% + 0px);transform:translate(-50%, -50%);}
#module_599627730 .ModulePupopContainer{background-color:rgba(0,0,0,0);}
#module_599627730 .ModulePupopContainer{background-image:none;}
#module_599627730 .ModulePupopContainer{background-repeat:no-repeat;}
#module_599627730 .ModulePupopContainer{background-position:0% 0%;}
#module_599627730 .ModulePupopContainer{background-attachment:scroll;}
#module_599627730 .ModulePupopContainer{}
#module_599627730 .ModulePupopContainer{padding-top:0px;}
#module_599627730 .ModulePupopContainer{padding-bottom:0px;}
#module_599627730 .ModulePupopContainer{padding-left:0px;}
#module_599627730 .ModulePupopContainer{padding-right:0px;}
#module_599627730 .ModulePupopContainer{border-top-color:#e1e1e1;}
#module_599627730 .ModulePupopContainer{border-top-style:solid;}
#module_599627730 .ModulePupopContainer{border-top-width:0px;}
#module_599627730 .ModulePupopContainer{border-bottom-color:#e1e1e1;}
#module_599627730 .ModulePupopContainer{border-bottom-style:solid;}
#module_599627730 .ModulePupopContainer{border-bottom-width:0px;}
#module_599627730 .ModulePupopContainer{border-left-color:#e1e1e1;}
#module_599627730 .ModulePupopContainer{border-left-style:solid;}
#module_599627730 .ModulePupopContainer{border-left-width:0px;}
#module_599627730 .ModulePupopContainer{border-right-color:#e1e1e1;}
#module_599627730 .ModulePupopContainer{border-right-style:solid;}
#module_599627730 .ModulePupopContainer{border-right-width:0px;}
#module_599627730 .ModulePupopContainer{border-top-left-radius:0px;}
#module_599627730 .ModulePupopContainer{border-top-right-radius:0px;}
#module_599627730 .ModulePupopContainer{border-bottom-left-radius:0px;}
#module_599627730 .ModulePupopContainer{border-bottom-right-radius:0px;}
#module_599627730 .ModulePupopContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627730 .pupopClose{top:-30px;right:-30px;left:auto;transform:none;}
#module_599627730 .pupopClose{width:calc(15px + 3px); height:calc(15px + 3px);}
#module_599627730 .pupopClose .iconfont{font-size:30px;}
#module_599627730 .pupopClose .iconfont{color:rgba(255, 255, 255, 1);}
#module_599627730 .pupopClose{background-color:rgba(255, 255, 255, 0);}
#module_599627730 .pupopClose{background-image:none;}
#module_599627730 .pupopClose{background-repeat:no-repeat;}
#module_599627730 .pupopClose{background-position:0% 0%;}
#module_599627730 .pupopClose{background-attachment:scroll;}
#module_599627730 .pupopClose{}
#module_599627730 .pupopClose{border-top-color:#e1e1e1;}
#module_599627730 .pupopClose{border-top-style:solid;}
#module_599627730 .pupopClose{border-top-width:0px;}
#module_599627730 .pupopClose{border-bottom-color:#e1e1e1;}
#module_599627730 .pupopClose{border-bottom-style:solid;}
#module_599627730 .pupopClose{border-bottom-width:0px;}
#module_599627730 .pupopClose{border-left-color:#e1e1e1;}
#module_599627730 .pupopClose{border-left-style:solid;}
#module_599627730 .pupopClose{border-left-width:0px;}
#module_599627730 .pupopClose{border-right-color:#e1e1e1;}
#module_599627730 .pupopClose{border-right-style:solid;}
#module_599627730 .pupopClose{border-right-width:0px;}
#module_599627730 .pupopClose{border-top-left-radius:0px;}
#module_599627730 .pupopClose{border-top-right-radius:0px;}
#module_599627730 .pupopClose{border-bottom-left-radius:0px;}
#module_599627730 .pupopClose{border-bottom-right-radius:0px;}
#module_599627730 .ModulePupopGiant{background-color:rgba(0, 0, 0, .6);}
#module_599627730 .ModulePuponArrow.PosTop {border-bottom-color:#e8e8e8;} #module_599627730 .ModulePuponArrow.PosBottom {border-top-color:#e8e8e8;}
#module_599627730{position: fixed !important; top: 0 !important; left: 0 !important; z-index: 100000 !important; display: none;width: 100% !important; height: 100% !important;}.ModulePupopContainer.ModulePupopContainer599627730 {max-width:1200px;}
.IsAdvertisement .ModulePupopContainer.ModulePupopContainer599627730 {width:1200px;}
#Sub599627730_1.ModuleSubPupopBox {height: auto !important; max-height: calc(100vh - 100px);}
.module_599627731 .ModuleHead .HeadCenter{float:none;}
#module_599627731 {
padding:0px;
}
#module_599627731 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627731 .ModuleHead599627731 {
display:none;
}
#module_599627731 .BodyCenter.BodyCenter599627731 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627731 >.module_599627731 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627731 >.module_599627731{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627731 .BodyCenter.BodyCenter599627731 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627731 .BodyCenter.BodyCenter599627731 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627731 >.module_599627731 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627731 >.module_599627731 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627731 >.module_599627731 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627731:hover {
border:none;
}
#module_599627731:hover >.module_599627731 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627731 .videoTitle{font-size:14px;}
#module_599627731 .videoTitle{font-family:微软雅黑,Microsoft YaHei;}
#module_599627731 .videoTitle{color:#333;}
#module_599627731 .videoTitle{font-weight:bold;}
#module_599627731 .videoTitle{text-decoration:none;}
#module_599627731 .videoTitle{font-style:normal;}
#module_599627731 .videoTitle{text-align:left;}
#module_599627731 .videoTitle{margin-top:8px;}
#module_599627731 .videoTitle{margin-bottom:2px;}
#module_599627731 .videoTitle{margin-left:0px;}
#module_599627731 .videoTitle{margin-right:0px;}
#module_599627731 .videoDescrib{font-size:12px;}
#module_599627731 .videoDescrib{font-family:微软雅黑,Microsoft YaHei;}
#module_599627731 .videoDescrib{color:#999;}
#module_599627731 .videoDescrib{font-weight:normal;}
#module_599627731 .videoDescrib{text-decoration:none;}
#module_599627731 .videoDescrib{font-style:normal;}
#module_599627731 .videoDescrib{text-align:left;}
#module_599627731 .videoBox:hover .videoTitle{font-size:14px;}
#module_599627731 .videoBox:hover .videoTitle{font-family:微软雅黑,Microsoft YaHei;}
#module_599627731 .videoBox:hover .videoTitle{color:#333;}
#module_599627731 .videoBox:hover .videoTitle{font-weight:bold;}
#module_599627731 .videoBox:hover .videoTitle{text-decoration:none;}
#module_599627731 .videoBox:hover .videoTitle{font-style:normal;}
#module_599627731 .videoBox:hover .videoDescrib{font-size:12px;}
#module_599627731 .videoBox:hover .videoDescrib{font-family:微软雅黑,Microsoft YaHei;}
#module_599627731 .videoBox:hover .videoDescrib{color:#999;}
#module_599627731 .videoBox:hover .videoDescrib{font-weight:normal;}
#module_599627731 .videoBox:hover .videoDescrib{text-decoration:none;}
#module_599627731 .videoBox:hover .videoDescrib{font-style:normal;}

.ModuleFullGiant.layout-101,
.ModuleFullGiant.layout-101>.BodyCenter,
.ModuleFullGiant.layout-101 .ModuleFullContainer,
.ModuleFullGiant.layout-101 .ModuleFullContainer>.ModuleSubContainer {height: 100%;}
.ModuleFullGiant.layout-101>.BodyCenter {position: relative;}
.ModuleFullGiant.layout-101 .ModuleFullContainer>.ModuleSubContainer {margin: 0;}
.ModuleFullGiant.layout-101 .swiper-container-horizontal .swiper-wrapper{
    /* 通过改变animation-timing-function 制作弹性切换效果 */
          transition: 2s cubic-bezier(0.68, -0.4, 0.27, 1.34) 0.2s;
      }
.ModuleFullGiant.layout-101 .ModuleFullItem { opacity: 1; position: relative; overflow-x: hidden; overflow-y: auto; font-size: 16px; color: #000; text-align: center;}

    /*滚动条 start*/
.ModuleFullGiant.layout-101 .ModuleFullItem::-webkit-scrollbar {width: 0px;}
/*滚动条 end*/
.ModuleFullGiant.layout-101 .ModuleFullItem .ModuleFullItemContainer {width: 100%; height: auto; position: absolute; left: 0; max-width: 100%; max-height: 100%;}
.ModuleFullGiant.layout-101 .ModuleFullItem .ModuleFullItemContainer>.addnewhelper {width: 100%; height: 100vh !important; display: flex; align-items: center; justify-content: center;}
.ModuleFullGiant.layout-101 .ModuleFullItem>div.fullBgVideo{width: 100%; height: 100%; display: flex; position: absolute; top: 0; left: 0; z-index: -1;}
.ModuleFullGiant.layout-101 .ModuleFullItem>div.fullBgVideo .bgVideoMask{position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgb(0, 0, 0); opacity: .2;}
.ModuleFullGiant.layout-101 .ModuleFullItem>div.fullBgVideo .bgVideo{width: 100%; height: 100%; object-fit: cover; object-position: center center;}
.ModuleFullGiant.layout-101 .ModuleFullItem>div.fullBgVideo.noBgVideo{display: none;}
/* 分页器 S */
.ModuleFullGiant.layout-101 .swiper-pagination-full {width: auto; height: auto; position: absolute; z-index: 10; transition: .3s;}
/* 分页器类型1 S */
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-1 .swiper-pagination-bullet {width: 30px; height: 30px; display: block; background: unset; opacity: 1; position: relative; transition: 400ms; margin-bottom: 0px;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-1 .swiper-pagination-bullet:last-child {margin-bottom: 0 !important;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic {position: absolute; top: 50%; left: 50%; width: 10px; height: 10px; transform: translate(-50%, -50%) scale(1); background: #ff6701; border-radius: 100%; transition: 350ms cubic-bezier(.215, .61, .355, 1);}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic-current {position: absolute; top: 50%; left: 50%; width: 0; height: 0; transform: translate(-50%, -50%); border: 1px solid rgba(0, 0, 0, 0); border-radius: 100%; opacity: 1; transition: border 350ms cubic-bezier(.215, .61, .355, 1);}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic {transform: translate(-50%, -50%) scale(1.4); background: #fff; border: 1px solid #ff6701;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic-current {width: 28px; height: 28px; opacity: 0; border-color: #ff6701; transition: 500ms cubic-bezier(.215, .61, .355, 1);}
/* 分页器类型1 E */
/* 分页器类型2 S */
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-2 {font-size: 14px; color: #666; font-weight: 400; text-align: right;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-2 .swiper-pagination-current,
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-2 .swiper-pagination-total {height: 50px; display: inline-block; line-height: 50px; margin-left: 5px;margin-right: 14px;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-2 .swiper-pagination-current {color: #ff6701; margin: 0 5px 0 0;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-2 .iconfont {display: block; cursor: pointer; color: #333; font-size: 57px; line-height: 1;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-2 .iconfont:hover{color: #ff6701;}
/* 分页器类型2 E */
/* 分页器类型3 S */
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom {width: auto; height: 20px; display: block; margin-bottom: 10px; position: relative; cursor: pointer;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom:last-child {margin-bottom: 0;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom-text {display: none; margin: 0 5px; white-space: nowrap; font-size: 14px; color: #ff6701;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom:before {content: ''; width: 20px; height: 1px; position: absolute; top: 20px; right: 0; border: 0; background: #707070; border-radius: 0; z-index: 1; transition: 0.3s ease 0s;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom:after {content: ''; width: 5px; height: 5px; position: absolute; right: -5px; top: 18px; border: 1px solid transparent; border-radius: 50%;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom-active .swiper-pagination-custom-text {display: block;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom-active:before {width: 100%; background: #ff6701;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-3 .swiper-pagination-custom-active:after {border-color: #ff6701;}
/* 分页器类型3 E */
/* 分页器类型4 S */
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-4 {text-align: center;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-4 .swiper-pagination-bullet {width: auto; height: auto; display: block; margin-bottom: 30px; line-height: 1; font-size: 16px; color: #999; font-weight: 400; opacity: 1; background: unset;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-4 .swiper-pagination-bullet:last-child {margin-bottom: 0 !important;}
.ModuleFullGiant.layout-101 .swiper-pagination-full-type-4 .swiper-pagination-bullet-active {font-size: 24px; color: #ff6701;}
/* 分页器类型4 E */
/* 分页器 E */

/* 滑动指示器 S */
.ModuleFullGiant.layout-101 .scroll-indicator {width: auto; height: auto; position: absolute; z-index: 10; transition: .3s; text-align: center; cursor: pointer;}
.ModuleFullGiant.layout-101 .scroll-indicator .scroll-indicator-text {font-size: 12px; color: #fff; margin: 0;}
/* 滑动指示器类型1 S */
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn {
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 0;
    margin: auto auto 12px auto;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none;
    background: none;
    border-radius: 100%;
    -webkit-transition: opacity 1s cubic-bezier(.26,1.04,.54,1);
    -o-transition: opacity 1s cubic-bezier(.26,1.04,.54,1);
    transition: opacity 1s cubic-bezier(.26,1.04,.54,1);
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn .iconfont {font-size: 26px; color: #fff;}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c1,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c2,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c3,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c4 {position: absolute; top: 0; left: 0; display: block; width: 50%; height: 100%; overflow: hidden;}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c2,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c4 {left: 50%;}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-common span {
    display: block;
    width: 100%;
    height: 100%;
    border: 2px solid #fff;
    opacity: .3;
    -webkit-transition: -webkit-transform .8s cubic-bezier(.77,0,.175,1);
    transition: -webkit-transform .8s cubic-bezier(.77,0,.175,1);
    -o-transition: transform .8s cubic-bezier(.77,0,.175,1);
    transition: transform .8s cubic-bezier(.77,0,.175,1);
    transition: transform .8s cubic-bezier(.77,0,.175,1),-webkit-transform .8s cubic-bezier(.77,0,.175,1);
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c1 span,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c2 span {-webkit-transition-delay: .15s; -o-transition-delay: .15s; transition-delay: .15s;}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c1 span,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c3 span {
    border-top-left-radius: 52px;
    border-bottom-left-radius: 52px;
    border-right: 0 !important;
    -webkit-transform-origin: right center;
    -ms-transform-origin: right center;
    transform-origin: right center;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c3 span {-webkit-transform: rotate(-180deg); -ms-transform: rotate(-180deg); transform: rotate(-180deg);}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c2 span,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c4 span {
    border-top-right-radius: 52px;
    border-bottom-right-radius: 52px;
    border-left: 0 !important;
    -webkit-transform-origin: left center;
    -ms-transform-origin: left center;
    transform-origin: left center;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c4 span  {-webkit-transform: rotate(180deg); -ms-transform: rotate(180deg); transform: rotate(180deg);}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c3 span,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn-c4 span {
    opacity: 0;
    -webkit-transition: opacity .5s cubic-bezier(.77,0,.175,1) .45s,-webkit-transform .8s cubic-bezier(.77,0,.175,1) .15s;
    transition: opacity .5s cubic-bezier(.77,0,.175,1) .45s,-webkit-transform .8s cubic-bezier(.77,0,.175,1) .15s;
    -o-transition: transform .8s cubic-bezier(.77,0,.175,1) .15s,opacity .5s cubic-bezier(.77,0,.175,1) .45s;
    transition: transform .8s cubic-bezier(.77,0,.175,1) .15s,opacity .5s cubic-bezier(.77,0,.175,1) .45s;
    transition: transform .8s cubic-bezier(.77,0,.175,1) .15s,opacity .5s cubic-bezier(.77,0,.175,1) .45s,-webkit-transform .8s cubic-bezier(.77,0,.175,1) .15s;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1:hover .scroll-indicator-btn-c1 span {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1:hover .scroll-indicator-btn-c2 span {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-1:hover .scroll-indicator-btn-c3 span,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1:hover .scroll-indicator-btn-c4 span {
    -webkit-transition: opacity .4s cubic-bezier(.26,1.04,.54,1),-webkit-transform .8s cubic-bezier(.77,0,.175,1);
    transition: opacity .4s cubic-bezier(.26,1.04,.54,1),-webkit-transform .8s cubic-bezier(.77,0,.175,1);
    -o-transition: transform .8s cubic-bezier(.77,0,.175,1),opacity .4s cubic-bezier(.26,1.04,.54,1);
    transition: transform .8s cubic-bezier(.77,0,.175,1),opacity .4s cubic-bezier(.26,1.04,.54,1);
    transition: transform .8s cubic-bezier(.77,0,.175,1),opacity .4s cubic-bezier(.26,1.04,.54,1),-webkit-transform .8s cubic-bezier(.77,0,.175,1);
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    transform: rotate(0);
    opacity: 1;
}
/* 滑动指示器类型1 E */
/* 滑动指示器类型2 S */
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-text {display: none;}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn {
    height: 60px;
    width: 60px;
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    background-color: #fff;
    -webkit-transform: scale(1) translate(-50%,-50%);
    transform: scale(1) translate(-50%,-50%);
    transition: transform 2s cubic-bezier(.33, 0, 0, 1) .5s, -webkit-transform 2s cubic-bezier(.33, 0, 0, 1) .5s;
    will-change: transform;
    -webkit-transform-origin: top left;
    transform-origin: top left;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn-c1,
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn-c2 {
    border: 1px solid #fff;
    height: 110px;
    width: 110px;
    border-radius: 50%;
    position: absolute;
    top: -25px;
    left: -25px;
    box-sizing: content-box;
    will-change: transform;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn-c1 {
    -webkit-animation: inner-circle-1-animation 2s cubic-bezier(.33, 0, 0, 1) infinite, inner-circle-1-animation-fade 2s linear infinite;
    animation: inner-circle-1-animation 2s cubic-bezier(.33, 0, 0, 1) infinite, inner-circle-1-animation-fade 2s linear infinite;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn-c2 {
    -webkit-animation: inner-circle-1-animation 2s 1s cubic-bezier(.33, 0, 0, 1) infinite, inner-circle-1-animation-fade 2s 1s linear infinite;
    animation: inner-circle-1-animation 2s 1s cubic-bezier(.33, 0, 0, 1) infinite, inner-circle-1-animation-fade 2s 1s linear infinite;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down {
    width: 22px;
    height: 10px;
    position: absolute;
    top: 50%;
    left: 50%;
    overflow: hidden;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    transition: opacity .4s cubic-bezier(.33,0,.67,1) 1.6s,margin-top 1.2s cubic-bezier(.33,0,0,1) 1.4s;
    will-change: transform;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down:after,
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down:before {
    content: "";
    position: absolute;
    height: 1px;
    width: 14px;
    background: #d59772;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down:before {
    top: 0;
    left: 0;
    -webkit-transform: rotate(37deg);
    transform: rotate(37deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down:after {
    top: 0;
    right: 0;
    -webkit-transform: rotate(-37deg);
    transform: rotate(-37deg);
    -webkit-transform-origin: top right;
    transform-origin: top right;
}
@-webkit-keyframes inner-circle-1-animation {
    0% {-webkit-transform: scale(0); transform: scale(0);}
    to {-webkit-transform: scale(1.32); transform: scale(1.32);}
}
@keyframes inner-circle-1-animation {
    0% {-webkit-transform: scale(0); transform: scale(0);}
    to {-webkit-transform: scale(1.32); transform: scale(1.32);}
}
@-webkit-keyframes inner-circle-1-animation-fade {
    0% {opacity: 1;}
    88% {opacity: 1;}
    98% {opacity: 0;}
    to {opacity: 0;}
}
@keyframes inner-circle-1-animation-fade {
    0% {opacity: 1;}
    88% {opacity: 1;}
    98% {opacity: 0;}
    to {opacity: 0;}
}
@media only screen and (max-width: 767px) {
    .ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn-c1,
    .ModuleFullGiant.layout-101 .scroll-indicator-type-2 .scroll-indicator-btn-c2 {height: 90px; width: 90px; top: -15px; left: -15px;}
    .ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down {width: 18px; height: 8px;}
}
/* 滑动指示器类型2 E */
/* 滑动指示器类型2 S */
.ModuleFullGiant.layout-101 .scroll-indicator-type-3 .scroll-indicator-btn {
    width: 22px;
    height: 42px;
    margin: 0 auto 15px auto;
    border-radius: 15px;
    border: 2px solid #fff;
    -webkit-animation: intro 1s;
    animation: intro 1s;
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-3 .scroll-indicator-btn-c1 {
    width: 3px;
    height: 3px;
    margin: 6px auto;
    border-radius: 4px;
    background: #fff;
    -webkit-animation: finger 1s infinite;
    animation: finger 1s infinite;
}
@-webkit-keyframes intro {
    0% {opacity: 0; -webkit-transform: translateY(40px); transform: translateY(40px);}
    100% {opacity: 1; -webkit-transform: translateY(0); transform: translateY(0);}
}
@keyframes intro {
    0% {opacity: 0; -webkit-transform: translateY(40px); transform: translateY(40px);}
    100% {opacity: 1; -webkit-transform: translateY(0); transform: translateY(0);}
}
@-webkit-keyframes finger {
    0% {opacity: 1}
    100% {opacity: 0; -webkit-transform: translateY(20px); transform: translateY(20px);}
}
@keyframes finger {
    0% {opacity: 1}
    100% {opacity: 0; -webkit-transform: translateY(20px); transform: translateY(20px);}
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-3 .scroll-indicator-btn.scrolltop,
.ModuleFullGiant.layout-101 .scroll-indicator-type-1 .scroll-indicator-btn.scrolltop .iconfont {
    transform: rotate(180deg);
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down.scrolltop:before{
    transform-origin: bottom right;
    bottom: 0;
    right: 0;
    top:auto;
    left:auto
}
.ModuleFullGiant.layout-101 .scroll-indicator-type-2 .arrow-down.scrolltop:after{
    transform-origin: bottom left;
    bottom: 0;
    left: 0;
    top:auto;
    right:auto
}


/* 滑动指示器类型2 E */
/* 滑动指示器 E */

@media only screen and (max-width: 767px) {
    .ModuleFullGiant.layout-101 .swiper-pagination-full {display: none;}
    .ModuleFullGiant.layout-101 .IsNotApplyMobile .ModuleSubContainer {display: block;}
    .ModuleFullGiant.layout-101 .IsNotApplyMobile .ModuleFullItemContainer {position: static !important; transform: unset !important;}
    .ModuleFullGiant.layout-101 .IsNotApplyMobile .ModuleFullItem .ModuleFullItemContainer>.addnewhelper {height: 60px !important;}
}.module_599627613 .ModuleHead .HeadCenter{float:none;}
#module_599627613 {
padding:0px;
}
#module_599627613 {
}
#module_599627613 .ModuleHead599627613 {
display:none;
}

#module_599627613 #Sub599627613_1_item {background-color:rgba(0,0,0,0);}
#module_599627613 #Sub599627613_1_item {background-image:url(/comdata/78819/202407/202407111034390619a6.jpg);}
#module_599627613 #Sub599627613_1_item {background-repeat:no-repeat;background-size:cover;}
#module_599627613 #Sub599627613_1_item {background-position:50% 50%;}
#module_599627613 #Sub599627613_1_item {background-attachment:scroll;}
#module_599627613 #Sub599627613_1 {}#module_599627613 #Sub599627613_1_item{}
#module_599627613 #Sub599627613_1 {!bgVideo!}{bgVideoUrl:none}
#module_599627613 #Sub599627613_1 {}#module_599627613 #Sub599627613_1_video .bgVideoMask{opacity:0.2}
#module_599627613 #Sub599627613_1 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_1{padding-top:100px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_1{padding-bottom:0px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_1{padding-left:100px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_1{padding-right:100px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_1{padding-top:0px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_1{padding-bottom:0px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_1{padding-left:0px;}}
#module_599627613 #Sub599627613_1 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_1{padding-right:0px;}}
#module_599627613 #Sub599627613_1 {!alignItems!}{center}
#module_599627613 #Sub599627613_2_item {background-color:rgba(0,0,0,0);}
#module_599627613 #Sub599627613_2_item {background-image:url(/comdata/78819/202407/20240711103245ff3e2c.jpg);}
#module_599627613 #Sub599627613_2_item {background-repeat:no-repeat;background-size:cover;}
#module_599627613 #Sub599627613_2_item {background-position:100% 0%;}
#module_599627613 #Sub599627613_2_item {background-attachment:scroll;}
#module_599627613 #Sub599627613_2 {}#module_599627613 #Sub599627613_2_item{}
#module_599627613 #Sub599627613_2 {!bgVideo!}{bgVideoUrl:none}
#module_599627613 #Sub599627613_2 {}#module_599627613 #Sub599627613_2_video .bgVideoMask{opacity:0.2}
#module_599627613 #Sub599627613_2 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_2{padding-top:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_2{padding-bottom:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_2{padding-left:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_2{padding-right:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_2{padding-top:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_2{padding-bottom:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_2{padding-left:0px;}}
#module_599627613 #Sub599627613_2 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_2{padding-right:0px;}}
#module_599627613 #Sub599627613_2 {!alignItems!}{center}
#module_599627613 #Sub599627613_3_item {background-color:rgba(0, 0, 0, 0.95);}
#module_599627613 #Sub599627613_3_item {background-image:none;}
#module_599627613 #Sub599627613_3_item {background-repeat:no-repeat;}
#module_599627613 #Sub599627613_3_item {background-position:0% 0%;}
#module_599627613 #Sub599627613_3_item {background-attachment:scroll;}
#module_599627613 #Sub599627613_3 {}#module_599627613 #Sub599627613_3_item{}
#module_599627613 #Sub599627613_3 {!bgVideo!}{bgVideoUrl:none}
#module_599627613 #Sub599627613_3 {}#module_599627613 #Sub599627613_3_video .bgVideoMask{opacity:0.2}
#module_599627613 #Sub599627613_3 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_3{padding-top:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_3{padding-bottom:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_3{padding-left:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_3{padding-right:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_3{padding-top:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_3{padding-bottom:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_3{padding-left:0px;}}
#module_599627613 #Sub599627613_3 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_3{padding-right:0px;}}
#module_599627613 #Sub599627613_3 {!alignItems!}{top}
#module_599627613 #Sub599627613_5_item {background-color:rgba(0,0,0,0);}
#module_599627613 #Sub599627613_5_item {background-image:url(/comdata/78819/202010/20201014102313cb1579.jpg);}
#module_599627613 #Sub599627613_5_item {background-repeat:no-repeat;background-size:cover;}
#module_599627613 #Sub599627613_5_item {background-position:50% 50%;}
#module_599627613 #Sub599627613_5_item {background-attachment:scroll;}
#module_599627613 #Sub599627613_5 {}#module_599627613 #Sub599627613_5_item{}
#module_599627613 #Sub599627613_5 {!bgVideo!}{bgVideoUrl:none}
#module_599627613 #Sub599627613_5 {}#module_599627613 #Sub599627613_5_video .bgVideoMask{opacity:0.2}
#module_599627613 #Sub599627613_5 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_5{padding-top:0px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_5{padding-bottom:0px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_5{padding-left:0px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_5{padding-right:0px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_5{padding-top:0px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_5{padding-bottom:40px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_5{padding-left:0px;}}
#module_599627613 #Sub599627613_5 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_5{padding-right:0px;}}
#module_599627613 #Sub599627613_5 {!alignItems!}{center}
#module_599627613 #Sub599627613_6_item {background-color:rgba(0,0,0,0);}
#module_599627613 #Sub599627613_6_item {background-image:url(/comdata/78819/202404/2024041913084777913a.jpg);}
#module_599627613 #Sub599627613_6_item {background-repeat:no-repeat;background-size:cover;}
#module_599627613 #Sub599627613_6_item {background-position:50% 50%;}
#module_599627613 #Sub599627613_6_item {background-attachment:scroll;}
#module_599627613 #Sub599627613_6 {}#module_599627613 #Sub599627613_6_item{}
#module_599627613 #Sub599627613_6 {!bgVideo!}{bgVideoUrl:none}
#module_599627613 #Sub599627613_6 {}#module_599627613 #Sub599627613_6_video .bgVideoMask{opacity:0.2}
#module_599627613 #Sub599627613_6 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_6{padding-top:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_6{padding-bottom:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_6{padding-left:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (min-width: 768px) {#module_599627613 #Sub599627613_6{padding-right:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_6{padding-top:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_6{padding-bottom:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_6{padding-left:0px;}}
#module_599627613 #Sub599627613_6 {}@media only screen and (max-width: 767px) {#module_599627613 #Sub599627613_6{padding-right:0px;}}
#module_599627613 #Sub599627613_6 {!alignItems!}{center}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic{background-color:rgb(255, 255, 255);} #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic-current{border-color:rgb(255, 255, 255);}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic{border-color:#ff6701;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic{border-style:solid;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-graphic{border-width:0px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet{margin-bottom: 0px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .iconfont{color:#333;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2{font-size:14px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-current, #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-total{font-family:微软雅黑,Microsoft YaHei;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2{color:#666;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-current, #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-total{font-weight:400;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-current, #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-total{text-decoration:none;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-current, #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-total{font-style:normal;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-3 .swiper-pagination-custom:before{background-color:#707070;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{font-size:16px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{font-family:微软雅黑,Microsoft YaHei;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{color:#999;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{font-weight:400;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{text-decoration:none;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{font-style:normal;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet{margin-bottom: 30px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic{background-color:rgba(0,0,0,0);}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic, #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic-current{border-color:rgb(251, 188, 44);}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic{border-style:solid;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-1 .swiper-pagination-bullet-active .swiper-pagination-bullet-graphic{border-width:1px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .iconfont:hover{color:#ff6701;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-2 .swiper-pagination-current{color:#ff6701;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-3 .swiper-pagination-custom-active .swiper-pagination-custom-text{color:#ff6701;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-3 .swiper-pagination-custom-active:before{background-color:#ff6701;} #module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-3 .swiper-pagination-custom-active:after {border-color:#ff6701;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet-active{font-size:24px;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet-active{font-family:微软雅黑,Microsoft YaHei;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet-active{color:#ff6701;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet-active{font-weight:400;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet-active{text-decoration:none;}
#module_599627613 #swiper-pagination-full-599627613 {}#module_599627613 #swiper-pagination-full-599627613.swiper-pagination-full-type-4 .swiper-pagination-bullet-active{font-style:normal;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-btn{background:rgba(255, 255, 255, 0);}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-btn-common span {border-color:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-btn-common span {border-style:solid;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-btn-common span {border-width:1px;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-btn .iconfont{color:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-text{font-size:12px;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-text{font-family:;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-text{color:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-text{font-weight:normal;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-text{text-decoration:none;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-1 .scroll-indicator-text{font-style:normal;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-2 .scroll-indicator-btn{background-color:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-2 .scroll-indicator-btn-c1, #module_599627613 #scroll-indicator-599627613.scroll-indicator-type-2 .scroll-indicator-btn-c2{border-color:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-2 .arrow-down:after, #module_599627613 #scroll-indicator-599627613.scroll-indicator-type-2 .arrow-down:before{background:#ff6701;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-btn{border-color:#fff;} #module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-btn-c1 {background:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-text{font-size:12px;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-text{font-family:;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-text{color:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-text{font-weight:normal;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-text{text-decoration:none;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3 .scroll-indicator-text{font-style:normal;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3:hover .scroll-indicator-btn{border-color:#fff;} #module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3:hover .scroll-indicator-btn-c1 {background:#fff;}
#module_599627613 #scroll-indicator-599627613 {}#module_599627613 #scroll-indicator-599627613.scroll-indicator-type-3:hover .scroll-indicator-text{color:#fff;}
#module_599627613 {height: 100%;}
.module_599627614 .ModuleHead .HeadCenter{float:none;}
#module_599627614 {
padding:0px;
}
#module_599627614 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627614 .ModuleHead599627614 {
display:none;
}
#module_599627614 .BodyCenter.BodyCenter599627614 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627614 >.module_599627614 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627614 >.module_599627614{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627614 .BodyCenter.BodyCenter599627614 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627614 .BodyCenter.BodyCenter599627614 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627614 >.module_599627614 {
margin-top:-5.3419%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627614 >.module_599627614 {
margin-top:0.0000%;
margin-left:5.4496%;
margin-right:5.4496%;
margin-bottom:0.0000%;
}
}
#module_599627614 >.module_599627614 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627614:hover {
border:none;
}
#module_599627614:hover >.module_599627614 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627614 .ModuleSubContainer{background-color:transparent;}
#module_599627614 .ModuleSubContainer{background-image:none;}
#module_599627614 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627614 .ModuleSubContainer{background-position:0% 0%;}
#module_599627614 .ModuleSubContainer{background-attachment:scroll;}
#module_599627614 .ModuleSubContainer{}
#module_599627614 {!bgVideo!}{bgVideoUrl:none}
#module_599627614 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627614 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627614 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627614 {}
/*图文模块 layout 101*/
.ModuleImageTextGiant.layout-101 {font-size: 14px;}
.ModuleImageTextGiant.layout-101 .imageTextGiant-Container{height: auto; word-wrap: break-word;max-height: 100%;padding:10px;max-width: 100%; overflow: hidden; position: relative;}.module_599627618 .ModuleHead .HeadCenter{float:none;}
#module_599627618 {
padding:0px;
}
#module_599627618 {
position:static;
z-index:0;
top:245px;
left:50px;
width:100%;
height: auto;
}
#module_599627618 .ModuleHead599627618 {
display:none;
}
#module_599627618 .BodyCenter.BodyCenter599627618 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627618 >.module_599627618 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627618 >.module_599627618{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627618 .BodyCenter.BodyCenter599627618 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627618 .BodyCenter.BodyCenter599627618 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627618 >.module_599627618 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:33.6709%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627618 >.module_599627618 {
margin-top:28.0645%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627618 >.module_599627618 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627618:hover {
border:none;
}
#module_599627618:hover >.module_599627618 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627618 .ModuleImageTextGiantContent{margin-top:0px;}
#module_599627618 .ModuleImageTextGiantContent{margin-bottom:0px;}
#module_599627618 .ModuleImageTextGiantContent{margin-left:0px;}
#module_599627618 .ModuleImageTextGiantContent{margin-right:0px;}
#module_599627618 .imageTextGiant-Container{padding-top:10px;}
#module_599627618 .imageTextGiant-Container{padding-bottom:10px;}
#module_599627618 .imageTextGiant-Container{padding-left:10px;}
#module_599627618 .imageTextGiant-Container{padding-right:10px;}
#module_599627618 .showHandle .btn{font-size:14px;}
#module_599627618 .showHandle .btn{font-family:微软雅黑,Microsoft YaHei;}
#module_599627618 .showHandle .btn{color:#333;}
#module_599627618 .showHandle .btn{font-weight:400;}
#module_599627618 .showHandle .btn{text-decoration:none;}
#module_599627618 .showHandle .btn{font-style:normal;}
#module_599627618 .showHandle div{border-top-color:#ddd !important;}
#module_599627618 .showHandle div{border-top-style:solid !important;}
#module_599627618 .showHandle div{border-top-width:1px !important;}
#module_599627618 .showHandle{background-color:transparent;}
#module_599627618 .showHandle{background-image:none;}
#module_599627618 .showHandle{background-repeat:no-repeat;}
#module_599627618 .showHandle{}
#module_599627618 .showHandle{}
#module_599627618 .showHandle{}
#module_599627618 a{font-size:unset;font-family:unset}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxContainer{margin-right: auto; margin-left: auto; padding-right: 0; padding-left: 0;}
.ModuleHoverBoxGiant.layout-101 .ModuleSubContainer{display: flex !important; position: relative; width:100%; overflow: hidden; background: none !important;}
.ModuleHoverBoxGiant.layout-101 .row{margin-right: 0px; margin-left: 0px;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxContainer{width: 100%; height: 100%}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_1{position: relative; overflow: hidden;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2{overflow: hidden; position: absolute;	display: none;  background: #fff;height: 100%; opacity: 0; top: 0; visibility: visible;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchFadeInUp{ -webkit-animation: switchFadeInUp-out .6s ease forwards; animation: switchFadeInUp-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchFadeIn{ -webkit-animation: switchFadeIn-out .6s ease forwards; animation: switchFadeIn-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchFadeInDown{ -webkit-animation: switchFadeInDown-out .6s ease forwards; animation: switchFadeInDown-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchSlideInDown{ -webkit-animation: switchSlideInDown-out .6s ease forwards; animation: switchSlideInDown-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchSlideInUp{ -webkit-animation: switchSlideInUp-out .6s ease forwards; animation: switchSlideInUp-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchBounceIn{ -webkit-animation: switchBounceIn-out .6s ease forwards; animation: switchBounceIn-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchBounceInLeft{ -webkit-animation: switchBounceInLeft-out .6s ease forwards; animation: switchBounceInLeft-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchBounceInRight{ -webkit-animation: switchBounceInRight-out .6s ease forwards; animation: switchBounceInRight-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchPulse{ -webkit-animation: switchPulse-out .6s ease forwards; animation: switchPulse-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchZoomIn{ -webkit-animation: switchZoomIn-out .6s ease forwards; animation: switchZoomIn-out .6s ease forwards; }
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchFlipInY{ -webkit-animation: switchFlipInY-out .6s ease forwards; animation: switchFlipInY-out .6s ease forwards; }
@media only screen and (min-width: 769px) {
	/* 弹现 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchFadeIn{-webkit-animation: fadeIn .6s ease forwards; animation: fadeIn .6s ease forwards;}
	/* 从上淡入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchFadeInDown{-webkit-animation: fadeInDown .6s ease forwards; animation: fadeInDown .6s ease forwards;}
	/* 从下淡入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchFadeInUp{-webkit-animation: fadeInUp .6s ease forwards; animation: fadeInUp .6s ease forwards;}
	/* 从上滑入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchSlideInDown{-webkit-animation: slideInDown .6s ease forwards; animation: slideInDown .6s ease forwards;}
	/* 从下滑入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchSlideInUp{-webkit-animation: slideInUp .6s ease forwards; animation: slideInUp .6s ease forwards;}
	/* 从里弹入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchBounceIn{-webkit-animation: bounceIn .6s ease forwards; animation: bounceIn .6s ease forwards;}
	/* 从左弹入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchBounceInLeft{-webkit-animation: bounceInLeft .6s ease forwards; animation: bounceInLeft .6s ease forwards;}
	/* 从右弹入 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchBounceInRight{-webkit-animation: bounceInRight .6s ease forwards; animation: bounceInRight .6s ease forwards;}
	/* 跳动 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchPulse{-webkit-animation: pulse .6s ease forwards; animation: pulse .6s ease forwards;}
	/* 放大 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchZoomIn{-webkit-animation: zoomIn .6s ease forwards; animation: zoomIn .6s ease forwards;}
	/* Y轴翻转 */
	.ModuleHoverBoxGiant.layout-101 .ModuleSubContainerHover:hover > .switchFlipInY{-webkit-animation: flipInY .6s ease forwards; animation: flipInY .6s ease forwards;}
}

@media only screen and (max-width: 768px) {
	.ModuleHoverBoxGiant.layout-101 .ModuleShowHover .ModuleHoverBoxItem_2{display: block !important; height: 100% !important; opacity: 1 !important; top: 0 !important;}
	.ModuleHoverBoxGiant.layout-101 .ModuleShowNormal .ModuleHoverBoxItem_2{display: none !important;}
}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2.switchAnimateNone{height: 100%;opacity: 1;top: 0;display: inline-block;visibility: visible;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .wow{visibility: visible !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .fadeIn{animation-name:fadeIn !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .fadeInLeft{animation-name:fadeInLeft !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .fadeInRight{animation-name:fadeInRight !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .fadeInUp{animation-name:fadeInUp !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .fadeInDown{animation-name:fadeInDown !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .slideInUp{animation-name:slideInUp !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .slideInDown{animation-name:slideInDown !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .slideInLeft{animation-name:slideInLeft !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .slideInRight{animation-name:slideInRight !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .zoomIn{animation-name:zoomIn !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .flipInX{animation-name:flipInX !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .flipInY{animation-name:flipInY !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .bounce{animation-name:bounce !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .flash{animation-name:flash !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .pulse{animation-name:pulse !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .rotateInDownLeft{animation-name:rotateInDownLeft !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .rotateInDownRight{animation-name:rotateInDownRight !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .bounceIn{animation-name:bounceIn !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .bounceInLeft{animation-name:bounceInLeft !important;}
.ModuleHoverBoxGiant.layout-101 .ModuleHoverBoxItem_2 .bounceInRight{animation-name:bounceInRight !important;}
/*start 退出动画*/
@keyframes switchBounceInRight-out {
	0%, 90% , 75%, 60% ,100% {
		-webkit-transition-timing-function: cubic-bezier(0.215,.61,.355,1);
		transition-timing-function: cubic-bezier(0.215,.61,.355,1);
	}
	0% {
		-webkit-transform: none;
		-ms-transform: none;
		transform: none;
	}
	60% {
		-webkit-transform: translate3d(25px,0,0);
		-ms-transform: translate3d(25px,0,0);
		transform: translate3d(25px,0,0);
	}
	75% {
		-webkit-transform: translate3d(-10px,0,0);
		-ms-transform: translate3d(-10px,0,0);
		transform: translate3d(-10px,0,0);
	}
	90% {
		-webkit-transform: translate3d(1000px,0,0);
		-ms-transform: translate3d(1000px,0,0);
		transform: translate3d(1000px,0,0);
	}
	100% {
		-webkit-transform: translate3d(3000px,0,0);
		-ms-transform: translate3d(3000px,0,0);
		transform: translate3d(3000px,0,0);
	}

}
@keyframes switchBounceInLeft-out {
	0%, 90% , 75%, 60% ,100% {
		-webkit-transition-timing-function:cubic-bezier(0.215,.61,.355,1);
		transition-timing-function:cubic-bezier(0.215,.61,.355,1);
	}
	0% {
		-webkit-transform: none;
		-ms-transform: none;
		transform: none;
    }
    60% {
		-webkit-transform: translate3d(-25px, 0, 0);
        -ms-transform: translate3d(-25px, 0, 0);
        transform: translate3d(-25px, 0, 0)
    }
    75% {
        -webkit-transform: translate3d(10px, 0, 0);
        -ms-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0)
    }
    90% {
		-webkit-transform: translate3d(-1000px, 0, 0);
        -ms-transform: translate3d(-1000px, 0, 0);
        transform: translate3d(-1000px, 0, 0);
    }
    100% {
		-webkit-transform: translate3d(-3000px, 0, 0);
        -ms-transform: translate3d(-3000px, 0, 0);
        transform: translate3d(-3000px, 0, 0);
    }
}
@keyframes switchBounceIn-out {
	0%, 100%, 20%, 40%, 60%, 80% {
		-webkit-transition-timing-function: cubic-bezier(0.215,.61,.355,1);
		transition-timing-function: cubic-bezier(0.215,.61,.355,1);
	}
	0% {
		opacity: 1;
		-webkit-transform: scale3d(1,1,1);
		-ms-transform: scale3d(1,1,1);
		transform: scale3d(1,1,1);
	}
	20% {
		-webkit-transform: scale3d(1.1,1.1,1.1);
		-ms-transform: scale3d(1.1,1.1,1.1);
		transform: scale3d(1.1,1.1,1.1);
	}
	40% {
		-webkit-transform: scale3d(.9,.9,.9);
		-ms-transform: scale3d(.9,.9,.9);
		transform: scale3d(.9,.9,.9);
	}
	60% {
		opacity: 1;
		-webkit-transform: scale3d(1.03,1.03,1.03);
		-ms-transform: scale3d(1.03,1.03,1.03);
		transform: scale3d(1.03,1.03,1.03);
	}
	80% {
		-webkit-transform: scale3d(.97,.97,.97);
		-ms-transform: scale3d(.97,.97,.97);
		transform: scale3d(.97,.97,.97);
	}
	100% {
		opacity: 0;
		-webkit-transform: scale3d(.3,.3,.3);
		-ms-transform: scale3d(.3,.3,.3);
		transform: scale3d(.3,.3,.3);
	}


}
@keyframes switchPulse-out  {
	0% {
		-webkit-transform: scale3d(1,1,1);
		-ms-transform: scale3d(1,1,1);
		transform: scale3d(1,1,1);
		opacity: 1;
	}
	50% {
		-webkit-transform: scale3d(1.05,1.05,1.05);
		-ms-transform: scale3d(1.05,1.05,1.05);
		transform: scale3d(1.05,1.05,1.05);
	}
	100% {
		-webkit-transform: scale3d(1,1,1);
		-ms-transform: scale3d(1,1,1);
		transform: scale3d(1,1,1);
		opacity: 0;
	}
}
@keyframes switchFlipInY-out {
	0% {
		-webkit-transform: perspective(400px);
		-ms-transform: perspective(400px);
		transform: perspective(400px);
	}
	40% {
		-webkit-transform: perspective(400px) rotate3d(0,1,0,-20deg);
		-ms-transform: perspective(400px) rotate3d(0,1,0,-20deg);
		transform: perspective(400px) rotate3d(0,1,0,-20deg);
		-webkit-transition-timing-function: ease-in;
		transition-timing-function: ease-in;
	}
	60% {
		-webkit-transform: perspective(400px) rotate3d(0,1,0,10deg);
		-ms-transform: perspective(400px) rotate3d(0,1,0,10deg);
		transform: perspective(400px) rotate3d(0,1,0,10deg);
		opacity: 1;
	}
	80% {
		-webkit-transform: perspective(400px) rotate3d(0,1,0,-5deg);
		-ms-transform: perspective(400px) rotate3d(0,1,0,-5deg);
		transform: perspective(400px) rotate3d(0,1,0,-5deg);
	}
	100% {
		-webkit-transform: perspective(400px) rotate3d(0,1,0,90deg);
		-ms-transform: perspective(400px) rotate3d(0,1,0,90deg);
		transform: perspective(400px) rotate3d(0,1,0,90deg);
		-webkit-transition-timing-function: ease-in;
		transition-timing-function: ease-in;
		opacity: 0;
	}
}
@keyframes  switchZoomIn-out {
	0%{
		opacity: 1;
	}
	50%{
		opacity: 0;
		-webkit-transform: scale3d(.3,.3,.3);
		-ms-transform: scale3d(.3,.3,.3);
		transform: scale3d(.3,.3,.3);
	}
	100%{
		opacity: 0;
	}
}
@keyframes switchFadeInUp-out {
	0% {
		opacity: 1;
		-webkit-transform: none;
		-ms-transform: none;
		transform: none;
	}
	100%{
		opacity: 0;
		-webkit-transform: translate3d(0,100%,0);
		-ms-transform: translate3d(0,100%,0);
		transform: translate3d(0,100%,0);
	}
}
@keyframes switchFadeIn-out {
	0%{
		opacity: 1;
	}
	100%{
		opacity: 0;
	}
}
@keyframes switchFadeInDown-out {
	0%{
		opacity: 1;
		-webkit-transform: none;
		-ms-transform: none;
		transform: none;
	}
	100%{
		opacity: 0;
		-webkit-transform: translate3d(0,-100%,0);
		-ms-transform: translate3d(0,-100%,0);
		transform: translate3d(0,-100%,0);
	}
}
@keyframes switchSlideInDown-out  {
	0%{
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
	100%{
		-webkit-transform: translateY(-100%);
		-ms-transform: translateY(-100%);
		transform: translateY(-100%);
	}
}
@keyframes switchSlideInUp-out  {
	0%{
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0);
	}
	100%{
		-webkit-transform: translateY(100%);
		-ms-transform: translateY(100%);
		transform: translateY(100%);
		visibility: visible;
	}
}
/*end 退出动画*/.module_599627671 .ModuleHead .HeadCenter{float:none;}
#module_599627671 {
padding:0px;
}
#module_599627671 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627671 .ModuleHead599627671 {
display:none;
}
#module_599627671 >.module_599627671 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627671 >.module_599627671{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627671 .BodyCenter.BodyCenter599627671 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627671 .BodyCenter.BodyCenter599627671 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627671 >.module_599627671 {
margin-top:3.6585%;
margin-left:1.4634%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627671 >.module_599627671 {
margin-top:18.3486%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627671 >.module_599627671 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627671:hover {
border:none;
}
#module_599627671:hover >.module_599627671 {
border-color:#ccc;
}
#module_599627671:hover >.module_599627671 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627671 .ModuleHoverBoxItem_1{background-color:rgba(0,0,0,0);}
#module_599627671 .ModuleHoverBoxItem_1{background-image:none;}
#module_599627671 .ModuleHoverBoxItem_1{background-repeat:no-repeat;}
#module_599627671 .ModuleHoverBoxItem_1{}
#module_599627671 .ModuleHoverBoxItem_1{background-attachment:scroll;}
#module_599627671 .ModuleHoverBoxItem_1{}
#module_599627671 .ModuleHoverBoxItem_1{padding-top:0px;}
#module_599627671 .ModuleHoverBoxItem_1{padding-bottom:0px;}
#module_599627671 .ModuleHoverBoxItem_1{padding-left:0px;}
#module_599627671 .ModuleHoverBoxItem_1{padding-right:0px;}
#module_599627671 .ModuleHoverBoxItem_2{background-color:rgba(0,0,0,0);}
#module_599627671 .ModuleHoverBoxItem_2{background-image:none;}
#module_599627671 .ModuleHoverBoxItem_2{background-repeat:no-repeat;}
#module_599627671 .ModuleHoverBoxItem_2{}
#module_599627671 .ModuleHoverBoxItem_2{background-attachment:scroll;}
#module_599627671 .ModuleHoverBoxItem_2{}
#module_599627671 .ModuleHoverBoxItem_2{padding-top:0px;}
#module_599627671 .ModuleHoverBoxItem_2{padding-bottom:0px;}
#module_599627671 .ModuleHoverBoxItem_2{padding-left:0px;}
#module_599627671 .ModuleHoverBoxItem_2{padding-right:0px;}
#module_599627671 .ModuleHoverBoxItem_2 .ModuleItem{opacity:1}
.module_599627672 .ModuleHead .HeadCenter{float:none;}
#module_599627672 {
padding:0px;
}
#module_599627672 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627672 .ModuleHead599627672 {
display:none;
}
#module_599627672 .BodyCenter.BodyCenter599627672 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627672 >.module_599627672 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627672 >.module_599627672{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627672 .BodyCenter.BodyCenter599627672 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627672 .BodyCenter.BodyCenter599627672 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627672 >.module_599627672 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627672 >.module_599627672 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627672 >.module_599627672 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627672:hover {
border:none;
}
#module_599627672:hover >.module_599627672 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627672 img{border-top-color:transparent;}
#module_599627672 img{border-top-style:none;}
#module_599627672 img{border-top-width:0px;}
#module_599627672 img{border-right-color:transparent;}
#module_599627672 img{border-right-style:none;}
#module_599627672 img{border-right-width:0px;}
#module_599627672 img{border-bottom-color:transparent;}
#module_599627672 img{border-bottom-style:none;}
#module_599627672 img{border-bottom-width:0px;}
#module_599627672 img{border-left-color:transparent;}
#module_599627672 img{border-left-style:none;}
#module_599627672 img{border-left-width:0px;}
#module_599627672 img{border-top-left-radius:0px;}
#module_599627672 img{border-top-right-radius:0px;}
#module_599627672 img{border-bottom-left-radius:0px;}
#module_599627672 img{border-bottom-right-radius:0px;}
#module_599627672 .BodyCenter{text-align: left;}
.module_599627673 .ModuleHead .HeadCenter{float:none;}
#module_599627673 {
padding:0px;
}
#module_599627673 {
position:static;
z-index:0;
top:27px;
left:0px;
width:100%;
height: auto;
}
#module_599627673 .ModuleHead599627673 {
display:none;
}
#module_599627673 .BodyCenter.BodyCenter599627673 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627673 >.module_599627673 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627673 >.module_599627673{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627673 .BodyCenter.BodyCenter599627673 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627673 .BodyCenter.BodyCenter599627673 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627673 >.module_599627673 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627673 >.module_599627673 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627673 >.module_599627673 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627673:hover {
border:none;
}
#module_599627673:hover >.module_599627673 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627673 img{border-top-color:transparent;}
#module_599627673 img{border-top-style:none;}
#module_599627673 img{border-top-width:0px;}
#module_599627673 img{border-right-color:transparent;}
#module_599627673 img{border-right-style:none;}
#module_599627673 img{border-right-width:0px;}
#module_599627673 img{border-bottom-color:transparent;}
#module_599627673 img{border-bottom-style:none;}
#module_599627673 img{border-bottom-width:0px;}
#module_599627673 img{border-left-color:transparent;}
#module_599627673 img{border-left-style:none;}
#module_599627673 img{border-left-width:0px;}
#module_599627673 img{border-top-left-radius:0px;}
#module_599627673 img{border-top-right-radius:0px;}
#module_599627673 img{border-bottom-left-radius:0px;}
#module_599627673 img{border-bottom-right-radius:0px;}
#module_599627673 .BodyCenter{text-align: left;}

.ModuleImageGiant.layout-107 .BodyCenter{
	text-align:center;
}
.ModuleImageGiant.layout-107 img{
	margin: auto;
    max-width: 100%;
	border: none;
    transition: all .5s;
    -moz-transition: all .5s;
    -webkit-transition: all .5s;
    -o-transition: all .5s;
    transition-duration: 500ms;
    -webkit-transition-duration:500ms;
}

.module_599627621 .ModuleHead .HeadCenter{float:none;}
#module_599627621 {
padding:0px;
}
#module_599627621 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627621 .ModuleHead599627621 {
display:none;
}
#module_599627621 .BodyCenter.BodyCenter599627621 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627621 >.module_599627621 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627621 >.module_599627621{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627621 .BodyCenter.BodyCenter599627621 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627621 .BodyCenter.BodyCenter599627621 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627621 >.module_599627621 {
margin-top:5.4430%;
margin-left:42.4051%;
margin-right:8.7342%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627621 >.module_599627621 {
margin-top:5.5046%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627621 >.module_599627621 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627621:hover {
border:none;
}
#module_599627621:hover >.module_599627621 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627621 img{border-top-color:transparent;}
#module_599627621 img{border-top-style:none;}
#module_599627621 img{border-top-width:0px;}
#module_599627621 img{border-right-color:transparent;}
#module_599627621 img{border-right-style:none;}
#module_599627621 img{border-right-width:0px;}
#module_599627621 img{border-bottom-color:transparent;}
#module_599627621 img{border-bottom-style:none;}
#module_599627621 img{border-bottom-width:0px;}
#module_599627621 img{border-left-color:transparent;}
#module_599627621 img{border-left-style:none;}
#module_599627621 img{border-left-width:0px;}
#module_599627621 img{border-top-left-radius:0px;}
#module_599627621 img{border-top-right-radius:0px;}
#module_599627621 img{border-bottom-left-radius:0px;}
#module_599627621 img{border-bottom-right-radius:0px;}
#module_599627621 .BodyCenter{text-align: center;}

/*图文模块 layout 105*/
.ModuleImageTextGiant.layout-105 .row{ margin-left:0; margin-right: 0;}
.ModuleImageTextGiant.layout-105 .imageTextGiant-Container{max-height: 100%; max-width: 100%;/*padding: 10px;*/}
.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .ModuleImageTextContent{padding:0; padding-left: 15px; margin: 0px }
.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .GraphicUpper{height:auto;padding-left: 0;padding-right: 0}
.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .normal_ImgtextBox_content{align-items: center;display: -webkit-box; /* OLD - iOS 6-, Safari 3.1-6 */ display: -moz-box; /* OLD - Firefox 19- (buggy but mostly works) */ display: -ms-flexbox; /* TWEENER - IE 10 */ display: -webkit-flex; /* NEW - Chrome */ display: flex;}
.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .GraphicUpper{text-align: left; overflow: hidden; display: block; text-align:center; position: relative;}
/*.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .GraphicUpper .dummy { padding-top: 100%; }*/
/* .ModuleImageTextGiant.layout-105 .normal_ImgtextBox_content:first-child{ margin-top: 0 !important; }
.ModuleImageTextGiant.layout-105 .normal_ImgtextBox_content:last-child{ margin-bottom: 0 !important; } */


@media only screen and (max-width: 767px) {
	.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .row .col-xs-8{padding: 0 3px;width: 91.333333%;}
}
/*@media only screen and (min-width: 992px) and (max-width: 1199px) {*/
	/*.ModuleImageTextGiant.layout-105 .imageTextGiant-Container{min-width: 100%;}*/
	/*.ModuleImageTextGiant.layout-105 .col-md-2{width: 14.666667%;}*/
/*}*/
/*@media only screen and (min-width: 769px) and (max-width: 991px) {*/
	/*.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .row{width:auto;}*/
/*}*/
@media only screen and (min-width: 768px) {
	.ModuleImageTextGiant.layout-105 .col-lg-2 {
    width: 10%;
	}
	.ModuleImageTextGiant.layout-105 .imageTextGiant-Container .normal_ImgtextBox_content .GraphicUpper .TextImage{width: 100%}
}.module_599627622 .ModuleHead .HeadCenter{float:none;}
#module_599627622 {
padding:0px;
}
#module_599627622 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627622 .ModuleHead599627622 {
display:none;
}
#module_599627622 .BodyCenter.BodyCenter599627622 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627622 >.module_599627622 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627622 >.module_599627622{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627622 .BodyCenter.BodyCenter599627622 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627622 .BodyCenter.BodyCenter599627622 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627622 >.module_599627622 {
margin-top:0.1266%;
margin-left:27.2152%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627622 >.module_599627622 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627622 >.module_599627622 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627622:hover {
border:none;
}
#module_599627622:hover >.module_599627622 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627622 .GraphicUpper .TextImage{border-top-left-radius:0px;}
#module_599627622 .GraphicUpper .TextImage{border-top-right-radius:0px;}
#module_599627622 .GraphicUpper .TextImage{border-bottom-left-radius:0px;}
#module_599627622 .GraphicUpper .TextImage{border-bottom-right-radius:0px;}
#module_599627622 .GraphicUpper{padding-top:0px;}
#module_599627622 .GraphicUpper{padding-bottom:0px;}
#module_599627622 .GraphicUpper{padding-left:0px;}
#module_599627622 .GraphicUpper {padding-right:0px;}
#module_599627622 .GraphicUpper{width:21%;} #module_599627622 .ModuleImageTextContent{width: calc(100% - 21%);}
#module_599627622 .MobileGraphicUpper{width:10%;} #module_599627622 .ModuleMobileImageTextContent{width: calc(100% - 10%);}
#module_599627622 .ModuleImageTextGiantContent{padding-top:25px;}
#module_599627622 .ModuleImageTextGiantContent{padding-bottom:0px;}
#module_599627622 .ModuleImageTextGiantContent{padding-left:25px;}
#module_599627622 .ModuleImageTextGiantContent{padding-right:0px;}
#module_599627622 .normal_ImgtextBox_content{padding-top:0px;}
#module_599627622 .normal_ImgtextBox_content{padding-bottom:0px;}
#module_599627622 .normal_ImgtextBox_content{padding-left:0px;}
#module_599627622 .normal_ImgtextBox_content{padding-right:0px;}
#module_599627622 .normal_ImgtextBox_content{margin-top:0px;margin-bottom:0px;}
#module_599627622 .normal_ImgtextBox_content{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
#module_599627622 .normal_ImgtextBox_content{background-color:transparent;}
#module_599627622 .normal_ImgtextBox_content{background-image:none;}
#module_599627622 .normal_ImgtextBox_content{background-repeat:no-repeat;}
#module_599627622 .normal_ImgtextBox_content{background-position:0% 0%;}
#module_599627622 .normal_ImgtextBox_content{background-attachment:none;}
#module_599627622 .normal_ImgtextBox_content{}
#module_599627622 .normal_ImgtextBox_content{border-top-left-radius:0px;}
#module_599627622 .normal_ImgtextBox_content{border-top-right-radius:0px;}
#module_599627622 .normal_ImgtextBox_content{border-bottom-left-radius:0px;}
#module_599627622 .normal_ImgtextBox_content{border-bottom-right-radius:0px;}
#module_599627622 .normal_ImgtextBox_content{border-top-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content{border-top-style:none;}
#module_599627622 .normal_ImgtextBox_content{border-top-width:0px;}
#module_599627622 .normal_ImgtextBox_content{border-right-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content{border-right-style:none;}
#module_599627622 .normal_ImgtextBox_content{border-right-width:0px;}
#module_599627622 .normal_ImgtextBox_content{border-bottom-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content{border-bottom-style:none;}
#module_599627622 .normal_ImgtextBox_content{border-bottom-width:0px;}
#module_599627622 .normal_ImgtextBox_content{border-left-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content{border-left-style:none;}
#module_599627622 .normal_ImgtextBox_content{border-left-width:0px;}
#module_599627622 .showHandle .btn{font-size:14px;}
#module_599627622 .showHandle .btn{font-family:微软雅黑,Microsoft YaHei;}
#module_599627622 .showHandle .btn{color:#333;}
#module_599627622 .showHandle .btn{font-weight:400;}
#module_599627622 .showHandle .btn{text-decoration:none;}
#module_599627622 .showHandle .btn{font-style:normal;}
#module_599627622 .showHandle div{border-top-color:#ddd !important;}
#module_599627622 .showHandle div{border-top-style:solid !important;}
#module_599627622 .showHandle div{border-top-width:1px !important;}
#module_599627622 .showHandle{background-color:transparent;}
#module_599627622 .showHandle{background-image:none;}
#module_599627622 .showHandle{background-repeat:no-repeat;}
#module_599627622 .showHandle{}
#module_599627622 .showHandle{}
#module_599627622 .showHandle{}
#module_599627622 .normal_ImgtextBox_content:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
#module_599627622 .normal_ImgtextBox_content:hover{border-top-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content:hover{border-top-style:none;}
#module_599627622 .normal_ImgtextBox_content:hover{border-right-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content:hover{border-right-style:none;}
#module_599627622 .normal_ImgtextBox_content:hover{border-bottom-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content:hover{border-bottom-style:none;}
#module_599627622 .normal_ImgtextBox_content:hover{border-left-color:rgba(0,0,0,0);}
#module_599627622 .normal_ImgtextBox_content:hover{border-left-style:none;}
#module_599627622 a{font-size:unset;font-family:unset}

#module_599627613 #Sub599627613_1 {top: 50%;transform: translateY(-50%);}
.module_599627629 .ModuleHead .HeadCenter{float:none;}
#module_599627629 {
padding:0px;
}
#module_599627629 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627629 .ModuleHead599627629 {
display:none;
}
#module_599627629 .BodyCenter.BodyCenter599627629 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627629 >.module_599627629 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627629 >.module_599627629{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627629 .BodyCenter.BodyCenter599627629 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627629 .BodyCenter.BodyCenter599627629 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627629 >.module_599627629 {
margin-top:-8.4222%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627629 >.module_599627629 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627629 >.module_599627629 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627629:hover {
border:none;
}
#module_599627629:hover >.module_599627629 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627629 .ModuleSubContainer{background-color:transparent;}
#module_599627629 .ModuleSubContainer{background-image:none;}
#module_599627629 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627629 .ModuleSubContainer{background-position:0% 0%;}
#module_599627629 .ModuleSubContainer{background-attachment:scroll;}
#module_599627629 .ModuleSubContainer{}
#module_599627629 {!bgVideo!}{bgVideoUrl:}
#module_599627629 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627629 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627629 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627629 {}
/*图文模块 layout 103*/
.ModuleImageTextGiant.layout-103 .imageTextGiant-Container{max-height: 100%; max-width: 100%;padding: 10px;}
.ModuleImageTextGiant.layout-103 .imageTextGiant-Container .GraphicUpper{text-align: center;}
.ModuleImageTextGiant.layout-103 .ModuleImageTextContent{width: 100%;}

.module_599627723 .ModuleHead .HeadCenter{float:none;}
#module_599627723 {
padding:0px;
}
#module_599627723 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627723 .ModuleHead599627723 {
display:none;
}
#module_599627723 .BodyCenter.BodyCenter599627723 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627723 >.module_599627723 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627723 >.module_599627723{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627723 .BodyCenter.BodyCenter599627723 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627723 .BodyCenter.BodyCenter599627723 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627723 >.module_599627723 {
margin-top:30.6410%;
margin-left:48.8462%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627723 >.module_599627723 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627723 >.module_599627723 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627723:hover {
border:none;
}
#module_599627723:hover >.module_599627723 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627723 .GraphicUpper .TextImage{border-top-left-radius:0px;}
#module_599627723 .GraphicUpper .TextImage{border-top-right-radius:0px;}
#module_599627723 .GraphicUpper .TextImage{border-bottom-left-radius:0px;}
#module_599627723 .GraphicUpper .TextImage{border-bottom-right-radius:0px;}
#module_599627723 .GraphicUpper .TextImage{margin-top:0px;}
#module_599627723 .GraphicUpper .TextImage{margin-bottom:10px;}
#module_599627723 .GraphicUpper .TextImage{margin-left:0px;}
#module_599627723 .GraphicUpper .TextImage{margin-right:0px;}
#module_599627723 .TextImage{width:30%;}
#module_599627723 .MobileTextImage{width:24%;}
#module_599627723 .imageTextGiant-Container .GraphicUpper{ text-align:right;}
#module_599627723 .imageTextGiant-Container{padding-top:10px;}
#module_599627723 .imageTextGiant-Container{padding-bottom:10px;}
#module_599627723 .imageTextGiant-Container{padding-left:10px;}
#module_599627723 .imageTextGiant-Container{padding-right:10px;}
#module_599627723 .ModuleImageTextGiantContent{padding-top:0px;}
#module_599627723 .ModuleImageTextGiantContent{padding-bottom:0px;}
#module_599627723 .ModuleImageTextGiantContent{padding-left:0px;}
#module_599627723 .ModuleImageTextGiantContent{padding-right:0px;}
#module_599627723 .imageTextGiant-Container{padding-top:0px;}
#module_599627723 .imageTextGiant-Container{padding-bottom:0px;}
#module_599627723 .imageTextGiant-Container{padding-left:0px;}
#module_599627723 .imageTextGiant-Container{padding-right:0px;}
#module_599627723 .showHandle .btn{font-size:14px;}
#module_599627723 .showHandle .btn{font-family:微软雅黑,Microsoft YaHei;}
#module_599627723 .showHandle .btn{color:#333;}
#module_599627723 .showHandle .btn{font-weight:400;}
#module_599627723 .showHandle .btn{text-decoration:none;}
#module_599627723 .showHandle .btn{font-style:normal;}
#module_599627723 .showHandle div{border-top-color:#ddd !important;}
#module_599627723 .showHandle div{border-top-style:solid !important;}
#module_599627723 .showHandle div{border-top-width:1px !important;}
#module_599627723 .showHandle{background-color:transparent;}
#module_599627723 .showHandle{background-image:none;}
#module_599627723 .showHandle{background-repeat:no-repeat;}
#module_599627723 .showHandle{}
#module_599627723 .showHandle{}
#module_599627723 .showHandle{}
#module_599627723 a{font-size:unset;font-family:unset}
.module_599627662 .ModuleHead .HeadCenter{float:none;}
#module_599627662 {
padding:0px;
}
#module_599627662 {
position:static;
z-index:0;
top:225px;
left:134px;
width:100%;
height: auto;
}
#module_599627662 .ModuleHead599627662 {
display:none;
}
#module_599627662 .BodyCenter.BodyCenter599627662 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627662 >.module_599627662 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627662 >.module_599627662{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627662 .BodyCenter.BodyCenter599627662 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627662 .BodyCenter.BodyCenter599627662 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627662 >.module_599627662 {
margin-top:28.7179%;
margin-left:9.3590%;
margin-right:0.5128%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627662 >.module_599627662 {
margin-top:0.0000%;
margin-left:5.4598%;
margin-right:5.4598%;
margin-bottom:0.0000%;
}
}
#module_599627662 >.module_599627662 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627662:hover {
border:none;
}
#module_599627662:hover >.module_599627662 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627662 .ModuleImageTextGiantContent{margin-top:0px;}
#module_599627662 .ModuleImageTextGiantContent{margin-bottom:0px;}
#module_599627662 .ModuleImageTextGiantContent{margin-left:0px;}
#module_599627662 .ModuleImageTextGiantContent{margin-right:0px;}
#module_599627662 .imageTextGiant-Container{padding-top:0px;}
#module_599627662 .imageTextGiant-Container{padding-bottom:10px;}
#module_599627662 .imageTextGiant-Container{padding-left:10px;}
#module_599627662 .imageTextGiant-Container{padding-right:10px;}
#module_599627662 .showHandle .btn{font-size:14px;}
#module_599627662 .showHandle .btn{font-family:微软雅黑,Microsoft YaHei;}
#module_599627662 .showHandle .btn{color:rgba(0,0,0,0);}
#module_599627662 .showHandle .btn{font-weight:400;}
#module_599627662 .showHandle .btn{text-decoration:none;}
#module_599627662 .showHandle .btn{font-style:normal;}
#module_599627662 .showHandle div{border-top-color:rgba(0,0,0,0) !important;}
#module_599627662 .showHandle div{border-top-style:solid !important;}
#module_599627662 .showHandle div{border-top-width:1px !important;}
#module_599627662 .showHandle{background-color:transparent;}
#module_599627662 .showHandle{background-image:none;}
#module_599627662 .showHandle{background-repeat:no-repeat;}
#module_599627662 .showHandle{}
#module_599627662 .showHandle{}
#module_599627662 .showHandle{}
#module_599627662 a{font-size:unset;font-family:unset}
.module_599627689 .ModuleHead .HeadCenter{float:none;}
#module_599627689 {
padding:0px;
}
#module_599627689 {
position:static;
z-index:0;
top:697px;
left:950px;
width:100%;
height: auto;
}
#module_599627689 .ModuleHead599627689 {
display:none;
}
#module_599627689 >.module_599627689 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627689 >.module_599627689{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627689 .BodyCenter.BodyCenter599627689 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627689 .BodyCenter.BodyCenter599627689 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627689 >.module_599627689 {
margin-top:4.2545%;
margin-left:9.8103%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627689 >.module_599627689 {
margin-top:0.0000%;
margin-left:5.4496%;
margin-right:5.4496%;
margin-bottom:5.4496%;
}
}
#module_599627689 >.module_599627689 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627689:hover {
border:none;
}
#module_599627689:hover >.module_599627689 {
border-color:#ccc;
}
#module_599627689:hover >.module_599627689 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627689 .ModuleHoverBoxItem_1{background-color:rgba(0,0,0,0);}
#module_599627689 .ModuleHoverBoxItem_1{background-image:none;}
#module_599627689 .ModuleHoverBoxItem_1{background-repeat:no-repeat;}
#module_599627689 .ModuleHoverBoxItem_1{}
#module_599627689 .ModuleHoverBoxItem_1{background-attachment:scroll;}
#module_599627689 .ModuleHoverBoxItem_1{}
#module_599627689 .ModuleHoverBoxItem_1{padding-top:0px;}
#module_599627689 .ModuleHoverBoxItem_1{padding-bottom:0px;}
#module_599627689 .ModuleHoverBoxItem_1{padding-left:0px;}
#module_599627689 .ModuleHoverBoxItem_1{padding-right:0px;}
#module_599627689 .ModuleHoverBoxItem_2{background-color:rgba(0,0,0,0);}
#module_599627689 .ModuleHoverBoxItem_2{background-image:none;}
#module_599627689 .ModuleHoverBoxItem_2{background-repeat:no-repeat;}
#module_599627689 .ModuleHoverBoxItem_2{}
#module_599627689 .ModuleHoverBoxItem_2{background-attachment:scroll;}
#module_599627689 .ModuleHoverBoxItem_2{}
#module_599627689 .ModuleHoverBoxItem_2{padding-top:0px;}
#module_599627689 .ModuleHoverBoxItem_2{padding-bottom:0px;}
#module_599627689 .ModuleHoverBoxItem_2{padding-left:0px;}
#module_599627689 .ModuleHoverBoxItem_2{padding-right:0px;}
#module_599627689 .ModuleHoverBoxItem_2 .ModuleItem{opacity:1}
.module_599627690 .ModuleHead .HeadCenter{float:none;}
#module_599627690 {
padding:0px;
}
#module_599627690 {
position:static;
z-index:0;
top:690px;
left:1065px;
width:100%;
height: auto;
}
#module_599627690 .ModuleHead599627690 {
display:none;
}
#module_599627690 .BodyCenter.BodyCenter599627690 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627690 >.module_599627690 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627690 >.module_599627690{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627690 .BodyCenter.BodyCenter599627690 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627690 .BodyCenter.BodyCenter599627690 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627690 >.module_599627690 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627690 >.module_599627690 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627690 >.module_599627690 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627690:hover {
border:none;
}
#module_599627690:hover >.module_599627690 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627690 img{border-top-color:transparent;}
#module_599627690 img{border-top-style:none;}
#module_599627690 img{border-top-width:0px;}
#module_599627690 img{border-right-color:transparent;}
#module_599627690 img{border-right-style:none;}
#module_599627690 img{border-right-width:0px;}
#module_599627690 img{border-bottom-color:transparent;}
#module_599627690 img{border-bottom-style:none;}
#module_599627690 img{border-bottom-width:0px;}
#module_599627690 img{border-left-color:transparent;}
#module_599627690 img{border-left-style:none;}
#module_599627690 img{border-left-width:0px;}
#module_599627690 img{border-top-left-radius:0px;}
#module_599627690 img{border-top-right-radius:0px;}
#module_599627690 img{border-bottom-left-radius:0px;}
#module_599627690 img{border-bottom-right-radius:0px;}
#module_599627690 .BodyCenter{text-align: left;}
.module_599627691 .ModuleHead .HeadCenter{float:none;}
#module_599627691 {
padding:0px;
}
#module_599627691 {
position:static;
z-index:0;
top:27px;
left:0px;
width:100%;
height: auto;
}
#module_599627691 .ModuleHead599627691 {
display:none;
}
#module_599627691 .BodyCenter.BodyCenter599627691 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627691 >.module_599627691 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627691 >.module_599627691{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627691 .BodyCenter.BodyCenter599627691 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627691 .BodyCenter.BodyCenter599627691 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627691 >.module_599627691 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627691 >.module_599627691 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627691 >.module_599627691 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627691:hover {
border:none;
}
#module_599627691:hover >.module_599627691 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627691 img{border-top-color:transparent;}
#module_599627691 img{border-top-style:none;}
#module_599627691 img{border-top-width:0px;}
#module_599627691 img{border-right-color:transparent;}
#module_599627691 img{border-right-style:none;}
#module_599627691 img{border-right-width:0px;}
#module_599627691 img{border-bottom-color:transparent;}
#module_599627691 img{border-bottom-style:none;}
#module_599627691 img{border-bottom-width:0px;}
#module_599627691 img{border-left-color:transparent;}
#module_599627691 img{border-left-style:none;}
#module_599627691 img{border-left-width:0px;}
#module_599627691 img{border-top-left-radius:0px;}
#module_599627691 img{border-top-right-radius:0px;}
#module_599627691 img{border-bottom-left-radius:0px;}
#module_599627691 img{border-bottom-right-radius:0px;}
#module_599627691 .BodyCenter{text-align: left;}

.module_599627727 .ModuleHead .HeadCenter{float:none;}
#module_599627727 {
padding:0px;
}
#module_599627727 {
position:static;
z-index:0;
top:556px;
left:173px;
width:100%;
height: auto;
}
#module_599627727 .ModuleHead599627727 {
display:none;
}
#module_599627727 {
border:none;
}
@media screen and (min-width: 768px){
#module_599627727 >.module_599627727 {
}
}
@media screen and (max-width: 767px){
#module_599627727 >.module_599627727 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}

#module_599627727 img{border-top-color:transparent;}
#module_599627727 img{border-top-style:none;}
#module_599627727 img{border-top-width:0px;}
#module_599627727 img{border-right-color:transparent;}
#module_599627727 img{border-right-style:none;}
#module_599627727 img{border-right-width:0px;}
#module_599627727 img{border-bottom-color:transparent;}
#module_599627727 img{border-bottom-style:none;}
#module_599627727 img{border-bottom-width:0px;}
#module_599627727 img{border-left-color:transparent;}
#module_599627727 img{border-left-style:none;}
#module_599627727 img{border-left-width:0px;}
#module_599627727 img{border-top-left-radius:0px;}
#module_599627727 img{border-top-right-radius:0px;}
#module_599627727 img{border-bottom-left-radius:0px;}
#module_599627727 img{border-bottom-right-radius:0px;}
#module_599627727 .BodyCenter{text-align: center;}


#module_599627613 #Sub599627613_2 {top: 50%;transform: translateY(-50%);}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .addnewhelper{white-space: unset;}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer {display:table;width: 100%;position: relative;table-layout:fixed;}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer.sticky {display:flex}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer > .SubContainer{display:table-cell; vertical-align: top;}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer.sticky > .SubContainer{display:block; position: sticky; top:20px;height: 100% !important; }
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer > .SubContainer.ModuleHoverBoxItem_2{display:none}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .SubPadding{
    height: 100%;
    position: relative;
    display: table-cell;
}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .SubDrag:after{
    display: block;
    content: '';
    width: 1px;
    border-left: 1px dashed rgba(75, 151, 255, 0);
    height: 100%;
    margin: auto;
}
.ModuleGridCustomGiant.layout-101:hover .SubDrag:after{border-left: 1px dashed rgba(75, 151, 255, .8);}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .SubDrag{
    width: 6px;
    cursor: e-resize;
    position: absolute;
    z-index: 99;
    min-height: 66px;
    height: 100%;
    background-color: rgba(75, 151, 255, 0);
}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .SubDrag:hover{background-color: rgba(75, 151, 255, .8);}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .SubDrag:hover:after{border-left: 1px dashed rgba(75, 151, 255, 0);}
.ModuleGridCustomGiant.layout-101 .row {margin-right: 0px;margin-left: 0px;}
.ModuleGridCustomGiant.layout-101 .ModuleGridCol{padding: 0px;}

.ModuleGridCustomGiant.layout-101 .ModuleGridContainer{
    margin-right: auto;
    margin-left: auto;
    padding-right: 0;
    padding-left: 0;
}
.ModuleGridCustomGiant.layout-101{height: 100%;position: relative;}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer{position: relative;}
.ModuleGridCustomGiant.layout-101 .gridBgVideo{display: flex; position: absolute;top: 0;left: 0;width:100%;z-index: 0;}
.ModuleGridCustomGiant.layout-101 .gridBgVideo .bgVideoMask{position: absolute;top: 0;left: 0;right: 0;bottom: 0;background-color: rgb(0,0,0);}
.ModuleGridCustomGiant.layout-101 .gridBgVideo .bgVideo{width: 100%; height: 100%; object-fit: cover; object-position: center center;}
.ModuleGridCustomGiant.layout-101 .gridBgVideo.noBgVideo{display: none;}
.ModuleGridCustomGiant.layout-101 .ModuleSubContainer .ModuleContainer .SubPadding {height: 100%}

@media only screen and (max-width: 767px) {
    .ModuleGridCustomGiant.layout-101 .ModuleSubContainer {display: flex;flex-wrap: wrap;}
    .ModuleGridCustomGiant.layout-101 .ModuleSubContainer > .SubPadding{float:left;height: 10px;}
	.ModuleGridCustomGiant.layout-101 .ModuleSubContainer > .SubContainer{float:left;padding-top:2px;padding-bottom:2px;}
	.ModuleGridCustomGiant.layout-101 .ModuleSubContainer > .SubPadding .SubDrag{display:none;}
    /* padding 宽度 用 5，子容器宽度为 (100% - (列数 - 1) * padding宽度)/列数) */
    /* 12列 */
    .ModuleGridCustomGiant.layout-101 .grid-col-12 > .SubPadding{width:5px;}
    .ModuleGridCustomGiant.layout-101 .grid-col-12 > .SubContainer{width:calc((100% - 55px)/12)!important;}
    /* 11列 */
    .ModuleGridCustomGiant.layout-101 .grid-col-11 > .SubPadding{width:5px;}
    .ModuleGridCustomGiant.layout-101 .grid-col-11 > .SubContainer{width:calc((100% - 50px)/11)!important;}
    /* 10列 */
    .ModuleGridCustomGiant.layout-101 .grid-col-10 > .SubPadding{width:5px;}
    .ModuleGridCustomGiant.layout-101 .grid-col-10 > .SubContainer{width:calc((100% - 45px)/10)!important;}
    /* 9列 */
    .ModuleGridCustomGiant.layout-101 .grid-col-9 > .SubPadding{width:5px;}
    .ModuleGridCustomGiant.layout-101 .grid-col-9 > .SubContainer{width:calc((100% - 40px)/10)!important;}
    /* 8列 */
    .ModuleGridCustomGiant.layout-101 .grid-col-8 > .SubPadding{width:5px;}
    .ModuleGridCustomGiant.layout-101 .grid-col-8 > .SubContainer{width:calc((100% - 35px)/8)!important;}
    /* 7列 */
    .ModuleGridCustomGiant.layout-101 .grid-col-7 > .SubPadding{width:5px;}
    .ModuleGridCustomGiant.layout-101 .grid-col-7 > .SubContainer{width:calc((100% - 30px)/7)!important;}
	/* 6列 */
	.ModuleGridCustomGiant.layout-101 .grid-col-6 > .SubPadding{width:5px;}
	.ModuleGridCustomGiant.layout-101 .grid-col-6 > .SubContainer{width:calc((100% - 25px)/6)!important;}
	/* 5列 */
	.ModuleGridCustomGiant.layout-101 .grid-col-5 > .SubPadding{width:5px;}
	.ModuleGridCustomGiant.layout-101 .grid-col-5 > .SubPadding5{display:none;}
	.ModuleGridCustomGiant.layout-101 .grid-col-5 > .SubContainer{width:calc((100% - 20px)/5)!important;}
	/* 4列 */
	.ModuleGridCustomGiant.layout-101 .grid-col-4 > .SubPadding{width:5px;}
	.ModuleGridCustomGiant.layout-101 .grid-col-4 > .SubPadding4{display:none;}
    .ModuleGridCustomGiant.layout-101 .grid-col-4 > .SubPadding8{display:none;}
	.ModuleGridCustomGiant.layout-101 .grid-col-4 > .SubContainer{width:calc((100% - 15px)/4)!important;}
	/* 3列 */
	.ModuleGridCustomGiant.layout-101 .grid-col-3 > .SubPadding{width:5px;}
	.ModuleGridCustomGiant.layout-101 .grid-col-3 > .SubPadding3{display:none;}
    .ModuleGridCustomGiant.layout-101 .grid-col-3 > .SubPadding6{display:none;}
	.ModuleGridCustomGiant.layout-101 .grid-col-3 > .SubContainer{width:calc((100% - 10px)/3)!important;}
	/* 2列 */
	.ModuleGridCustomGiant.layout-101 .grid-col-2 > .SubPadding{width:5px;}
	.ModuleGridCustomGiant.layout-101 .grid-col-2 > .SubPadding2{display:none;}
	.ModuleGridCustomGiant.layout-101 .grid-col-2 > .SubPadding4{display:none;}
    .ModuleGridCustomGiant.layout-101 .grid-col-2 > .SubPadding6{display:none;}
    .ModuleGridCustomGiant.layout-101 .grid-col-2 > .SubPadding8{display:none;}
	.ModuleGridCustomGiant.layout-101 .grid-col-2 > .SubContainer{width:calc((100% - 5px)/2)!important;}
	/* 1列 无padding */
	.ModuleGridCustomGiant.layout-101 .grid-col-1 > .SubPadding{width:0px;display:none;}
	.ModuleGridCustomGiant.layout-101 .grid-col-1 > .SubContainer{width:100%!important;}
    .ModuleGridCustomGiant.layout-101 .ModuleSubContainer.sticky > .SubContainer{
        position:inherit !important;
        top:unset !important;
    }
}

.ModuleGridCustomGiant.layout-101 .ModuleGridBorder{border:0px !important;padding: 0 !important;}
.module_599627623 .ModuleHead .HeadCenter{float:none;}
#module_599627623 {
padding:0px;
}
#module_599627623 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627623 .ModuleHead599627623 {
display:none;
}
#module_599627623 .BodyCenter.BodyCenter599627623 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627623 >.module_599627623 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627623 >.module_599627623{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627623 .BodyCenter.BodyCenter599627623 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627623 .BodyCenter.BodyCenter599627623 {
padding-top:11.7816%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627623 >.module_599627623 {
margin-top:0.0000%;
margin-left:0.5342%;
margin-right:0.5342%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627623 >.module_599627623 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627623 >.module_599627623 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627623:hover {
border:none;
}
#module_599627623:hover >.module_599627623 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627623 .ModuleSubContainer{background-color:transparent;}
#module_599627623 .ModuleSubContainer{background-image:none;}
#module_599627623 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627623 .ModuleSubContainer{background-position:0% 0%;}
#module_599627623 .ModuleSubContainer{background-attachment:scroll;}
#module_599627623 .ModuleSubContainer{}
#module_599627623 {!bgVideo!}{bgVideoUrl:none}
#module_599627623 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627623 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627623 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627623 {max-width:none;width:100%;margin: auto;}
.ModuleGridContainer.ModuleGridContainer599627623 .SubPadding {width:0px;}
.module_599627624 .ModuleHead .HeadCenter{float:none;}
#module_599627624 {
padding:0px;
}
#module_599627624 {
position:static;
z-index:0;
top:849px;
left:168px;
width:100%;
height: auto;
}
#module_599627624 .ModuleHead599627624 {
display:none;
}
#module_599627624 .BodyCenter.BodyCenter599627624 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627624 >.module_599627624 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627624 >.module_599627624{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627624 .BodyCenter.BodyCenter599627624 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627624 .BodyCenter.BodyCenter599627624 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627624 >.module_599627624 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:1.1364%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627624 >.module_599627624 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627624 >.module_599627624 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627624:hover {
border:none;
}
#module_599627624:hover >.module_599627624 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627624 img{border-top-color:transparent;}
#module_599627624 img{border-top-style:none;}
#module_599627624 img{border-top-width:0px;}
#module_599627624 img{border-right-color:transparent;}
#module_599627624 img{border-right-style:none;}
#module_599627624 img{border-right-width:0px;}
#module_599627624 img{border-bottom-color:transparent;}
#module_599627624 img{border-bottom-style:none;}
#module_599627624 img{border-bottom-width:0px;}
#module_599627624 img{border-left-color:transparent;}
#module_599627624 img{border-left-style:none;}
#module_599627624 img{border-left-width:0px;}
#module_599627624 img{border-top-left-radius:0px;}
#module_599627624 img{border-top-right-radius:0px;}
#module_599627624 img{border-bottom-left-radius:0px;}
#module_599627624 img{border-bottom-right-radius:0px;}
#module_599627624 .BodyCenter{text-align: center;}

.module_599627625 .ModuleHead .HeadCenter{float:none;}
#module_599627625 {
padding:0px;
}
#module_599627625 {
position:static;
z-index:0;
top:903px;
left:212px;
width:100%;
height: auto;
}
#module_599627625 .ModuleHead599627625 {
display:none;
}
#module_599627625 .BodyCenter.BodyCenter599627625 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627625 >.module_599627625 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627625 >.module_599627625{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627625 .BodyCenter.BodyCenter599627625 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627625 .BodyCenter.BodyCenter599627625 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627625 >.module_599627625 {
margin-top:1.1364%;
margin-left:0.0000%;
margin-right:1.1364%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627625 >.module_599627625 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627625 >.module_599627625 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627625:hover {
border:none;
}
#module_599627625:hover >.module_599627625 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627625 img{border-top-color:transparent;}
#module_599627625 img{border-top-style:none;}
#module_599627625 img{border-top-width:0px;}
#module_599627625 img{border-right-color:transparent;}
#module_599627625 img{border-right-style:none;}
#module_599627625 img{border-right-width:0px;}
#module_599627625 img{border-bottom-color:transparent;}
#module_599627625 img{border-bottom-style:none;}
#module_599627625 img{border-bottom-width:0px;}
#module_599627625 img{border-left-color:transparent;}
#module_599627625 img{border-left-style:none;}
#module_599627625 img{border-left-width:0px;}
#module_599627625 img{border-top-left-radius:0px;}
#module_599627625 img{border-top-right-radius:0px;}
#module_599627625 img{border-bottom-left-radius:0px;}
#module_599627625 img{border-bottom-right-radius:0px;}
#module_599627625 .BodyCenter{text-align: center;}

.ModuleProductListGiant.layout-130 .pro-container{transform: translateZ(0); }
.ModuleProductListGiant.layout-130 .pro-container li{float: left; cursor: pointer; transition: all 0.3s ease 0s; margin: 0 30px 30px 0; padding: 0;}
.ModuleProductListGiant.layout-130 .pro-item .content {display: block; position: relative;}
.ModuleProductListGiant.layout-130 .pro-container li .pro-img {display: block; overflow: hidden; position: relative;}
.ModuleProductListGiant.layout-130 .pro-container li .pro-img .dummy {padding-top:100%;}
.ModuleProductListGiant.layout-130 .pro-container li .pro-img img {position: absolute; top: 0; bottom: 0; left: 0; right: 0; margin: auto;}
/* .ModuleProductListGiant.layout-130 .pro-container li:hover .pro-img img {transform: scale(1.1);} */
.ModuleProductListGiant.layout-130 .pro-shade{visibility: hidden; position: absolute;top:0; background-color: rgba(0, 0, 0, .5); z-index: 33; width: 100%; height: 100%; }
.ModuleProductListGiant.layout-130 .pro-item:hover{transform: scale(1.07);z-index: 999;}
.ModuleProductListGiant.layout-130 .pro-item:hover .pro-shade{visibility: visible;}
.ModuleProductListGiant.layout-130 .pro-item:hover .item_info{transform:translateY(0);}
.ModuleProductListGiant.layout-130 .pro-item:hover .pro-name{opacity:1;}
.ModuleProductListGiant.layout-130 .pro-item:hover .pro-desc{opacity:1;}
.ModuleProductListGiant.layout-130 .pro-item:hover .pro-btn{transform:translateY(0); opacity: 1}
.ModuleProductListGiant.layout-130 .pro-btn:hover{ transform:rotate(90deg) !important;transition: .2s;}
.ModuleProductListGiant.layout-130 .pro-btn{ opacity: 0;  transform: translateY(-120px);  transition: .36s ease;position: relative; margin: 0 auto;width: 48px; height: 48px; background: #4caf50; border-radius: 50%;margin-bottom:40px }
.ModuleProductListGiant.layout-130 .pro-btn .icon-tianjia{margin-top: 2px;font-size: 34px; position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);}
.item_info{ padding: 0 10%;transform: translateY(-120px); transition: .36s ease;}
.ModuleProductListGiant.layout-130 .pro-item .pro-shade-box{width: 100%; position: absolute; top:50%;left:50%; transform: translate(-50%,-50%); }
.ModuleProductListGiant.layout-130 .pro-item .amin-pc{opacity: 0; transition: all .3s ease;}
.ModuleProductListGiant.layout-130 .pro-item .pro-name{ color: #333;line-height: 1.2; text-align:center; overflow:hidden; text-overflow:ellipsis; word-break: break-word !important;}
.ModuleProductListGiant.layout-130 .pro-item .pro-desc{ color: #666;line-height: 1.2; text-align:center; overflow:hidden; text-overflow:ellipsis; word-break: break-word !important;}
.ModuleProductListGiant.layout-130 .pc-container{ display: block;}
.ModuleProductListGiant.layout-130 .mobile-container{ display: none;}
@media screen and (min-width: 768px) {
    .ModuleProductListGiant.layout-130 .pro-container li.col-lg-1 {width: 100%;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(n).col-lg-1 {margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-lg-2 {width: 50%;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(2n).col-lg-2 {margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-lg-3 {width: calc(100% / 3);}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(3n).col-lg-3 {margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-lg-4 {width: 25%;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(4n).col-lg-4 {margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-lg-5 {width:20%;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(5n).col-lg-5 {margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-lg-6 {width: calc(100% / 6);}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(6n).col-lg-6{ margin-right: 0 !important;}
}
@media screen and (max-width: 767px) {
    .ModuleProductListGiant.layout-130 .pro-container li {margin-bottom: 5px !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-xs-1 {width: 100% ;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(n).col-xs-1{margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-xs-2 {width: 50%;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(2n).col-xs-2{margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pro-container li.col-xs-3 {width: 30% ;}
    .ModuleProductListGiant.layout-130 .pro-container li:nth-child(3n).col-xs-3{margin-right: 0 !important;}
    .ModuleProductListGiant.layout-130 .pc-container{ display: none;}
    .ModuleProductListGiant.layout-130 .mobile-container{ display: block;}
    .ModuleProductListGiant.layout-130 .pro-item:hover{transform: unset;z-index: 999;}
    .ModuleProductListGiant.layout-130 .pro-item .item_info{padding: 0 5%;transform:translateY(0);}
}
.ModuleProductListGiant.layout-130 .pagerGiant .inputer{outline: 0;}.module_620309116 .ModuleHead .HeadCenter{float:none;}
#module_620309116 {
padding:0px;
}
#module_620309116 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_620309116 .ModuleHead620309116 {
display:none;
}
#module_620309116 .BodyCenter.BodyCenter620309116 {
background:none;background-color:rgba(0,0,0,0);
}
#module_620309116 >.module_620309116 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_620309116 >.module_620309116{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_620309116 .BodyCenter.BodyCenter620309116 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_620309116 .BodyCenter.BodyCenter620309116 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_620309116 >.module_620309116 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_620309116 >.module_620309116 {
margin-top:0.0000%;
margin-left:5.7471%;
margin-right:5.7471%;
margin-bottom:8.6207%;
}
}
#module_620309116 >.module_620309116 {box-shadow: 0px 0px 0px 0px #ccc}
#module_620309116:hover {
border:none;
}
#module_620309116:hover >.module_620309116 {
border-color:#ccc;
}
#module_620309116:hover >.module_620309116 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_620309116 .pro-item{background-color:rgba(0, 0, 0, 0);}
#module_620309116 .pro-item{background-image:none;}
#module_620309116 .pro-item{background-repeat:no-repeat;}
#module_620309116 .pro-item{}
#module_620309116 .pro-item{}
#module_620309116 .pro-item{}
#module_620309116 .pro-item{margin-bottom:5px; margin-right:5px;} @media screen and (max-width: 767px) { #module_620309116 .pro-container li.col-xs-1{width:100% !important;} #module_620309116 .pro-container li.col-xs-2{width:calc((100% - 5px) / 2) !important;} #module_620309116 .pro-container li.col-xs-3{width:calc((100% - 5px * 2) / 3) !important;}} #module_620309116 .pro-container li.col-lg-2{width:calc((100% - 5px) / 2);} #module_620309116 .pro-container li.col-lg-3{width:calc((100% - 5px * 2) / 3);} #module_620309116 .pro-container li.col-lg-4{width:calc((100% - 5px * 3) / 4);} #module_620309116 .pro-container li.col-lg-5{width:calc((100% - 5px * 4) / 5);} #module_620309116 .pro-container li.col-lg-6{width:calc((100% - 5px * 5) / 6);}
#module_620309116 .pro-img .dummy{padding-top:66.67%;}
#module_620309116 .pro-img img{ object-fit:unset;}
#module_620309116 .pro-img img{ width:auto;}
#module_620309116 .pro-img img{ height:auto;}
#module_620309116 .pro-name{font-size:12px;}
#module_620309116 .pro-name{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pro-name{color:rgb(127, 127, 127);}
#module_620309116 .pro-name{font-weight:normal;}
#module_620309116 .pro-name{text-decoration:none;}
#module_620309116 .pro-name{}
#module_620309116 .pro-name{margin-top:10px;}
#module_620309116 .pro-name{margin-bottom:0px;}
#module_620309116 .pro-desc{font-size:14px;}
#module_620309116 .pro-desc{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pro-desc{color:#BFBFBF;}
#module_620309116 .pro-desc{font-weight:normal;}
#module_620309116 .pro-desc{text-decoration:none;}
#module_620309116 .pro-desc{}
#module_620309116 .pro-desc{margin-top:10px;}
#module_620309116 .pro-desc{margin-bottom:10px;}
#module_620309116 .pro-btn .icon-tianjia{font-size:32px;}
#module_620309116 .pro-btn{background-color:#FFD231;}
#module_620309116 .pro-btn .icon-tianjia{color:#E1E1E1;}
#module_620309116 .pro-btn{margin-top:0px;}
#module_620309116 .pro-btn{margin-bottom:30px;}
#module_620309116 .pro-btn{height:48px;width:48px}
#module_620309116 .pro-shade{background-color:rgba(0, 0, 0, .5);}
#module_620309116 .pro-shade{background-image:none;}
#module_620309116 .pro-shade{background-repeat:no-repeat;}
#module_620309116 .pro-shade{}
#module_620309116 .pro-shade{}
#module_620309116 .pro-shade{}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li{font-size:14px;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li,#module_620309116 .productListOrderCtrlTab .summary .totalProduct{color:#333;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li{font-weight:normal;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li{text-decoration:none;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li{font-style:normal;}
#module_620309116 .pagerGiant .text{font-size:12px;}
#module_620309116 .pagerGiant .text{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pagerGiant .text{color:#333;}
#module_620309116 .pagerGiant .text{}
#module_620309116 .pagerGiant .text{}
#module_620309116 .pagerGiant .text{}
#module_620309116 .pagerGiant .current,#module_620309116 .pagerGiant .num,#module_620309116 .pagerGiant .inputer{width:30px}
#module_620309116 .pagerGiant .current,#module_620309116 .pagerGiant .num,#module_620309116 .pagerGiant .inputer{height:30px}
#module_620309116 .pagerGiant .num{font-size:12px;}
#module_620309116 .pagerGiant .num{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pagerGiant .num{color:#333;}
#module_620309116 .pagerGiant .num{}
#module_620309116 .pagerGiant .num{}
#module_620309116 .pagerGiant .num{}
#module_620309116 .pagerGiant .submit{width:55px}
#module_620309116 .pagerGiant .submit{height:30px}
#module_620309116 .pagerGiant .current,#module_620309116 .pagerGiant .num,#module_620309116 .pagerGiant .inputer,#module_620309116 .pagerGiant .submit{border-top-left-radius:0px;}
#module_620309116 .pagerGiant .current,#module_620309116 .pagerGiant .num,#module_620309116 .pagerGiant .inputer,#module_620309116 .pagerGiant .submit{border-top-right-radius:0px;}
#module_620309116 .pagerGiant .current,#module_620309116 .pagerGiant .num,#module_620309116 .pagerGiant .inputer,#module_620309116 .pagerGiant .submit{border-bottom-left-radius:0px;}
#module_620309116 .pagerGiant .current,#module_620309116 .pagerGiant .num,#module_620309116 .pagerGiant .inputer,#module_620309116 .pagerGiant .submit{border-bottom-right-radius:0px;}
#module_620309116 .pagerGiant .next,#module_620309116 .pagerGiant .splitline{color:#ccc;}
#module_620309116 .pagerGiant .num{background-color:#fff;}
#module_620309116 .pagerGiant .num{border-top-color:#ccc;}
#module_620309116 .pagerGiant .num{border-top-style:solid;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{border-top-width:1px;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .inputer{border-bottom-color:#ccc;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .inputer{border-bottom-style:solid;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer{border-bottom-width:1px;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .inputer{border-left-color:#ccc;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .inputer{border-left-style:solid;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer{border-left-width:1px;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .inputer{border-right-color:#ccc;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .inputer{border-right-style:solid;}
#module_620309116 .pagerGiant .num, #module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer{border-right-width:1px;}
#module_620309116 .page-more a{font-size:16px;}
#module_620309116 .page-more a{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .page-more a{color:#666 !important;}
#module_620309116 .page-more a{font-weight:normal;}
#module_620309116 .page-more a{text-decoration:none;}
#module_620309116 .page-more a{font-style:normal;}
#module_620309116 .page-more a{background-color:#fff;}
#module_620309116 .page-more a{border-top-color:#eee;}
#module_620309116 .page-more a{border-top-style:solid;}
#module_620309116 .page-more a{border-top-width:2px;}
#module_620309116 .page-more a{border-bottom-color:#eee;}
#module_620309116 .page-more a{border-bottom-style:solid;}
#module_620309116 .page-more a{border-bottom-width:2px;}
#module_620309116 .page-more a{border-left-color:#eee;}
#module_620309116 .page-more a{border-left-style:solid;}
#module_620309116 .page-more a{border-left-width:2px;}
#module_620309116 .page-more a{border-right-color:#eee;}
#module_620309116 .page-more a{border-right-style:solid;}
#module_620309116 .page-more a{border-right-width:2px;}
#module_620309116 .choosed-tab, #module_620309116 .setting-screening-criteria{font-size:12px;}
#module_620309116 .choosed-tab{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .choosed-tab, #module_620309116 .setting-screening-criteria{color:#ffd231;}
#module_620309116 .choosed-tab{}
#module_620309116 .choosed-tab{}
#module_620309116 .choosed-tab{}
#module_620309116 .selected-light,#module_620309116 .setting-all, #module_620309116 .setting-left-bar, #module_620309116 .setting-pred-warp .setting-collapse, #module_620309116 .setting-pred-warp .setting-collapse .icpng{font-size:12px;} #module_620309116 .setting-pred-warp .setting-collapse{flex: 0 0 calc(12px * 4);} #module_620309116 .setting-right-temp ul.FilterPc {height: calc(12px + 36px);}
#module_620309116 .selected-light,#module_620309116 .setting-all{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .selected-light,#module_620309116 .setting-left-bar,#module_620309116 .setting-reset,#module_620309116 .setting-pred-warp .setting-collapse,#module_620309116 .giantroom .btn-more{color:#000;}
#module_620309116 .selected-light{}
#module_620309116 .selected-light,#module_620309116 .setting-all{}
#module_620309116 .selected-light,#module_620309116 .setting-all{}
#module_620309116 .setting-right-temp ul li, #module_620309116 .choosed-tab{border-top-left-radius:2px;}
#module_620309116 .setting-right-temp ul li, #module_620309116 .choosed-tab{border-top-right-radius:2px;}
#module_620309116 .setting-right-temp ul li, #module_620309116 .choosed-tab{border-bottom-left-radius:2px;}
#module_620309116 .setting-right-temp ul li, #module_620309116 .choosed-tab{border-bottom-right-radius:2px;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab{border-top-color:#d6d6d6;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab{border-top-style:solid;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-top-width:1px;} #module_620309116 .giantroom {margin-top: calc(-16px - 1px);}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-bottom-color:#d6d6d6;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-bottom-style:solid;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-bottom-width:1px;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-left-color:#d6d6d6;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-left-style:solid;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-left-width:1px;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-right-color:#d6d6d6;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-right-style:solid;}
#module_620309116 .pred-filter, #module_620309116 .choosed-tab, #module_620309116 .giantroom .btn-more{border-right-width:1px;}
#module_620309116 .setting-pred-warp{border-top-color:#d6d6d6;}
#module_620309116 .setting-left-bar{background-color:#fafafa;}
#module_620309116 .setting-left-bar{width:100px} #module_620309116 .setting-right-temp ul.FilterPc{width: calc( 100% - 100px );}
#module_620309116 .setting-right-temp ul li{background-color:rgba(255, 255, 255, 0);}
#module_620309116 .choosed-tab .icsmall{color:#999;}
#module_620309116 .setting-right-temp ul li{margin-right:10px;}
#module_620309116 .pro-item:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
#module_620309116 .pro-item:hover .pro-name{font-size:12px;}
#module_620309116 .pro-item:hover .pro-name{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pro-item:hover .pro-name{color:#FFD231;}
#module_620309116 .pro-item:hover .pro-name{font-weight:normal;}
#module_620309116 .pro-item:hover .pro-name{text-decoration:none;}
#module_620309116 .pro-item:hover .pro-name{}
#module_620309116 .pro-item:hover .pro-desc{font-size:14px;}
#module_620309116 .pro-item:hover .pro-desc{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pro-item:hover .pro-desc{color:#BFBFBF;}
#module_620309116 .pro-item:hover .pro-desc{font-weight:normal;}
#module_620309116 .pro-item:hover .pro-desc{text-decoration:none;}
#module_620309116 .pro-item:hover .pro-desc{}
#module_620309116 .pro-btn:hover .icon-tianjia{font-size:32px;}
#module_620309116 .pro-btn:hover .icon-tianjia{color:#E1E1E1;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{font-size:14px;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{color:#666;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{font-weight:normal;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{text-decoration:none;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{font-style:normal;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{background-color:#ffd231;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{background-image:none;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{background-repeat:no-repeat;}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{}
#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li:hover,#module_620309116 .productListOrderCtrlTab .orderCtrlPanel li.selected{}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{font-size:12px;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{color:#fff;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit{background-color:#ffd231;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-top-color:#ffd231;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-top-style:solid;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-bottom-color:#ffd231;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-bottom-style:solid;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-left-color:#ffd231;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-left-style:solid;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-right-color:#ffd231;}
#module_620309116 .pagerGiant .current, #module_620309116 .pagerGiant .submit, #module_620309116 .pagerGiant .inputer:hover{border-right-style:solid;}
#module_620309116 .page-more a:hover{font-size:16px;}
#module_620309116 .page-more:hover a{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .page-more a:hover{color:#666 !important;}
#module_620309116 .page-more a:hover {font-weight:normal;}
#module_620309116 .page-more a:hover {text-decoration:none;}
#module_620309116 .page-more a:hover {font-style:normal;}
#module_620309116 .page-more a:hover{background-color:#fff;}
#module_620309116 .page-more a:hover{border-top-color:#eee;}
#module_620309116 .page-more a:hover{border-top-style:solid;}
#module_620309116 .page-more a:hover{border-bottom-color:#eee;}
#module_620309116 .page-more a:hover{border-bottom-style:solid;}
#module_620309116 .page-more a:hover{border-left-color:#eee;}
#module_620309116 .page-more a:hover{border-left-style:solid;}
#module_620309116 .page-more a:hover{border-right-color:#eee;}
#module_620309116 .page-more a:hover{border-right-style:solid;}
#module_620309116 .selected-light:hover,#module_620309116 .selected-light.active,#module_620309116 .items-active{font-size:12px;}
#module_620309116 .selected-light:hover,#module_620309116 .selected-light.active,#module_620309116 .items-active{font-family:微软雅黑,Microsoft YaHei;}
#module_620309116 .selected-light:hover,#module_620309116 .selected-light.active,#module_620309116 .items-active{color:#ffd231;} #module_620309116 .items-active{border-color:#ffd231;} #module_620309116 .bean-finish{background:#ffd231;}
#module_620309116 .selected-light:hover,#module_620309116 .selected-light.active,#module_620309116 .items-active{}
#module_620309116 .selected-light:hover,#module_620309116 .selected-light.active,#module_620309116 .items-active{}
#module_620309116 .selected-light:hover,#module_620309116 .selected-light.active,#module_620309116 .items-active{}
#module_620309116 .setting-left-bar:hover{background-color:#fafafa;}
#module_620309116 .setting-right-temp ul li:hover, #module_620309116 .setting-right-temp ul li.active{background-color:rgba(255, 255, 255, 0);}
#module_620309116 .choosed-tab .icsmall:hover{color:#999;}
.ModuleCustomFormGiant.layout-101 .submitbtnbox{display: flex;}
.ModuleCustomFormGiant.layout-101 .smsvaldatebox,
.ModuleCustomFormGiant.layout-101 .frist_item,
.ModuleCustomFormGiant.layout-101 input.input-text-color,
.ModuleCustomFormGiant.layout-101 .customFormDatetime,
.ModuleCustomFormGiant.layout-101 .pcCitybox select
{
    height: 38px;
    line-height: 38px;
}
/*新验证码开始*/
.ModuleCustomFormGiant.layout-101 .vciline{
    height: 2px;
    position: absolute;
    left: 0;
    bottom: 0;
}
.ModuleCustomFormGiant.layout-101 .VCClose:hover .VCLine1 {
     display: none;
}
.ModuleCustomFormGiant.layout-101 .VCClose:hover .VCLine2 {
   transform: rotate(180deg);
   transition:all 0.5s;
}
.ModuleCustomFormGiant.layout-101 .VCClose{
    position: relative;
    margin-right: 20px;
    width: 30px;
    float: right;
    margin-top: 17px;
    height: 30px;
    cursor: pointer;
}
.ModuleCustomFormGiant.layout-101 .VCLine1{
     border-top:#ccc solid 2px;
     width: 18px;
     transform: rotate(45deg);
     right: 0;
     position: absolute;
     top: 9px;

}
.ModuleCustomFormGiant.layout-101 .VCLine2{
    border-top:#ccc solid 2px;
    width: 18px;
    transform: rotate(-45deg);
    right: 0;
    position: absolute;
    top: 9px;

}
.ModuleCustomFormGiant.layout-101 .VCTitle{
    color:#333;
    font-size:20px;
    font-weight: bold;
    margin-top: 41px;
    margin-bottom: 25px;

}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .vcbtn{
     margin-top: 22px;
     text-align: right;
}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .vcbtn1{

    width: 80px;
    height: 36px;
    background: #1e88e5;
    border-radius: 18px;
    line-height: 36px;
    text-align: center;
    color: #fff;
    display: inline-block;
    margin-left: 10px;
    cursor: pointer;
    font-size: 16px;
}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .vcbtn2{
    width: 80px;
    height: 36px;
    background: #fff;
    border-radius: 18px;
    line-height: 36px;
    text-align: center;
    color: #666666 ;
    border: 1px #ccc solid;
    display: inline-block;
    cursor: pointer;
    font-size: 16px;
}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .vcinput::placeholder{
    color:#999999
}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .vcinput{
     border:none;
     padding: 20px 0 ;
     background: #fff !important;
     border-bottom: solid #ccc 1px;
     color: #666;
}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .changebtn{
      text-align: right;
      display: block;

}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv .VerificationCode{
    width: 100%;
    height: 100px;
}
.ModuleCustomFormGiant.layout-101 .VerificationCodecontext{
    padding:0 33px;
}
.ModuleCustomFormGiant.layout-101 .VerificationCodediv{

    position: absolute;
    bottom: 0;
    z-index: 999999999;
    background: #fff;
    transform: translate(-50%,-50%);
    top:auto;
    left: 50%;
    max-width: 350px;
    height: 380px;
    border-radius: 6px;
    box-shadow: 9.9px 9.9px 49px 0px rgba(0,0,0,0.11);
    display: none;
    width: 100%;
}
/*新验证码结束*/
.ModuleCustomFormGiant.layout-101 .title {
    text-align: center;
    line-height: 50px;
    height: 50px;
    color: #333;
    font-size: 18px;
    margin-bottom: 14px;
    font-weight: normal;
    min-height: 30px;
}

.ModuleCustomFormGiant.layout-101 .from-Describe {
    font-size: 14px;
    line-height: 1.5;
    padding-bottom: 15px;
    display: flex;
    align-content: center;
    align-items: center;
}

.ModuleCustomFormGiant.layout-101 input[type=text] {
    line-height: 38px;
    font-size: 14px;
    border: 1px solid #e5e5e5;
    background: #fbfbfb;
    width: 100%;
    padding: 0 10px 0 16px;
    box-sizing: border-box;
    min-height: 30px;
    border-radius: 0;
}
.ModuleCustomFormGiant.layout-101 textarea {
    -webkit-appearance: none;
}
.ModuleCustomFormGiant.layout-101 .pcCitybox {
    display: -webkit-flex;
    /* Safari */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 34px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.ModuleCustomFormGiant.layout-101 .customFormicon {
    width: 20px;
    height: 20px;
    margin-right: 5px;
    margin-top: -5px;
    display: inline-block !important
}

/* .ModuleCustomFormGiant.layout-101 .Select_Simulate li:hover {
    color: #fff;
} */
.ModuleCustomFormGiant.layout-101 label{
    line-height: 2;
}
.ModuleCustomFormGiant.layout-101 .Select_Simulate.layout1 .frist_item {

    height: 38px;
    line-height: 38px;
}
.ModuleCustomFormGiant.layout-101 .smsvaldatebox{
    height: 40px;
    line-height: 40px;
}
.ModuleCustomFormGiant.layout-101  .Browse-file {
    height: 38px;
    line-height: 38px;
 }
 .ModuleCustomFormGiant.layout-101 .Browse-img {
    height: 80px;

 }
.ModuleCustomFormGiant.layout-101 .multi-row-text
{
    border-radius: 0;
}
.ModuleCustomFormGiant.layout-101 .Form-list {
    width: 100%;
    padding-top: 20px;
}
.ModuleCustomFormGiant.layout-101 .areabox .area{
  height: 38px;
  line-height: 38px;
}
.ModuleCustomFormGiant.layout-101 .Select_Simulate .frist_item{
    padding: 0 16px;
}
.ModuleCustomFormGiant.layout-101 .Select_Simulate .xilaImg {
    float: right;
    margin-top: 16px;
    /* margin-right:16px */
}

.ModuleCustomFormGiant.layout-101 .pcCitybox select {
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    -moz-flex-grow: 1;
    margin-right: 10px;
    height: 34px;
    line-height: 34px;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    padding: 0 10px;
    cursor: pointer;
    background: url(/skinp/modules/ModuleCustomFormGiant/images/arrow1.png) no-repeat center right #fbfbfb;
    font-size: 14px;
    border: 1px solid #e5e5e5;
}

.ModuleCustomFormGiant.layout-101 .cityval {
    box-sizing: border-box;
    background: #fbfbfb;
    width: 100%;
    text-align: left;
}

.ModuleCustomFormGiant.layout-101 .choose-time-mobile {
    font-size: 14px;
}

.ModuleCustomFormGiant.layout-101 .pcCitybox .Couty-select {
    margin-right: 0;
}

.ModuleCustomFormGiant.layout-101 .submitbtn {
    line-height: 40px;
    height: 40px;
    text-align: center;
    margin-top: 8px
}

.ModuleCustomFormGiant.layout-101 input.VerificationCodeinput {
    width: 188px;
    line-height: 34px;
    margin-right: 10px;
    float: left;
    min-height: 36px;
}

.ModuleCustomFormGiant.layout-101 .VerificationCode {
    width: 86px;
    height: 34px;
    float: left;
}

.ModuleCustomFormGiant.layout-101 .Browse-file-input {
    flex-grow: 1;
    -webkit-flex-grow: 1;
    -ms-flex-grow: 1;
    -moz-flex-grow: 1;
    margin-right: 10px;
}

.ModuleCustomFormGiant.layout-101 .file-box {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.ModuleCustomFormGiant.layout-101 .Browse-file {
    /* width: 90px; height: 32px; line-height: 32px;*/
    color: #333;
    background: #f8f8f8;
    border: 1px solid #eee;
    font-size: 12px;
    text-align: center;
    flex-grow: 0;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    -moz-flex-grow: 0;
    box-sizing: border-box;
    cursor: pointer;
}

.ModuleCustomFormGiant.layout-101 .Browse-file:hover {
    color: #666;
    background: #eee;
    border: 1px solid #ccc;
}

.ModuleCustomFormGiant.layout-101 .input-text-color {
    color: #333;
}

.ModuleCustomFormGiant.layout-101 .Describ-text-color {
    color: #666;
}

.ModuleCustomFormGiant.layout-101 .submibtn-color {
    color: #fff;
    background: #000;
    cursor: pointer;
}
.ModuleCustomFormGiant.layout-101 .icon-riqixuanze{
    padding-right: 10px;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #b3b6b7;
}
.ModuleCustomFormGiant.layout-101 .submibtn-color:hover {
    color: #fff;
    background: #666;
}

.ModuleCustomFormGiant.layout-101 .multi-row-text, .ModuleCustomFormGiant.layout-101 .Select_Simulate .frist_item, .ModuleCustomFormGiant.layout-101 .choose-time {
    background: #fbfbfb;
}

.ModuleCustomFormGiant.layout-101 input[type=radio]:checked, .ModuleCustomFormGiant.layout-101 input[type=checkbox]:checked {
    background: #f10215;
    border: 1px solid #f10215;
}

.ModuleCustomFormGiant.layout-101 label {
    font-weight: normal;
    color: #666
}
.ModuleCustomFormGiant.layout-101 .verticalalign{
    margin-bottom: 12px;
}
.ModuleCustomFormGiant.layout-101  .customform-upload-img-preview{
    border-radius:0
}
.ModuleCustomFormGiant.layout-101  .in-formList-checkbox{
    display: flex;
    flex-wrap: wrap;
}

.ModuleCustomFormGiant.layout-101 .in-formList-checkbox span {
    width: calc(100% - 34px);
    display: initial;
    /* vertical-align: middle; */
    min-width: 36px;
    flex:1;
    word-break: break-all;
}

@media screen and (max-width: 767px) {

    .ModuleCustomFormGiant.layout-101 .verticalalign  span {
        width: calc(100% - 34px);
        display: initial;
        vertical-align: middle;
        min-width: 36px;
    }
    .ModuleCustomFormGiant.layout-101 input[type=text],
    .ModuleCustomFormGiant.layout-101 .smsvaldatebox,
    .ModuleCustomFormGiant.layout-101 .cityval,
    .ModuleCustomFormGiant.layout-101 .choose-time {
        line-height: 38px !important;
        height: 38px !important;
        min-height: 38px;
    }
    .ModuleCustomFormGiant.layout-101 input.VerificationCodeinput {
        width: 120px;
    }
    .ModuleCustomFormGiant.layout-101 .VerificationCode {
        height: 40px
    }
    .ModuleCustomFormGiant.layout-101 .ModuleCustomFormGiant .changebtn {
        line-height: 40px;
        min-height: 40px;
    }
    .ModuleCustomFormGiant.layout-101  .describe{ margin-top:10px}
    .ModuleCustomFormGiant.layout-101 .customFormTextarea{width:100%}
}

/*  新增*/

.ModuleCustomFormGiant.layout-101 .cityPickerBoxwb,.ModuleCustomFormGiant.layout-101 .cityPickerBox .cityPickerInput {
    height: 38px
}

.ModuleCustomFormGiant.layout-101 .select_box {
    display: none;
}

.ModuleCustomFormGiant.layout-101 .UploadFileSpan {
    line-height: 30px;
    font-size: 12px;
    /* border: 1px solid #e5e5e5;
    background: #fbfbfb; */
    padding: 0 10px;
    box-sizing: border-box;
    min-height: 30px;
    width: 316px;
    display: none;
    padding-left: 0px;
}

.ModuleCustomFormGiant.layout-101  .iconfont-fujian {
    font-size: 19px !important;
}
.ModuleCustomFormGiant.layout-101  .imgclose {
    right: -9px;
    position: absolute;
    top: -10px;
}
.module_9988620309116 .ModuleHead .HeadCenter{float:none;}
#module_9988620309116 {
padding:0px;
}
#module_9988620309116 {
}
#module_9988620309116 .ModuleHead0 {
display:block;
}

#module_9988620309116 .InsidePage-list-content span,#module_9988620309116 .in-formList-checkbox span{font-size:px;}
#module_9988620309116 .InsidePage-list-content span,#module_9988620309116 .in-formList-checkbox span{font-family:;}
#module_9988620309116 .InsidePage-list-content span,#module_9988620309116 .in-formList-checkbox span{color:;}
#module_9988620309116 .InsidePage-list-content span,#module_9988620309116 .in-formList-checkbox span{}
#module_9988620309116 .InsidePage-list-content span,#module_9988620309116 .in-formList-checkbox span{}
#module_9988620309116 .InsidePage-list-content span,#module_9988620309116 .in-formList-checkbox span{}
#module_9988620309116 .title{font-size:px;}
#module_9988620309116 .title{font-family:;}
#module_9988620309116 .title{color:;}
#module_9988620309116 .title{}
#module_9988620309116 .title{}
#module_9988620309116 .title{}
#module_9988620309116 .title{background-color:;}
#module_9988620309116 .title{}
#module_9988620309116 .title{}
#module_9988620309116 .title{}
#module_9988620309116 .title{}
#module_9988620309116 .title{}
#module_9988620309116 .title{height:px;line-height:px;}
#module_9988620309116 .title{text-align:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{background-color:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-top-color:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-top-style:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-top-width:px;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-right-color:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-right-style:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-right-width:px;}
#module_9988620309116 .smsvcode,#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-bottom-color:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-bottom-style:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-bottom-width:px;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-left-color:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-left-style:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 .customFormCheckbox,#module_9988620309116 input[type=radio],#module_9988620309116.customFormDatetime,#module_9988620309116 input.input-text-color,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .multi-row-text,#module_9988620309116 .cityval{border-left-width:px;}
#module_9988620309116 .smsbtn,#module_9988620309116 .customFormTextarea,#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 input.input-text-color,#module_9988620309116.customFormDatetime,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .cityval{border-top-left-radius:px;}
#module_9988620309116 .smsbtn,#module_9988620309116 .customFormTextarea,#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 input.input-text-color,#module_9988620309116.customFormDatetime,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .cityval{border-top-right-radius:px;}
#module_9988620309116 .smsbtn,#module_9988620309116 .customFormTextarea,#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 input.input-text-color,#module_9988620309116.customFormDatetime,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .cityval{border-bottom-left-radius:px;}
#module_9988620309116 .smsbtn,#module_9988620309116 .customFormTextarea,#module_9988620309116 .smsvcode,#module_9988620309116 .frist_item,#module_9988620309116 .VerificationCodeinput,#module_9988620309116 input.input-text-color,#module_9988620309116.customFormDatetime,#module_9988620309116 .Browse-file,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select,#module_9988620309116 .cityval{border-bottom-right-radius:px;}
#module_9988620309116 .Describ-text-color{font-size:px;}
#module_9988620309116 .Describ-text-color{font-family:;}
#module_9988620309116 .Describ-text-color{color:;}
#module_9988620309116 .Describ-text-color{}
#module_9988620309116 .Describ-text-color{}
#module_9988620309116 .Describ-text-color{}
#module_9988620309116 .smsvcode,#module_9988620309116 .smsbtn,#module_9988620309116 .input-text-color,#module_9988620309116 .cityval{font-size:px;}
#module_9988620309116 .smsvcode,#module_9988620309116 .smsbtn,#module_9988620309116 .input-text-color,#module_9988620309116 .cityval{font-family:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .input-text-color,#module_9988620309116 .cityval{color:;}
#module_9988620309116 .smsvcode,#module_9988620309116 .smsbtn,#module_9988620309116 .input-text-color,#module_9988620309116 .cityval{}
#module_9988620309116 .smsvcode,#module_9988620309116 .smsbtn,#module_9988620309116 .input-text-color,#module_9988620309116 .cityval{}
#module_9988620309116 .smsvcode,#module_9988620309116 .smsbtn,#module_9988620309116 .input-text-color,#module_9988620309116 .cityval{}
#module_9988620309116 .smsvaldatebox,#module_9988620309116 .frist_item,#module_9988620309116 input.input-text-color,#module_9988620309116 .customFormDatetime,#module_9988620309116 .pcCitybox select{line-height:px;height:px;}
#module_9988620309116 .input-text-color::placeholder,#module_9988620309116 .smsvcode::placeholder{font-size:px;}
#module_9988620309116 .input-text-color::placeholder,#module_9988620309116 .smsvcode::placeholder{font-family:;}
#module_9988620309116 .input-text-color::placeholder,#module_9988620309116 .smsvcode::placeholder{color:;}
#module_9988620309116 .input-text-color::placeholder,#module_9988620309116 .smsvcode::placeholder{}
#module_9988620309116 .input-text-color::placeholder,#module_9988620309116 .smsvcode::placeholder{}
#module_9988620309116 .input-text-color::placeholder,#module_9988620309116 .smsvcode::placeholder{}
#module_9988620309116 .submitbtn{font-size:px;}
#module_9988620309116 .submitbtn{font-family:;}
#module_9988620309116 .submitbtn{color:;}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{background-color:;} #module_9988620309116 input[type=radio]:checked, #module_9988620309116 input[type=checkbox]:checked{background:;border:1px solid } 
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtn{}
#module_9988620309116 .submitbtnbox{justify-content:;}
#module_9988620309116 .submitbtn{width:%;}
#module_9988620309116 .submitbtn{height:px;line-height:px;}
#module_9988620309116 .submitbtn{border-top-left-radius:px;}
#module_9988620309116 .submitbtn{border-top-right-radius:px;}
#module_9988620309116 .submitbtn{border-bottom-left-radius:px;}
#module_9988620309116 .submitbtn{border-bottom-right-radius:px;}
#module_9988620309116 .vcbtn1{background-color:;}
#module_9988620309116 .vcbtn1{color:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-top-color:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-top-style:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-right-color:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-right-style:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-bottom-color:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-bottom-style:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-left-color:;}
#module_9988620309116 .frist_item:hover,#module_9988620309116 .smsvcode:hover, #module_9988620309116 .VerificationCodeinput:hover,#module_9988620309116 .customFormCheckbox:hover,#module_9988620309116 input[type=radio]:hover,#module_9988620309116 input.input-text-color:hover,#module_9988620309116.customFormDatetime:hover,#module_9988620309116 .Browse-file:hover,#module_9988620309116 .customFormDatetime:hover,#module_9988620309116 .pcCitybox select:hover,#module_9988620309116 .multi-row-text:hover,#module_9988620309116 .cityval:hover{border-left-style:;}
#module_9988620309116 .submitbtn:hover{font-size:px;}
#module_9988620309116 .submitbtn:hover{font-family:;}
#module_9988620309116 .submitbtn:hover{color:;}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{background-color:;}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{}
#module_9988620309116 .submitbtn:hover{}


.ModuleGridContainer.ModuleGridContainer599627623 #Sub599627623_1 {width:29.9676%;}
.module_599627626 .ModuleHead .HeadCenter{float:none;}
#module_599627626 {
padding:0px;
}
#module_599627626 {
position:static;
z-index:0;
top:891px;
left:676px;
width:100%;
height: auto;
}
#module_599627626 .ModuleHead599627626 {
display:none;
}
#module_599627626 .BodyCenter.BodyCenter599627626 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627626 >.module_599627626 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627626 >.module_599627626{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627626 .BodyCenter.BodyCenter599627626 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627626 .BodyCenter.BodyCenter599627626 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627626 >.module_599627626 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627626 >.module_599627626 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627626 >.module_599627626 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627626:hover {
border:none;
}
#module_599627626:hover >.module_599627626 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627626 img{border-top-color:transparent;}
#module_599627626 img{border-top-style:none;}
#module_599627626 img{border-top-width:0px;}
#module_599627626 img{border-right-color:transparent;}
#module_599627626 img{border-right-style:none;}
#module_599627626 img{border-right-width:0px;}
#module_599627626 img{border-bottom-color:transparent;}
#module_599627626 img{border-bottom-style:none;}
#module_599627626 img{border-bottom-width:0px;}
#module_599627626 img{border-left-color:transparent;}
#module_599627626 img{border-left-style:none;}
#module_599627626 img{border-left-width:0px;}
#module_599627626 img{border-top-left-radius:0px;}
#module_599627626 img{border-top-right-radius:0px;}
#module_599627626 img{border-bottom-left-radius:0px;}
#module_599627626 img{border-bottom-right-radius:0px;}
#module_599627626 .BodyCenter{text-align: center;}

.ModuleGridContainer.ModuleGridContainer599627623 #Sub599627623_2 {width:28.4017%;}
.module_599627627 .ModuleHead .HeadCenter{float:none;}
#module_599627627 {
padding:0px;
}
#module_599627627 {
position:static;
z-index:0;
top:165px;
left:1250px;
width:100%;
height: auto;
}
#module_599627627 .ModuleHead599627627 {
display:none;
}
#module_599627627 .BodyCenter.BodyCenter599627627 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627627 >.module_599627627 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627627 >.module_599627627{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627627 .BodyCenter.BodyCenter599627627 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627627 .BodyCenter.BodyCenter599627627 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627627 >.module_599627627 {
margin-top:0.0000%;
margin-left:0.8186%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627627 >.module_599627627 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627627 >.module_599627627 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627627:hover {
border:none;
}
#module_599627627:hover >.module_599627627 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627627 img{border-top-color:transparent;}
#module_599627627 img{border-top-style:none;}
#module_599627627 img{border-top-width:0px;}
#module_599627627 img{border-right-color:transparent;}
#module_599627627 img{border-right-style:none;}
#module_599627627 img{border-right-width:0px;}
#module_599627627 img{border-bottom-color:transparent;}
#module_599627627 img{border-bottom-style:none;}
#module_599627627 img{border-bottom-width:0px;}
#module_599627627 img{border-left-color:transparent;}
#module_599627627 img{border-left-style:none;}
#module_599627627 img{border-left-width:0px;}
#module_599627627 img{border-top-left-radius:0px;}
#module_599627627 img{border-top-right-radius:0px;}
#module_599627627 img{border-bottom-left-radius:0px;}
#module_599627627 img{border-bottom-right-radius:0px;}
#module_599627627 .BodyCenter{text-align: center;}

.module_599627628 .ModuleHead .HeadCenter{float:none;}
#module_599627628 {
padding:0px;
}
#module_599627628 {
position:static;
z-index:0;
top:608px;
left:1069px;
width:100%;
height: auto;
}
#module_599627628 .ModuleHead599627628 {
display:none;
}
#module_599627628 .BodyCenter.BodyCenter599627628 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627628 >.module_599627628 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627628 >.module_599627628{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627628 .BodyCenter.BodyCenter599627628 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627628 .BodyCenter.BodyCenter599627628 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627628 >.module_599627628 {
margin-top:0.8186%;
margin-left:0.8186%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627628 >.module_599627628 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627628 >.module_599627628 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627628:hover {
border:none;
}
#module_599627628:hover >.module_599627628 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627628 img{border-top-color:transparent;}
#module_599627628 img{border-top-style:none;}
#module_599627628 img{border-top-width:0px;}
#module_599627628 img{border-right-color:transparent;}
#module_599627628 img{border-right-style:none;}
#module_599627628 img{border-right-width:0px;}
#module_599627628 img{border-bottom-color:transparent;}
#module_599627628 img{border-bottom-style:none;}
#module_599627628 img{border-bottom-width:0px;}
#module_599627628 img{border-left-color:transparent;}
#module_599627628 img{border-left-style:none;}
#module_599627628 img{border-left-width:0px;}
#module_599627628 img{border-top-left-radius:0px;}
#module_599627628 img{border-top-right-radius:0px;}
#module_599627628 img{border-bottom-left-radius:0px;}
#module_599627628 img{border-bottom-right-radius:0px;}
#module_599627628 .BodyCenter{text-align: center;}

.ModuleGridContainer.ModuleGridContainer599627623 #Sub599627623_3 {width:41.630700000000004%;}

#module_599627613 #Sub599627613_3 {top: 0;transform: translateY(0);}
.module_599627663 .ModuleHead .HeadCenter{float:none;}
#module_599627663 {
padding:0px;
}
#module_599627663 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627663 .ModuleHead599627663 {
display:none;
}
#module_599627663 .BodyCenter.BodyCenter599627663 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627663 >.module_599627663 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627663 >.module_599627663{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627663 .BodyCenter.BodyCenter599627663 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_599627663 .BodyCenter.BodyCenter599627663 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627663 >.module_599627663 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_599627663 >.module_599627663 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627663 >.module_599627663 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627663:hover {
border:none;
}
#module_599627663:hover >.module_599627663 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627663 .ModuleSubContainer{background-color:transparent;}
#module_599627663 .ModuleSubContainer{background-image:none;}
#module_599627663 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627663 .ModuleSubContainer{background-position:0% 0%;}
#module_599627663 .ModuleSubContainer{background-attachment:scroll;}
#module_599627663 .ModuleSubContainer{}
#module_599627663 {!bgVideo!}{bgVideoUrl:none}
#module_599627663 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627663 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627663 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627663 {}
.module_599627664 .ModuleHead .HeadCenter{float:none;}
#module_599627664 {
padding:0px;
}
#module_599627664 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627664 .ModuleHead599627664 {
display:none;
}
#module_599627664 .BodyCenter.BodyCenter599627664 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627664 >.module_599627664 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627664 >.module_599627664{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627664 .BodyCenter.BodyCenter599627664 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627664 .BodyCenter.BodyCenter599627664 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627664 >.module_599627664 {
margin-top:2.4615%;
margin-left:3.0769%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627664 >.module_599627664 {
margin-top:0.0000%;
margin-left:5.9946%;
margin-right:48.5014%;
margin-bottom:0.0000%;
}
}
#module_599627664 >.module_599627664 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627664:hover {
border:none;
}
#module_599627664:hover >.module_599627664 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627664 img{border-top-color:transparent;}
#module_599627664 img{border-top-style:none;}
#module_599627664 img{border-top-width:0px;}
#module_599627664 img{border-right-color:transparent;}
#module_599627664 img{border-right-style:none;}
#module_599627664 img{border-right-width:0px;}
#module_599627664 img{border-bottom-color:transparent;}
#module_599627664 img{border-bottom-style:none;}
#module_599627664 img{border-bottom-width:0px;}
#module_599627664 img{border-left-color:transparent;}
#module_599627664 img{border-left-style:none;}
#module_599627664 img{border-left-width:0px;}
#module_599627664 img{border-top-left-radius:0px;}
#module_599627664 img{border-top-right-radius:0px;}
#module_599627664 img{border-bottom-left-radius:0px;}
#module_599627664 img{border-bottom-right-radius:0px;}
#module_599627664 .BodyCenter{text-align: center;}

.ModuleCommonClsGiant.layout-105 .one-classify-box{white-space:nowrap;overflow-x:auto;overflow-y: hidden;font-size: 0;}
.ModuleCommonClsGiant.layout-105 .one-classify-box.show{white-space:normal;}
.ModuleCommonClsGiant.layout-105 .one-classify-box.show .main-class-item{margin-bottom: 5px;}
/*到手机应该隐藏*/
.ModuleCommonClsGiant.layout-105 .one-classify-box::-webkit-scrollbar{height:4px}
.ModuleCommonClsGiant.layout-105 .one-classify-box::-webkit-scrollbar-thumb {height:4px;width: 40px;background-color: rgba(94,94,94,.5);border-radius: 2px}
.ModuleCommonClsGiant.layout-105 .one-classify-box::-webkit-scrollbar-track-piece{height:8px;width: 40px;background-color: #fff}
.ModuleCommonClsGiant.layout-105 .main-class-item{background: #fff;border-top:0;color:#666;width: auto;display:inline-flex;align-content: center;
  justify-content: center;
  justify-items: center;
  align-items: center;
  float:none;
}
.ModuleCommonClsGiant.layout-105  .ItemCount .main-class-item {
  float:left;
}
.ModuleCommonClsGiant.layout-105 .main-class-text{
  font-size:12px;width:100%;padding: 0 10px;color:#666;overflow: hidden;   text-align: center;
 }

.ModuleCommonClsGiant.layout-105 .main-class-item.active{background: #303135}
.ModuleCommonClsGiant.layout-105 .main-class-item.active .main-class-text{color:#fff;}


/*PC屏幕*/
@media (min-width: 768px){
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-1 {width: 100% !important; }
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-2 {width: calc((100% - 5px) / 2) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-2 {width: calc((100% - 5px) / 2) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-3 {width: calc((100% - 5px) / 3) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-4 {width: calc((100% - 5px) / 4) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-5 {width: calc((100% - 5px) / 5) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-6 {width: calc((100% - 5px) / 6) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-7 {width: calc((100% - 5px) / 7) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-8 {width: calc((100% - 5px) / 8) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-9 {width: calc((100% - 5px) / 9) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-10 {width: calc((100% - 5px) / 10) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-11 {width: calc((100% - 5px) / 11) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-12 {width: calc((100% - 5px) / 12) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-2:nth-child(2n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-3:nth-child(3n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-4:nth-child(4n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-5:nth-child(5n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-6:nth-child(6n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-7:nth-child(7n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-8:nth-child(8n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-9:nth-child(9n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-10:nth-child(10n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-11:nth-child(11n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-lg-11:nth-child(12n) {margin-right: 0 !important;}
}


@media (max-width: 767px) {
  .ModuleCommonClsGiant.layout-105 .main-class-text {
    font-size: 12px;
    line-height: 30px;
    color: #666;
    display: inline-block;
    vertical-align: middle;
    overflow: hidden;
    padding: 0 5px;
    text-align: center;
  }
  .ModuleCommonClsGiant.layout-105 .one-classify-box::-webkit-scrollbar {  display: none!important;  width: 0px;  height: 0px; }
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-1 {width: 100% !important; }
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-2:nth-child(2n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-3:nth-child(3n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-4:nth-child(4n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-5:nth-child(5n) {margin-right: 0 !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-2 {width: calc((100% - 5px) / 2) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-3 {width: calc((100% - 5px * 2) / 3) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-4 {width: calc((100% - 5px * 3) / 4) !important;}
  .ModuleCommonClsGiant.layout-105 .ItemCount li.col-xs-5 {width: calc((100% - 5px * 4) / 5) !important;}

}.module_599627670 .ModuleHead .HeadCenter{float:none;}
#module_599627670 {
padding:0px;
}
#module_599627670 {
position:static;
z-index:0;
top:307px;
left:1123px;
width:100%;
height: auto;
}
#module_599627670 .ModuleHead599627670 {
display:none;
}
#module_599627670 .BodyCenter.BodyCenter599627670 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627670 >.module_599627670 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627670 >.module_599627670{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627670 .BodyCenter.BodyCenter599627670 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627670 .BodyCenter.BodyCenter599627670 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627670 >.module_599627670 {
margin-top:1.6465%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627670 >.module_599627670 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:19.8868%;
margin-bottom:0.0000%;
}
}
#module_599627670 >.module_599627670 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627670:hover {
border:none;
}
#module_599627670:hover >.module_599627670 {
border-color:#ccc;
}
#module_599627670:hover >.module_599627670 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627670 .one-classify-box{text-align:right;}
#module_599627670 .main-class-item{background-color:rgba(0,0,0,0);}
#module_599627670 .main-class-item{background-image:none;}
#module_599627670 .main-class-item{background-repeat:no-repeat;}
#module_599627670 .main-class-item{}
#module_599627670 .main-class-item{}
#module_599627670 .main-class-item{}
#module_599627670 .main-class-item{height:40px;}
#module_599627670 .main-class-item{border-top-color:rgb(63, 63, 63);}
#module_599627670 .main-class-item{border-top-style:solid;}
#module_599627670 .main-class-item{border-top-width:1px;}
#module_599627670 .main-class-item{border-right-color:rgb(63, 63, 63);}
#module_599627670 .main-class-item{border-right-style:solid;}
#module_599627670 .main-class-item{border-right-width:1px;}
#module_599627670 .main-class-item{border-bottom-color:rgb(63, 63, 63);}
#module_599627670 .main-class-item{border-bottom-style:solid;}
#module_599627670 .main-class-item{border-bottom-width:1px;}
#module_599627670 .main-class-item{border-left-color:rgb(63, 63, 63);}
#module_599627670 .main-class-item{border-left-style:solid;}
#module_599627670 .main-class-item{border-left-width:1px;}
#module_599627670 .main-class-item{border-top-left-radius:0px;}
#module_599627670 .main-class-item{border-top-right-radius:0px;}
#module_599627670 .main-class-item{border-bottom-left-radius:0px;}
#module_599627670 .main-class-item{border-bottom-right-radius:0px;}
#module_599627670 .main-class-item{margin-right:9px;} @media (max-width: 767px){#module_599627670 .ItemCount li.col-xs-2 {width: calc((100% - 9px) / 2) !important;}#module_599627670 .ItemCount li.col-xs-3 {width: calc((100% - 9px * 2) / 3) !important;}#module_599627670 .ItemCount li.col-xs-4 {width: calc((100% - 9px * 3) / 4) !important;}#module_599627670 .ItemCount li.col-xs-5 {width: calc((100% - 9px * 4) / 5) !important;}} @media (min-width: 768px){#module_599627670 .ItemCount li.col-lg-2 {width: calc((100% - 9px) / 2) !important;}#module_599627670 .ItemCount li.col-lg-3 {width: calc((100% - 9px * 2) / 3) !important;}#module_599627670 .ItemCount li.col-lg-4 {width: calc((100% - 9px * 3) / 4) !important;}#module_599627670 .ItemCount li.col-lg-5 {width: calc((100% - 9px * 4) / 5) !important;}#module_599627670 .ItemCount li.col-lg-6 {width: calc((100% - 9px * 5) / 6) !important;}#module_599627670 .ItemCount li.col-lg-7 {width: calc((100% - 9px * 6) / 7) !important;}#module_599627670 .ItemCount li.col-lg-8 {width: calc((100% - 9px * 7) / 8) !important;}#module_599627670 .ItemCount li.col-lg-9 {width: calc((100% - 9px * 8) / 9) !important;}#module_599627670 .ItemCount li.col-lg-10 {width: calc((100% - 9px * 9) / 10) !important;}#module_599627670 .ItemCount li.col-lg-11 {width: calc((100% - 9px * 10) / 11) !important;}#module_599627670 .ItemCount li.col-lg-12 {width: calc((100% - 9px * 11) / 12) !important;}}
#module_599627670 .main-class-item{margin-Bottom:5px;} 
#module_599627670 .main-class-text{font-size:12px;}
#module_599627670 .main-class-text{font-family:微软雅黑,Microsoft YaHei;}
#module_599627670 .main-class-text{color:rgb(165, 165, 165);}
#module_599627670 .main-class-text{font-weight:normal;}
#module_599627670 .main-class-text{text-decoration:none;}
#module_599627670 .main-class-text{font-style:normal;}
#module_599627670 .main-class-item{width:100px;width:100;}
#module_599627670 .main-class-text{padding-left:10px;}
#module_599627670 .main-class-text{padding-right:10px}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-top-color:transparent;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-top-style:solid;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-right-color:transparent;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-right-style:solid;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-bottom-color:transparent;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-bottom-style:solid;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-left-color:transparent;}
#module_599627670 .main-class-item:hover,#module_599627670 .main-class-item.active{border-left-style:solid;}
#module_599627670 .main-class-item.active{background-color:rgb(251, 188, 44);}
#module_599627670 .main-class-item.active{background-image:none;}
#module_599627670 .main-class-item.active{background-repeat:no-repeat;}
#module_599627670 .main-class-item.active{}
#module_599627670 .main-class-item.active{}
#module_599627670 .main-class-item.active{}
#module_599627670 .main-class-item.active .main-class-text{font-size:12px;}
#module_599627670 .main-class-item.active .main-class-text{font-family:微软雅黑,Microsoft YaHei;}
#module_599627670 .main-class-item.active .main-class-text{color:#fff;}
#module_599627670 .main-class-item.active .main-class-text{font-weight:normal;}
#module_599627670 .main-class-item.active .main-class-text{text-decoration:none;}
#module_599627670 .main-class-item.active .main-class-text{font-style:normal;}


/*layout-108*/
.ModuleNewsListGiant.layout-108 .news-item{ padding: 0 10px;border-bottom: 1px solid #eeeeee;overflow: hidden;line-height: 50px;height: 50px;}
/*.ModuleNewsListGiant.layout-108 .news-item .news-tit .news-title{ float: left;font-size:14px;}*/
/*.ModuleNewsListGiant.layout-108 .news-item .news-tit time{ display: block; float: right; font-size: 12px; color: #666666;}*/
.ModuleNewsListGiant.layout-108 .news-item .news-tit.nodata {display: flex;}
.ModuleNewsListGiant.layout-108 .news-item .news-tit .news-title{ font-size:14px;display: inline-block;width: calc(100% - 120px);}
.ModuleNewsListGiant.layout-108 .news-item .news-tit time{ width: auto;;float: right;}
/*.ModuleNewsListGiant.layout-108 .news-item:hover{ background: #f5f5f5;}*/
.ModuleNewsListGiant.layout-108 .news-item:hover{ border-bottom: 1px solid rgba(0,0,0,0);}
.ModuleNewsListGiant.layout-108 .news-prefix{float: left;}


@media (max-width: 767px) {
    .ModuleNewsListGiant.layout-108 .news-item {
        width: 100%;
        margin: 0 auto;
    }


    .ModuleNewsListGiant.layout-108 .news-item .news-tit .news-title {
        font-size: 14px;
        display: inline-block;
        width: calc(100% - 105px);
    }

    .ModuleNewsListGiant.layout-108 .news-item .news-tit time {
        font-size: 12px;
        width: auto;
    }
}

@media (min-width: 768px) {
    .ModuleNewsListGiant.layout-108 .news-item {
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
    }
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-1 {width: 100%;margin-right: 0;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-2 { width: 46%;float: left; margin-right: 8%;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-2:nth-child(2n){ margin-right: 0%;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-3 {float: left;width: 31.3%;margin-right: 3%;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-3:nth-child(3n) {margin-right: 0;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-4 {float: left;width: 22.7%;margin-right: 3%;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-4:nth-child(4n) {margin-right: 0;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-5 {float: left;width: 17.6%;margin-right: 3%;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-5:nth-child(5n) {margin-right: 0;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-6 {float: left;width: 14.1%;margin-right: 3%;}
    .ModuleNewsListGiant.layout-108 .news-item.containergrid-6:nth-child(6n) {margin-right: 0;}
}

/*置顶推荐*/
.ModuleNewsListGiant.layout-108 .staFun{
    border: 1px solid #e50b1c;
    font-size: 12px;
    padding: 0px 2px;
    color: #e50b1c;
    font-weight: bold;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
}
.ModuleNewsListGiant.layout-108 .pagerGiant .inputer{outline: 0;}

.module_599627669 .ModuleHead .HeadCenter{float:none;}
#module_599627669 {
padding:0px;
}
#module_599627669 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_599627669 .ModuleHead599627669 {
display:none;
}
#module_599627669 .BodyCenter.BodyCenter599627669 {
background:none;background-color:rgb(21, 21, 21);
}
#module_599627669 >.module_599627669 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627669 >.module_599627669{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627669 .BodyCenter.BodyCenter599627669 {
padding-top:3.2931%;
padding-left:3.2931%;
padding-right:3.2931%;
padding-bottom:3.2931%;
}
}
@media screen and (max-width: 767px){
#module_599627669 .BodyCenter.BodyCenter599627669 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627669 >.module_599627669 {
margin-top:2.9586%;
margin-left:0.0000%;
margin-right:1.2486%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627669 >.module_599627669 {
margin-top:8.6207%;
margin-left:5.7471%;
margin-right:5.7471%;
margin-bottom:0.0000%;
}
}
#module_599627669 >.module_599627669 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627669:hover {
border:none;
}
#module_599627669:hover >.module_599627669 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627669 .news-item.even{background-color:transparent;}
#module_599627669 .news-item.even{background-image:none;}
#module_599627669 .news-item.even{background-repeat:no-repeat;}
#module_599627669 .news-item.even{}
#module_599627669 .news-item.even{}
#module_599627669 .news-item.even{}
#module_599627669 .news-item.odd{background-color:transparent;}
#module_599627669 .news-item.odd{background-image:none;}
#module_599627669 .news-item.odd{background-repeat:no-repeat;}
#module_599627669 .news-item.odd{}
#module_599627669 .news-item.odd{}
#module_599627669 .news-item.odd{}
#module_599627669 .news-item{height:50px;line-height:50px;}
#module_599627669 .news-title{font-size:14px;}
#module_599627669 .news-title{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .news-title{color:rgb(191, 191, 191);}
#module_599627669 .news-title{font-weight:normal;}
#module_599627669 .news-title{text-decoration:none;}
#module_599627669 .news-title{font-style:normal;}
#module_599627669 .news-title{margin-top:0px;}
#module_599627669 .news-title{margin-bottom:0px;}
#module_599627669 .news-title{margin-left:0px;}
#module_599627669 .news-title{margin-right:0px;}
#module_599627669 .news-prefix .iconfont {font-size:14px;} #module_599627669 .news-prefix svg{width:14px;height:14px}
#module_599627669 .news-prefix .iconfont{color:#000;} #module_599627669 .news-prefix svg > *{ fill:#000;}
#module_599627669 .news-prefix{margin-top:0px;}
#module_599627669 .news-prefix{margin-bottom:0px;}
#module_599627669 .news-prefix{margin-left:0px;}
#module_599627669 .news-prefix{margin-right:14px;}
#module_599627669 .news-item time{font-size:12px;}
#module_599627669 .news-item time{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .news-item time{color:#666666;}
#module_599627669 .news-item time{font-weight:normal;}
#module_599627669 .news-item time{text-decoration:none;}
#module_599627669 .news-item time{font-style:normal;}
#module_599627669 .news-item{border-bottom-color:rgb(38, 38, 38);}
#module_599627669 .news-item{border-bottom-style:solid;}
#module_599627669 .news-item{border-bottom-width:1px;}
#module_599627669 .pagerGiant .text{font-size:12px;}
#module_599627669 .pagerGiant .text{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .pagerGiant .text{color:#333;}
#module_599627669 .pagerGiant .text{font-weight:normal;}
#module_599627669 .pagerGiant .text{text-decoration:initial;}
#module_599627669 .pagerGiant .text{font-style:normal;}
#module_599627669 .pagerGiant .current,#module_599627669 .pagerGiant .num,#module_599627669 .pagerGiant .inputer{width:30px}
#module_599627669 .pagerGiant .current,#module_599627669 .pagerGiant .num,#module_599627669 .pagerGiant .inputer{height:30px}
#module_599627669 .pagerGiant .num{font-size:12px;}
#module_599627669 .pagerGiant .num{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .pagerGiant .num{color:#333;}
#module_599627669 .pagerGiant .num{font-weight:normal;}
#module_599627669 .pagerGiant .num{text-decoration:initial;}
#module_599627669 .pagerGiant .num{font-style:normal;}
#module_599627669 .pagerGiant .submit{width:55px}
#module_599627669 .pagerGiant .submit{height:30px}
#module_599627669 .pagerGiant .current,#module_599627669 .pagerGiant .num,#module_599627669 .pagerGiant .inputer,#module_599627669 .pagerGiant .submit{border-top-left-radius:0px;}
#module_599627669 .pagerGiant .current,#module_599627669 .pagerGiant .num,#module_599627669 .pagerGiant .inputer,#module_599627669 .pagerGiant .submit{border-top-right-radius:0px;}
#module_599627669 .pagerGiant .current,#module_599627669 .pagerGiant .num,#module_599627669 .pagerGiant .inputer,#module_599627669 .pagerGiant .submit{border-bottom-left-radius:0px;}
#module_599627669 .pagerGiant .current,#module_599627669 .pagerGiant .num,#module_599627669 .pagerGiant .inputer,#module_599627669 .pagerGiant .submit{border-bottom-right-radius:0px;}
#module_599627669 .pagerGiant .next,#module_599627669 .pagerGiant .splitline{color:#ccc;}
#module_599627669 .pagerGiant .num{background-color:#fff;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-top-color:#ccc;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-top-style:solid;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer{border-top-width:1px;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-bottom-color:#ccc;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-bottom-style:solid;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer{border-bottom-width:1px;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-left-color:#ccc;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-left-style:solid;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer{border-left-width:1px;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-right-color:#ccc;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .inputer{border-right-style:solid;}
#module_599627669 .pagerGiant .num, #module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer{border-right-width:1px;}
#module_599627669 .page-more a{font-size:16px;}
#module_599627669 .page-more a{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .page-more a{color:#666 !important;}
#module_599627669 .page-more a{font-weight:normal;}
#module_599627669 .page-more a{text-decoration:none;}
#module_599627669 .page-more a{font-style:normal;}
#module_599627669 .page-more a{background-color:rgb(251, 188, 44);}
#module_599627669 .page-more a{border-top-color:#eee;}
#module_599627669 .page-more a{border-top-style:solid;}
#module_599627669 .page-more a{border-top-width:2px;}
#module_599627669 .page-more a{border-bottom-color:#eee;}
#module_599627669 .page-more a{border-bottom-style:solid;}
#module_599627669 .page-more a{border-bottom-width:2px;}
#module_599627669 .page-more a{border-left-color:#eee;}
#module_599627669 .page-more a{border-left-style:solid;}
#module_599627669 .page-more a{border-left-width:2px;}
#module_599627669 .page-more a{border-right-color:#eee;}
#module_599627669 .page-more a{border-right-style:solid;}
#module_599627669 .page-more a{border-right-width:2px;}
#module_599627669 .news-item:hover.even{background-color:rgb(255, 201, 5);}
#module_599627669 .news-item:hover.even{background-image:none;}
#module_599627669 .news-item:hover.even{background-repeat:no-repeat;}
#module_599627669 .news-item:hover.even{}
#module_599627669 .news-item:hover.even{}
#module_599627669 .news-item:hover.even{}
#module_599627669 .news-item:hover.odd{background-color:rgb(251, 188, 44);}
#module_599627669 .news-item:hover.odd{background-image:none;}
#module_599627669 .news-item:hover.odd{background-repeat:no-repeat;}
#module_599627669 .news-item:hover.odd{}
#module_599627669 .news-item:hover.odd{}
#module_599627669 .news-item:hover.odd{}
#module_599627669 .news-item:hover .news-title{font-size:14px;}
#module_599627669 .news-item:hover .news-title{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .news-item:hover .news-title{color:rgb(255, 255, 255);}
#module_599627669 .news-item:hover .news-title{font-weight:normal;}
#module_599627669 .news-item:hover .news-title{text-decoration:underline;}
#module_599627669 .news-item:hover .news-title{font-style:normal;}
#module_599627669 .news-item:hover time{font-size:12px;}
#module_599627669 .news-item:hover time{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .news-item:hover time{color:rgb(255, 255, 255);}
#module_599627669 .news-item:hover time{font-weight:normal;}
#module_599627669 .news-item:hover time{text-decoration:none;}
#module_599627669 .news-item:hover time{font-style:normal;}
#module_599627669 .news-item:hover{border-bottom-color:rgb(38, 38, 38);}
#module_599627669 .news-item:hover{border-bottom-style:solid;}
#module_599627669 .news-item:hover{border-bottom-width:1px;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{font-size:12px;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{color:#fff;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{font-weight:normal;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{text-decoration:initial;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{font-style:normal;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit{background-color:#000;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-top-color:#000;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-top-style:solid;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-bottom-color:#000;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-bottom-style:solid;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-left-color:#000;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-left-style:solid;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-right-color:#000;}
#module_599627669 .pagerGiant .current, #module_599627669 .pagerGiant .submit, #module_599627669 .pagerGiant .inputer:hover{border-right-style:solid;}
#module_599627669 .page-more a:hover{font-size:16px;}
#module_599627669 .page-more:hover a{font-family:微软雅黑,Microsoft YaHei;}
#module_599627669 .page-more a:hover{color:#666 !important;}
#module_599627669 .page-more a:hover {font-weight:normal;}
#module_599627669 .page-more a:hover {text-decoration:none;}
#module_599627669 .page-more a:hover {font-style:normal;}
#module_599627669 .page-more a:hover{background-color:#fff;}
#module_599627669 .page-more a:hover{border-top-color:#eee;}
#module_599627669 .page-more a:hover{border-top-style:solid;}
#module_599627669 .page-more a:hover{border-bottom-color:#eee;}
#module_599627669 .page-more a:hover{border-bottom-style:solid;}
#module_599627669 .page-more a:hover{border-left-color:#eee;}
#module_599627669 .page-more a:hover{border-left-style:solid;}
#module_599627669 .page-more a:hover{border-right-color:#eee;}
#module_599627669 .page-more a:hover{border-right-style:solid;}


#module_599627613 #Sub599627613_5 {top: 50%;transform: translateY(-50%);}
.module_599627667 .ModuleHead .HeadCenter{float:none;}
#module_599627667 {
padding:0px;
}
#module_599627667 {
position:static;
z-index:0;
top:0px;
width:100%;
height: auto;
}
#module_599627667 .ModuleHead599627667 {
display:none;
}
#module_599627667 .BodyCenter.BodyCenter599627667 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627667 >.module_599627667 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627667 >.module_599627667{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627667 .BodyCenter.BodyCenter599627667 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627667 .BodyCenter.BodyCenter599627667 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627667 >.module_599627667 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627667 >.module_599627667 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627667 >.module_599627667 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627667:hover {
border:none;
}
#module_599627667:hover >.module_599627667 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627667 .ModuleSubContainer{background-color:transparent;}
#module_599627667 .ModuleSubContainer{background-image:none;}
#module_599627667 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627667 .ModuleSubContainer{background-position:0% 0%;}
#module_599627667 .ModuleSubContainer{background-attachment:scroll;}
#module_599627667 .ModuleSubContainer{}
#module_599627667 {!bgVideo!}{bgVideoUrl:none}
#module_599627667 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627667 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627667 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627667 {}
.module_599627729 .ModuleHead .HeadCenter{float:none;}
#module_599627729 {
padding:0px;
}
#module_599627729 {
position:static;
z-index:0;
top:0px;
width:100%;
height: auto;
}
#module_599627729 .ModuleHead599627729 {
display:none;
}
#module_599627729 {
border:none;
}
@media screen and (min-width: 768px){
#module_599627729 >.module_599627729 {
margin-top:12.7379%;
margin-left:2.7818%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627729 >.module_599627729 {
margin-top:9.7701%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}

#module_599627729 .ModuleImageTextGiantContent{margin-top:0px;}
#module_599627729 .ModuleImageTextGiantContent{margin-bottom:0px;}
#module_599627729 .ModuleImageTextGiantContent{margin-left:0px;}
#module_599627729 .ModuleImageTextGiantContent{margin-right:0px;}
#module_599627729 .imageTextGiant-Container{padding-top:10px;}
#module_599627729 .imageTextGiant-Container{padding-bottom:10px;}
#module_599627729 .imageTextGiant-Container{padding-left:10px;}
#module_599627729 .imageTextGiant-Container{padding-right:10px;}
#module_599627729 .showHandle .btn{font-size:14px;}
#module_599627729 .showHandle .btn{font-family:;}
#module_599627729 .showHandle .btn{color:#333;}
#module_599627729 .showHandle .btn{font-weight:400;}
#module_599627729 .showHandle .btn{text-decoration:none;}
#module_599627729 .showHandle .btn{font-style:normal;}
#module_599627729 .showHandle div{border-top-color:#ddd !important;}
#module_599627729 .showHandle div{border-top-style:solid !important;}
#module_599627729 .showHandle div{border-top-width:1px !important;}
#module_599627729 .showHandle{background-color:transparent;}
#module_599627729 .showHandle{}
#module_599627729 .showHandle{background-repeat:no-repeat;}
#module_599627729 .showHandle{}
#module_599627729 .showHandle{}
#module_599627729 .showHandle{}
#module_599627729 a{font-size:unset;font-family:unset}
.module_599627665 .ModuleHead .HeadCenter{float:none;}
#module_599627665 {
padding:0px;
}
#module_599627665 {
position:static;
z-index:0;
top:771px;
left:257px;
width:100%;
height: auto;
}
#module_599627665 .ModuleHead599627665 {
display:none;
}
#module_599627665 .BodyCenter.BodyCenter599627665 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627665 >.module_599627665 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627665 >.module_599627665{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627665 .BodyCenter.BodyCenter599627665 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627665 .BodyCenter.BodyCenter599627665 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627665 >.module_599627665 {
margin-top:16.4615%;
margin-left:4.4615%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627665 >.module_599627665 {
margin-top:5.1724%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627665 >.module_599627665 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627665:hover {
border:none;
}
#module_599627665:hover >.module_599627665 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627665 .ModuleImageTextGiantContent{margin-top:0px;}
#module_599627665 .ModuleImageTextGiantContent{margin-bottom:0px;}
#module_599627665 .ModuleImageTextGiantContent{margin-left:0px;}
#module_599627665 .ModuleImageTextGiantContent{margin-right:0px;}
#module_599627665 .imageTextGiant-Container{padding-top:10px;}
#module_599627665 .imageTextGiant-Container{padding-bottom:10px;}
#module_599627665 .imageTextGiant-Container{padding-left:10px;}
#module_599627665 .imageTextGiant-Container{padding-right:10px;}
#module_599627665 .showHandle .btn{font-size:14px;}
#module_599627665 .showHandle .btn{font-family:微软雅黑,Microsoft YaHei;}
#module_599627665 .showHandle .btn{color:#333;}
#module_599627665 .showHandle .btn{font-weight:400;}
#module_599627665 .showHandle .btn{text-decoration:none;}
#module_599627665 .showHandle .btn{font-style:normal;}
#module_599627665 .showHandle div{border-top-color:#ddd !important;}
#module_599627665 .showHandle div{border-top-style:solid !important;}
#module_599627665 .showHandle div{border-top-width:1px !important;}
#module_599627665 .showHandle{background-color:transparent;}
#module_599627665 .showHandle{background-image:none;}
#module_599627665 .showHandle{background-repeat:no-repeat;}
#module_599627665 .showHandle{}
#module_599627665 .showHandle{}
#module_599627665 .showHandle{}
#module_599627665 a{font-size:unset;font-family:unset}
.ModuleShareGiant.layout-101  .social-share a { text-decoration: none;}
.ModuleShareGiant.layout-101  .social-share,.ModuleShareGiant.layout-101 .tips{ display: inline-block; }
.ModuleShareGiant.layout-101  .mvertical {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.ModuleShareGiant.layout-101  .social-share .social-share-icon {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 36px;
    font-size: 20px;
    border-radius: 50%;
    line-height: 36px;
    border: 1px solid #ccc;
    color: #999;
    text-align: center;
    vertical-align: middle;
    transition: background 0.6s ease-out 0s;
    margin-right: 5px;
    margin-left: 5px;
    margin-top: 8px;
    margin-bottom: 8px;
}
/* .ModuleShareGiant.layout-101  .social-share .icon-default-WeChat:hover{ color: #3dbe5b !important;  border-color: #3dbe5b !important;}
.ModuleShareGiant.layout-101  .social-share .type-weibo:hover{ color: #ff5757 !important;  border-color: #ff5757 !important;}
.ModuleShareGiant.layout-101 .social-share .icon-kefulei_huabanfuben11:hover{ color: #21b3f7 !important;  border-color: #21b3f7 !important;}
.ModuleShareGiant.layout-101 .social-share .icon-kongjian:hover{  color: #f8c514 !important;  border-color: #f8c514 !important;}
.ModuleShareGiant.layout-101 .social-share .icon-Facebook2:hover{  color: #3D5A98 !important;  border-color: #3D5A98 !important;}
.ModuleShareGiant.layout-101 .social-share .icon-Twitter1:hover{  color: #1DA1F3 !important;  border-color: #1DA1F3 !important;} */
.ModuleShareGiant.layout-101  .social-share .icon-wechat .mwechat-qrcode {
    display: none;
    border: 1px solid #eee;
    position: absolute;
    z-index: 9;
    top: -205px;
    left: -84px;
    width: 200px;
    height: 192px;
    color: #666;
    font-size: 12px;
    text-align: center;
    background-color: #fff;
    box-shadow: 0 2px 10px #aaa;
    transition: all 200ms;
}
.ModuleShareGiant.layout-101 .social-share .icon-wechat:hover .mwechat-qrcode {
    display: block;
}
.ModuleShareGiant.layout-101 .social-share .icon-wechat .mwechat-qrcode .qrcode {
    width: 105px;
    margin: 10px auto;
}
.ModuleShareGiant.layout-101 .social-share .icon-wechat .mwechat-qrcode .help p {
    font-weight: normal;
    line-height: 16px;
    padding: 0;
    margin: 0;
}
.ModuleShareGiant.layout-101 .social-share .icon-wechat .mwechat-qrcode:after {
    content: '';
    position: absolute;
    left: 50%;
    margin-left: -6px;
    bottom: -13px;
    width: 0;
    height: 0;
    border-width: 8px 6px 6px 6px;
    border-style: solid;
    border-color: #fff transparent transparent transparent;
}
.ModuleShareGiant.layout-101 .social-share .icon-wechat .mwechat-qrcode h4 {
    font-weight: normal;
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    background-color: #f3f3f3;
    margin: 0;
    padding: 0;
    color: #777 !important;
}
.ModuleShareGiant.layout-101  .mshareMask{ top: 0;
	left: 0; position: fixed; z-index: 999;  height: 100%; width: 100%; background: #000;  opacity: 0.6; display: none;}

.ModuleShareGiant.layout-101  .share-wx-qrcode-img .help p{ width: 40%; margin: 0 auto; color:#999;  display: block; line-height: 20px; height: auto; }
.ModuleShareGiant.layout-101  .share-wx-qrcode-img,.mwechat-qrcode { border-radius: 5px; top:0px; position: fixed;z-index:9999; top: 50%;
    left: 50%;box-shadow: 0px 0px 9px 5px rgba(0,0,0,0.09);
    transform: translate(-50%,-50%);text-align: center; width: 270px;  background: #fff; visibility: hidden;}
.ModuleShareGiant.layout-101  #mwx-qrcode{ width: 224px; height: 224px; margin: 0 auto;}
.ModuleShareGiant.layout-101 .share-wx-qrcode-img h4{border-top-right-radius: 5px;
    border-top-left-radius: 5px;background: #F7F8FA; height: 40px; line-height: 40px; font-size: 16px; color: #666; font-weight: bold; margin-bottom: 13px;}
.ModuleShareGiant.layout-101 .share-wx-qrcode-img .help{ margin-top: 12px; margin-bottom: 19px; color: #999;font-size: 14px; line-height: 20px;}
.ModuleShareGiant.layout-101  .share-wx-tips{ display: none;    z-index: 99999;
    position: fixed;
    top: 10px;
	right: 35px;    color: #fff; font-size: 24px; text-align: center;}
    
.ModuleShareGiant.layout-101 .fixed.icon-default-Weibo,.ModuleShareGiant.layout-101 .fixed.icon-default-Weibo:hover{ color:#F62319 !important}
.ModuleShareGiant.layout-101 .fixed.icon-default-WeChat,.ModuleShareGiant.layout-101 .fixed.icon-default-WeChat:hover{ color:#60C84D !important}
.ModuleShareGiant.layout-101 .fixed.icon-kongjian,.ModuleShareGiant.layout-101 .fixed.icon-kongjian:hover{ color:#FACC52 !important}
.ModuleShareGiant.layout-101 .fixed.icon-kefulei_huabanfuben11,.ModuleShareGiant.layout-101 .fixed.icon-kefulei_huabanfuben11:hover{ color:#21B3F7 !important}
.ModuleShareGiant.layout-101 .fixed.icon-Facebook2,.ModuleShareGiant.layout-101 .fixed.icon-Facebook2:hover{ color:#3D5A98 !important}
.ModuleShareGiant.layout-101 .fixed.icon-Twitter1,.ModuleShareGiant.layout-101 .fixed.icon-Twitter1:hover{ color:#1DA1F3 !important}
.ModuleShareGiant.layout-101 .fixed.icon-linkedin,.ModuleShareGiant.layout-101 .fixed.icon-linkedin:hover{ color:#0b66c3 !important}
.ModuleShareGiant.layout-101 .fixed.icon-tumblr.ModuleShareGiant.layout-101 .fixed.icon-tumblr:hover{ color:#36465f !important}
.ModuleShareGiant.layout-101 .fixed.icon-Pinterest,.ModuleShareGiant.layout-101 .fixed.icon-Pinterest:hover{ color:#cb2027 !important}
.ModuleShareGiant.layout-101 .fixed.icon-vk,.ModuleShareGiant.layout-101 .fixed.icon-vk:hover{ color:#4c6c91 !important}
.ModuleShareGiant.layout-101 .fixed.icon-Twitter,.ModuleShareGiant.layout-101 .fixed.icon-Twitter:hover{ color:#39A6DC !important}
.ModuleShareGiant.layout-101 .fixed.icon-GooglePlus,.ModuleShareGiant.layout-101 .fixed.icon-GooglePlus:hover{ color:#DB4B40 !important}
.module_599627666 .ModuleHead .HeadCenter{float:none;}
#module_599627666 {
padding:0px;
}
#module_599627666 {
position:static;
z-index:0;
top:488px;
left:304px;
width:100%;
height: auto;
}
#module_599627666 .ModuleHead599627666 {
display:none;
}
#module_599627666 .BodyCenter.BodyCenter599627666 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627666 >.module_599627666 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627666 >.module_599627666{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627666 .BodyCenter.BodyCenter599627666 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627666 .BodyCenter.BodyCenter599627666 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627666 >.module_599627666 {
margin-top:1.9034%;
margin-left:5.2709%;
margin-right:15.2269%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627666 >.module_599627666 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627666 >.module_599627666 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627666:hover {
border:none;
}
#module_599627666:hover >.module_599627666 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627666 .ModuleSharediv{text-align: left;}
#module_599627666 .social-share-icon{color:rgb(153, 153, 153);}
#module_599627666 .social-share-icon{font-size:20px;}
#module_599627666 .social-share-icon{ margin-right:5px;margin-left:5px;} #module_599627666 .mvertical{margin-top:5px;margin-bottom:5px;}
#module_599627666 .social-share-icon{height:36px; width:36px;line-height:36px}
#module_599627666 .social-share-icon{border-top-color:#ccc;}
#module_599627666 .social-share-icon{border-top-style:solid;}
#module_599627666 .social-share-icon{border-top-width:0px;}
#module_599627666 .social-share-icon{border-right-color:#ccc;}
#module_599627666 .social-share-icon{border-right-style:solid;}
#module_599627666 .social-share-icon{border-right-width:0px;}
#module_599627666 .social-share-icon{border-bottom-color:#ccc;}
#module_599627666 .social-share-icon{border-bottom-style:solid;}
#module_599627666 .social-share-icon{border-bottom-width:0px;}
#module_599627666 .social-share-icon{border-left-color:#ccc;}
#module_599627666 .social-share-icon{border-left-style:solid;}
#module_599627666 .social-share-icon{border-left-width:0px;}
#module_599627666 .social-share-icon{background-color:transparent;}
#module_599627666 .social-share-icon{}
#module_599627666 .social-share-icon:hover{background-color:transparent;}
#module_599627666 .social-share-icon:hover{}
#module_599627666 .social-share-icon:hover{color:#999;}
#module_599627666 .social-share-icon:hover{font-size:20px;}
#module_599627666 .social-share-icon:hover{border-top-color:#ccc;}
#module_599627666 .social-share-icon:hover{border-top-style:solid;}
#module_599627666 .social-share-icon:hover{border-top-width:0px;}
#module_599627666 .social-share-icon:hover{border-right-color:#ccc;}
#module_599627666 .social-share-icon:hover{border-right-style:solid;}
#module_599627666 .social-share-icon:hover{border-right-width:0px;}
#module_599627666 .social-share-icon:hover{border-bottom-color:#ccc;}
#module_599627666 .social-share-icon:hover{border-bottom-style:solid;}
#module_599627666 .social-share-icon:hover{border-bottom-width:0px;}
#module_599627666 .social-share-icon:hover{border-left-color:#ccc;}
#module_599627666 .social-share-icon:hover{border-left-style:solid;}
#module_599627666 .social-share-icon:hover{border-left-width:0px;}


#module_599627613 #Sub599627613_6 {top: 50%;transform: translateY(-50%);}
#module_599627613 .swiper-pagination-full {right: 30px;left: unset;top: 50%;transform: translateY(-50%);bottom: unset;}
.module_599627619 .ModuleHead .HeadCenter{float:none;}
#module_599627619 {
padding:0px;
}
#module_599627619 {
position:static;
z-index:4000;
top:832px;
left:0px;
width:100%;
height: auto;
}
#module_599627619 .ModuleHead599627619 {
display:none;
}
#module_599627619 .BodyCenter.BodyCenter599627619 {
background:none;background-color:rgba(0, 0, 0, 0.95);
}
#module_599627619 >.module_599627619 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627619 >.module_599627619{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627619 .BodyCenter.BodyCenter599627619 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627619 .BodyCenter.BodyCenter599627619 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627619 >.module_599627619 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627619 >.module_599627619 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627619 >.module_599627619 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627619:hover {
border:none;
}
#module_599627619:hover >.module_599627619 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627619 .ModuleSubContainer{background-color:transparent;}
#module_599627619 .ModuleSubContainer{background-image:none;}
#module_599627619 .ModuleSubContainer{background-repeat:no-repeat;}
#module_599627619 .ModuleSubContainer{background-position:0% 0%;}
#module_599627619 .ModuleSubContainer{background-attachment:scroll;}
#module_599627619 .ModuleSubContainer{}
#module_599627619 {!bgVideo!}{bgVideoUrl:none}
#module_599627619 .gridBgVideo .bgVideoMask{opacity:0.2}
#module_599627619 .ModuleSubContainer{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0); transition: box-shadow 0.5s ease;}
#module_599627619 .ModuleSubContainer:hover{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0);}
.ModuleGridContainer.ModuleGridContainer599627619 {max-width:none;width:100%;}
.module_599627620 .ModuleHead .HeadCenter{float:none;}
#module_599627620 {
padding:0px;
}
#module_599627620 {
position:static;
z-index:0;
top:1994px;
left:1183px;
width:100%;
height: auto;
}
#module_599627620 .ModuleHead599627620 {
display:none;
}
#module_599627620 .BodyCenter.BodyCenter599627620 {
background:none;background-color:rgba(0, 0, 0, 0);
}
#module_599627620 >.module_599627620 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627620 >.module_599627620{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627620 .BodyCenter.BodyCenter599627620 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627620 .BodyCenter.BodyCenter599627620 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_599627620 >.module_599627620 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_599627620 >.module_599627620 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_599627620 >.module_599627620 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627620:hover {
border:none;
}
#module_599627620:hover >.module_599627620 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627620 .ModuleImageTextGiantContent{margin-top:0px;}
#module_599627620 .ModuleImageTextGiantContent{margin-bottom:0px;}
#module_599627620 .ModuleImageTextGiantContent{margin-left:0px;}
#module_599627620 .ModuleImageTextGiantContent{margin-right:0px;}
#module_599627620 .imageTextGiant-Container{padding-top:10px;}
#module_599627620 .imageTextGiant-Container{padding-bottom:10px;}
#module_599627620 .imageTextGiant-Container{padding-left:10px;}
#module_599627620 .imageTextGiant-Container{padding-right:10px;}
#module_599627620 .showHandle .btn{font-size:14px;}
#module_599627620 .showHandle .btn{font-family:微软雅黑,Microsoft YaHei;}
#module_599627620 .showHandle .btn{color:#333;}
#module_599627620 .showHandle .btn{font-weight:400;}
#module_599627620 .showHandle .btn{text-decoration:none;}
#module_599627620 .showHandle .btn{font-style:normal;}
#module_599627620 .showHandle div{border-top-color:#ddd !important;}
#module_599627620 .showHandle div{border-top-style:solid !important;}
#module_599627620 .showHandle div{border-top-width:1px !important;}
#module_599627620 .showHandle{background-color:rgba(0, 0, 0, 0);}
#module_599627620 .showHandle{background-image:none;}
#module_599627620 .showHandle{background-repeat:no-repeat;}
#module_599627620 .showHandle{}
#module_599627620 .showHandle{}
#module_599627620 .showHandle{}
#module_599627620 a{font-size:unset;font-family:unset}
/*友情链接 layout 101*/

.ModuleLinkListGiant.layout-101{min-height: 29px;}
.ModuleLinkListGiant.layout-101 .LinkHor{display: inline-block;white-space: nowrap;}
.ModuleLinkListGiant.layout-101 .LinkVer{display:block;}
.ModuleLinkListGiant.layout-101 .btn:hover{text-decoration:underline;}.module_672076653 .ModuleHead .HeadCenter{float:none;}
#module_672076653 {
padding:0px;
}
#module_672076653 {
position:static;
z-index:0;
width:100%;
height: auto;
}
#module_672076653 .ModuleHead672076653 {
display:none;
}
#module_672076653 .BodyCenter.BodyCenter672076653 {
background:none;background-color:rgb(12, 12, 12);
}
#module_672076653 >.module_672076653 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:6px;
}
#module_672076653 >.module_672076653{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_672076653 .BodyCenter.BodyCenter672076653 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_672076653 .BodyCenter.BodyCenter672076653 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (min-width: 768px){
#module_672076653 >.module_672076653 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
@media screen and (max-width: 767px){
#module_672076653 >.module_672076653 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
#module_672076653 >.module_672076653 {box-shadow: 0px 0px 0px 0px #ccc}
#module_672076653:hover {
border:none;
}
#module_672076653:hover >.module_672076653 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_672076653 .BodyCenter{text-align:center;}
#module_672076653 .link-item .LinkHor .btn-link{font-size:12px;}
#module_672076653 .link-item .LinkHor .btn-link{font-family:微软雅黑,Microsoft YaHei;}
#module_672076653 .link-item .LinkHor .btn-link{color:rgb(255, 255, 255);}
#module_672076653 .link-item .LinkHor .btn-link{font-weight:normal;}
#module_672076653 .link-item .LinkHor .btn-link{text-decoration:none;}
#module_672076653 .link-item .LinkHor .btn-link{font-style:normal;}
#module_672076653 .link-item .LinkHor .btn-link:hover{font-size:12px;}
#module_672076653 .link-item .LinkHor .btn-link:hover{font-family:微软雅黑,Microsoft YaHei;}
#module_672076653 .link-item .LinkHor .btn-link:hover{color:#23527c;}
#module_672076653 .link-item .LinkHor .btn-link:hover{font-weight:normal;}
#module_672076653 .link-item .LinkHor .btn-link:hover{text-decoration:underline;}
#module_672076653 .link-item .LinkHor .btn-link:hover{font-style:normal;}
.ModuleMobileNavGiant.layout-101 p {
    color: #fff;
}

.ModuleMobileNavGiant.layout-101 .subnav li a {
    font-size: 15px;
}

.ModuleMobileNavGiant.layout-101 .mobileNav_1.showFloatNav {
    -ms-transform: translateX(17rem) !important;
    -khtml-transform: translateX(17rem) !important;
    -webkit-transform: translateX(17rem) !important;
    -o-transform: translateX(17rem) !important;
    -moz-transform: translateX(17rem) !important;
    transform: translateX(17rem) !important;
    -ms-transition: 0.5s;
    -webkit-transition: 0.5s;
    -khtml-transition: 0.5;
    -o-transition: 0.5s;
    -moz-transition: 0.5s;
    transition: 0.5s;
}

.mobileFootNav_1.showFloatNav {
    -ms-transform: translateX(17rem) !important;
    -khtml-transform: translateX(17rem) !important;
    -webkit-transform: translateX(17rem) !important;
    -o-transform: translateX(17rem) !important;
    -moz-transform: translateX(17rem) !important;
    transform: translateX(17rem) !important;
    -ms-transition: 0.5s;
    -webkit-transition: 0.5s;
    -khtml-transition: 0.5;
    -o-transition: 0.5s;
    -moz-transition: 0.5s;
    transition: 0.5s;
}

.ModuleMobileNavGiant.layout-101 .maxlarge {
    max-width: 10rem;
}

.ModuleMobileNavGiant.layout-101 .maxmiddle {
    max-width: 8rem;
}

.ModuleMobileNavGiant.layout-101 .header-model {
    width: 100%;
    height: 3rem;
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item {
    background: #333333;
    max-height: calc(100vh);
    z-index: 99999;
    -webkit-overflow-scrolling: touch;
}

.mobileNavFloatLayer_1 {
    display: block;
    visibility: hidden;
    width: 17rem;
    height: 100%;
    -ms-transform: translateX(-17rem);
    -khtml-transform: translateX(-17rem);
    -webkit-transform: translateX(-17rem);
    -o-transform: translateX(-17rem);
    -moz-transform: translateX(-17rem);
    transform: translateX(-17rem);
    -ms-transition: 0.5s;
    -webkit-transition: 0.5s;
    -khtml-transition: 0.5;
    -o-transition: 0.5s;
    -moz-transition: 0.5s;
    transition: 0.5s;
    -webkit-box-shadow: inset -0.3rem 0rem 0.5rem rgba(0, 0, 0, .3);
    -ms-box-shadow: inset -0.3rem 0rem .5rem rgba(0, 0, 0, .3);
    -moz-box-shadow: inset -0.3rem 0rem .5rem rgba(0, 0, 0, .3);
    -o-box-shadow: inset -0.3rem 0rem .5rem rgba(0, 0, 0, .3);
    box-shadow: inset -0.3rem 0rem 0.5rem rgba(0, 0, 0, .3);
    z-index: 9999
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item ul li {
    color: #fff;
    border-bottom: 1px solid #666;
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item ul li.on {
    border-left: 5px solid #888888;
    transition: all .3s ease-out
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item>ul>li>a {
    padding-left: 15%;
    font-size: 15px;
    color: #fff;
    display: block;
    height: 65px;
    position: relative;
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item>ul>li>a .main-class-colorlump {
    width: 10px;
    height: 66px;
    background-color: #888;
    display: none;
    vertical-align: middle;
    top: unset;
    margin-right: 0;
    transform: unset;
    position: unset;
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item ul li i {
    width: 18.5px;
    height: 20px;
    display: inline-block;
    margin-right: 1.06rem;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item ul li p {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 15px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 2.06rem;
}

.ModuleMobileNavGiant.layout-101 .micro-nav-item ul li.open>a .more:before {
    content: "\e6a6";
}

.ModuleMobileNavGiant.layout-101 .subnav {
    z-index: -99;
}

.ModuleMobileNavGiant.layout-101 .micro-nav {
    position: absolute;
    top: 50px;
    width: 100%;
    display: none;
}

.ModuleMobileNavGiant.layout-101 .more {
    line-height: 65px;
    z-index: 999;
    font-size: 20px;
    text-align: center;
    width: 50px;
    height: 100%;
    display: inline-block;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.4s ease;
}

.ModuleMobileNavGiant.layout-101 .treenav li a.treemenu {
    padding-left: 30%;
}

/*滚动条*/

.ModuleMobileNavGiant.layout-101 .micro-nav-item {
    overflow: auto;
    height: 100%;
}

.ModuleMobileNavGiant.layout-101 #accordion {
    position: relative;
    top: 0;
}

/*二级导航*/

.ModuleMobileNavGiant.layout-101 .subnav a {
    padding-left: 25%;
    display: block;
    font-size: 15px;
    position: relative;
    height: 65px;
    /*line-height: 65px;*/
}

.ModuleMobileNavGiant.layout-101 .containers {
    width: 100%;
    height: 100%;
    max-width: 100%;
    overflow: hidden;
    position: relative;
}

.ModuleMobileNavGiant.layout-101 header {
    display: flex;
    height: 50px!important;
    background: #000;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    line-height: 50px;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    height: 50px;
    padding: 0 0
}

.ModuleMobileNavGiant.layout-101 header {
    box-sizing: border-box;
    width: 100%
}



.bodyfixd {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.bodyabstre {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden
}



.ModuleMobileNavGiant.layout-101 .subnav li:first-child {
    border-top: 1px solid #666;
}

.ModuleMobileNavGiant.layout-101 .subnav li {
    border: 0 ! important
}

.ModuleMobileNavGiant.layout-101 .subnav li:last-child {
    border-bottom: 0;
}

.ModuleMobileNavGiant.layout-101 #mobileDesignerContainer #pagebody {
    margin-top: 3rem;
}

.ModuleMobileNavGiant.layout-101 #header p {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-left: 0;
    width: 100%;
    /* max-width: 80%; */
}

.ModuleMobileNavGiant.layout-101 #header p img {
    max-height: 40px;
    padding-right: 8pt;
    padding-left: 8pt;
    /* margin-right: 8px;
     margin-left: 15px;
    padding-left: 40px; */
}

.ModuleMobileNavGiant.layout-101 .navcontent {
    display: flex;
    width: calc(100% - 60px);
    margin-left: 50px;
    margin-right: 8pt;
}

.ModuleMobileNavGiant.layout-101 .navcontent a {
    width: 56px;
    text-align: center;
    display: block;
}

.ModuleMobileNavGiant.layout-101 .icons {
    font-size: 16pt;
    color: #fff;
}

.ModuleMobileNavGiant.layout-101 .svgdiv {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 999;
}

.ModuleMobileNavGiant.layout-101 .svgdiv svg {
    width: 16pt;
    height: 16pt;
    position: relative;
    z-index: -1;
}

@media only screen and (max-width: 767px) {
    .mobileNav {
        width: 100%;
        height: 3rem;
        line-height: 3rem;
        text-align: center;
        position: fixed;
    }
    .mobileNavFloatLayer_1 {
        display: block;
        visibility: hidden;
        width: 17rem;
        height: 100%;
        -ms-transform: translateX(-17rem);
        -khtml-transform: translateX(-17rem);
        -webkit-transform: translateX(-17rem);
        -o-transform: translateX(-17rem);
        -moz-transform: translateX(-17rem);
        transform: translateX(-17rem);
        -ms-transition: 0.5s;
        -webkit-transition: 0.5s;
        -khtml-transition: 0.5;
        -o-transition: 0.5s;
        -moz-transition: 0.5s;
        transition: 0.5s;
        -webkit-box-shadow: inset -0.3rem 0rem 0.5rem rgba(0, 0, 0, .3);
        -ms-box-shadow: inset -0.3rem 0rem .5rem rgba(0, 0, 0, .3);
        -moz-box-shadow: inset -0.3rem 0rem .5rem rgba(0, 0, 0, .3);
        -o-box-shadow: inset -0.3rem 0rem .5rem rgba(0, 0, 0, .3);
        box-shadow: inset -0.3rem 0rem 0.5rem rgba(0, 0, 0, .3);
        z-index: 9999
    }
    .mobileNavFloatLayer_r {
        right: 0;
        -ms-transform: translateX(17rem);
        -khtml-transform: translateX(17rem);
        -webkit-transform: translateX(17rem);
        -o-transform: translateX(17rem);
        -moz-transform: translateX(17rem);
        transform: translateX(17rem);
    }
}

@media only screen and (min-width: 768px) {
    .ModuleMobileNavGiant.layout-101 #mobileDesignerContainer #pagebody {
        margin-top: 0px;
    }
    .ModuleMobileNavGiant.layout-101 .header-model {
        display: none;
    }
}

.ModuleMobileNavGiant.layout-101 .icon-dianhua-moren {
    font-weight: normal;
}

.ModuleMobileNavGiant.layout-101 .icon-sousuo {
    font-weight: 750;
}

.ModuleMobileNavGiant.layout-101 .MobileNavClickLayer{
    top: 0;
    z-index: 9998;
    opacity: 0;
    height: 100%;
    width: 100%;
    position: fixed;
    display: none;
}.module_599627610 .ModuleHead .HeadCenter{float:none;}
#module_599627610 {
padding:0px;
}
#module_599627610 {
}
#module_599627610 .ModuleHead599627610 {
display:none;
}
#module_599627610 .BodyCenter.BodyCenter599627610 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627610 >.module_599627610 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627610 >.module_599627610{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627610 .BodyCenter.BodyCenter599627610 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_599627610 .BodyCenter.BodyCenter599627610 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627610 >.module_599627610 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_599627610 >.module_599627610 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627610 >.module_599627610 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627610:hover {
border:none;
}
#module_599627610:hover >.module_599627610 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627610 .langlistbox{background-color:#fff} #module_599627610 .langlistbox .jt{border-bottom-color:#fff}
#module_599627610 .langlistbox .langinfo{color:#333333}
#module_599627610 .langlistbox .langinfo{background-color:#fff}
#module_599627610 .langlistbox .langinfo{border-color:#333333}
#module_599627610 .langlistbox .langtxt{ font-size:16px }
#module_599627610 .langlistbox .langtxt{ color:#333333 }
#module_599627610 {}#module_599627610.layout-101 #header{border-bottom-color:transparent;}
#module_599627610 {}#module_599627610.layout-101 #header{border-bottom-style:none;}
#module_599627610 {}#module_599627610.layout-101 #header{border-bottom-width:0px;}
#module_599627610 {}#module_599627610.layout-101 #header{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.1);}
#module_599627610 {}#module_599627610.layout-101 #MobileNav{background-color: transparent;} #module_599627610.layout-101 #header{background-color:rgb(15, 36, 62);}
#module_599627610 {}#module_599627610.layout-101 #header{}
#module_599627610 {}#module_599627610.layout-101 #header p{font-Family:微软雅黑,Microsoft YaHei;}
#module_599627610 {}#module_599627610.layout-101 #header p{font-size:16px;}
#module_599627610 {}#module_599627610.layout-101 #header p{color:#fff;}
#module_599627610 {}#module_599627610.layout-101 #header p{font-weight:normal;}
#module_599627610 {}#module_599627610.layout-101 #header p{text-decoration:none;}
#module_599627610 {}#module_599627610.layout-101 #header p{font-style:normal;}
#module_599627610 {}#module_599627610.layout-101 .lcitem .rect{background:#fff;} #module_599627610.layout-101 .icons{color:#fff;} #module_599627610.layout-101 .svgdiv svg>*{fill:#fff;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item ul li{border-bottom-color:#4e4e4e;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item,#module_599627610.layout-101 #MobileNavFloatLayer{background-color:#3c3c3c;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item,#module_599627610.layout-101 #MobileNavFloatLayer{}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a{background:#3c3c3c;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a{}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a,#module_599627610.layout-101 .micro-nav-item>ul>li>a p{color:#fff;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a{background:#353535;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a{}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a,#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a p{color:#fff;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a,#module_599627610.layout-101 .micro-nav-item>ul>li>a p{font-family:微软雅黑,Microsoft YaHei;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a,#module_599627610.layout-101 .micro-nav-item>ul>li>a p{font-size:15px;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a,#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a p{font-family:微软雅黑,Microsoft YaHei;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a,#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a p{font-size:15px;}
#module_599627610 .langlistbox li:hover .langinfo,#module_599627610 .langactive .langinfo {color:#fff}
#module_599627610 .langlistbox li:hover .langinfo,#module_599627610 .langactive .langinfo{background-color:#333333}
#module_599627610 .langlistbox li:hover .langinfo,#module_599627610 .langactive .langinfo{border-color:#333333}
#module_599627610 .langlistbox li:hover .langtxt,#module_599627610 .langactive .langtxt{ color:#333333 }
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a:hover{}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a:hover,#module_599627610.layout-101 .micro-nav-item>ul>li>a:hover p{color:#fff;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li>a:hover .main-class-colorlump{display:block; background-color:#585858;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a:hover{background:#4a4747;}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a:hover{}
#module_599627610 {}#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a:hover,#module_599627610.layout-101 .micro-nav-item>ul>li .subnav a:hover p{color:#fff;}
.ModuleMobileFloatNavGiant {position: fixed;
     z-index: 4998;}
.ModuleMobileFloatNavGiant .defaultbtn{ position: relative; box-shadow: 0pt 1.5pt 3pt 0pt rgba(0,0,0,0.16); background-color: #fff;
    width:50px; height: 50px; border-radius: 50%;}
.ModuleMobileFloatNavGiant .spotgroup,.ModuleMobileFloatNavGiant .itemicon{  position: absolute;color: green;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-45%);
    font-size: 16px;
}
.ModuleMobileFloatNavGiant svg {
    width: 22px;
    height: 22px;
    margin-top: 5px;
}


.ModuleMobileFloatNavGiant .itemicon{ color: black;}
.ModuleMobileFloatNavGiant .itembtn{ display: block; position: relative; box-shadow: 0pt 1.5pt 3pt 0pt rgba(0,0,0,0.16); background-color: #fff;
    width:45px; height: 45px; border-radius: 50%; margin-bottom: 15px;}
.ModuleMobileFloatNavGiant .itemtext{
    position: relative;
    left: -60px;
    background: #fff;
    padding: 5px 14px;
    border-radius: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    box-shadow: 0px 1.5px 10px 0px rgba(0,0,0,0.16);
    color: #666;
    box-sizing: content-box;
    white-space: nowrap;
    /* min-width: 80px; */
}
.ModuleMobileFloatNavGiant .defaultbtnbghover{
    transform: rotate(180deg);
    transition: all 0.5s;
}
.ModuleMobileFloatNavGiant .itemtextright{
    text-align: right;
}
.ModuleMobileFloatNavGiant .itemtextleft{
    text-align: left;
}
.ModuleMobileFloatNavGiant .itemtextright::before{
    border-left: 8px solid #fff;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    top: 50%;
    right: -8px;
    position: absolute;
    transform: translateY(-50%);
    content: '';
}
.ModuleMobileFloatNavGiant .itemtextleft::before{
    border-right: 8px solid #fff;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    top: 50%;
    left: -7px;
    position: absolute;
    transform: translateY(-50%);
    content: '';
}
.ModuleMobileFloatNavGiant .FloatNavitemlist{ height: 0; display: none; transition:all}
.itemimg{
    max-height: 25px;
    max-width: 25px;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: cover !important;
    width: 100%;
    height: 100%
}
.FloatNavMask {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    filter: alpha(opacity=50);
    opacity: 0.50;
    background: #000;
    z-index: 15;
}
.FloatNavQRCode span img {
    border: solid 0.625rem #fff;
}
.FloatNavQRCode span {
    display: block;
    overflow: hidden;
    bottom: 3.750rem;
    right: 0.625rem;
    position: fixed;
    z-index: 20;
}
.FloatNavQRCode {
    display: none;
    position: fixed;
    top: 0;
    overflow: hidden;
    width: 100%;
    height: 100%;
    z-index: 10;
    text-align: center;
}.module_599627611 .ModuleHead .HeadCenter{float:none;}
#module_599627611 {
padding:0px;
}
#module_599627611 {
}
#module_599627611 .ModuleHead599627611 {
display:none;
}

#module_599627611 .defaultbtn{background-color:#fff}
#module_599627611 .defaultbtn{}
#module_599627611 .spotgroup{color:#000}
#module_599627611 .itembtn{background-color:#fff}
#module_599627611 .itembtn{}
#module_599627611 .itemicon{color:#666} #module_599627611 svg>*{fill:#666}
#module_599627611 .itemtext{background-color:#fff} #module_599627611 .itemtextright::before{border-left-color:#fff} #module_599627611 .itemtextleft::before{border-right-color:#fff}
#module_599627611 .itemtext{}
#module_599627611 .itemtext{font-size:12px;}
#module_599627611 .itemtext{font-family:;}
#module_599627611 .itemtext{color:#666 !important;}
#module_599627611 .itemtext{font-weight:normal;}
#module_599627611 .itemtext{text-decoration:none;}
#module_599627611 .itemtext{font-style:normal;}
#module_599627611 .defaultbtnbghover{background-color:#000000}
#module_599627611 .defaultbtnbghover{}
#module_599627611 .defaultbtniconbghover{color:#fff}
.module_599627612 .ModuleHead .HeadCenter{float:none;}
#module_599627612 {
padding:0px;
}
#module_599627612 {
}
#module_599627612 .ModuleHead599627612 {
display:none;
}
#module_599627612 .BodyCenter.BodyCenter599627612 {
background:none;background-color:rgba(0,0,0,0);
}
#module_599627612 >.module_599627612 {
border-top-style:solid;
border-top-color:#000;
border-top-width:0px;
border-left-style:solid;
border-left-color:#000;
border-left-width:0px;
border-right-style:solid;
border-right-color:#000;
border-right-width:0px;
border-bottom-style:solid;
border-bottom-color:#000;
border-bottom-width:0px;
}
#module_599627612 >.module_599627612{
overflow: hidden;
-webkit-backface-visibility:hidden;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;
}
@media screen and (min-width: 768px){
#module_599627612 .BodyCenter.BodyCenter599627612 {
padding-top:0px;
padding-left:0px;
padding-right:0px;
padding-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_599627612 .BodyCenter.BodyCenter599627612 {
padding-top:0.0000%;
padding-left:0.0000%;
padding-right:0.0000%;
padding-bottom:0.0000%;
}
}
@media screen and (min-width: 768px){
#module_599627612 >.module_599627612 {
margin-top:0px;
margin-left:0px;
margin-right:0px;
margin-bottom:0px;
}
}
@media screen and (max-width: 767px){
#module_599627612 >.module_599627612 {
margin-top:0.0000%;
margin-left:0.0000%;
margin-right:0.0000%;
margin-bottom:0.0000%;
}
}
#module_599627612 >.module_599627612 {box-shadow: 0px 0px 0px 0px #ccc}
#module_599627612:hover {
border:none;
}
#module_599627612:hover >.module_599627612 {box-shadow: 0px 0px 0px 0px #ccc;transition: box-shadow 0.5s ease 0s;}

#module_599627612 .foot-nav-list{background-color:rgb(15, 36, 62) !important;}
#module_599627612 .foot-nav-list{border-top-color:transparent;}
#module_599627612 .foot-nav-list{border-top-style:none;}
#module_599627612 .foot-nav-list{border-top-width:0px;}
#module_599627612 #MobileFootNav{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.1);}
#module_599627612 .icon{color:#fff !important;} #module_599627612 .icon svg >*{fill:#fff}
#module_599627612 .itemText{font-size:12px;}
#module_599627612 .itemText{font-family:微软雅黑,Microsoft YaHei;}
#module_599627612 .itemText{color:#fff !important;}
#module_599627612 .itemText{font-weight:normal;}
#module_599627612 .itemText{text-decoration:none;}
#module_599627612 .itemText{font-style:normal;}
#module_599627612 .iconh{color:#fff !important;}  #module_599627612 .iconh svg >*{fill:#fff}
#module_599627612 .itemTexth{font-size:12px;}
#module_599627612 .itemTexth{font-family:微软雅黑,Microsoft YaHei;}
#module_599627612 .itemTexth{color:#fff !important;}
#module_599627612 .itemTexth{font-weight:normal;}
#module_599627612 .itemTexth{text-decoration:none;}
#module_599627612 .itemTexth{font-style:normal;}
