(function(){ try {var elementStyle = document.createElement('style'); elementStyle.appendChild(document.createTextNode(".embed-hot-issue[data-v-d2162698]{background:#fff;position:absolute}.embed-hot-issue-item[data-v-d2162698]{position:absolute;display:flex;align-items:center;height:28px;line-height:28px;color:#303133;background-color:#fff;border-radius:14px;margin:6px 0;padding:0 8px 0 4px;box-sizing:border-box;min-width:80px;max-width:45vw;opacity:0;transform:translateY(-20px);transition:all 1s ease;z-index:-1;cursor:pointer}.embed-hot-issue-item .embed-hot-issue-icon[data-v-d2162698]{flex:none;width:20px;height:20px;display:inline-block;border-radius:50%;background:#9861E6;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAASCAYAAABb0P4QAAAAAXNSR0IArs4c6QAAANxJREFUOE/V1LEOwVAUxvH/wWIxeAaTxMAgsXoBL6B2MXoTCXZsnkOIgUHiPVgwcORwIzRtc5N20an5bvvLvT2nR1S1AcyBKumuI9ATVd0B9XTW5+29gXcglxH4MFAzwl7M/4EXYOA+wRgoAr5Z5JEXIhLYiqpaK3UB3ywS3AItt8M10AR8s9iiHBxY+6q+b/aHVU7d49bYJ6CUWnoDZwMnQD8jcGpg3saOG18SgjtAJZRtgFUos3lg42sWBn6eU9UysATabsHuAxG5xZ0oEXR/TAEYAVdgKCKJ0+kJf9pyYaU+UkoAAAAASUVORK5CYII=);background-repeat:no-repeat;background-position:center center;background-size:10px 9px}.embed-hot-issue-item .embed-hot-issue-text[data-v-d2162698]{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;font-size:12px}.embed-hot-issue .issue-item-1[data-v-d2162698]{color:#303133;background-color:#fff;box-shadow:1px 1px 8px 1px #0000001f}.embed-hot-issue .issue-item-0[data-v-d2162698]{color:#fff;background-color:#4c4c4c}.embed-hot-issue .fade-1[data-v-d2162698]{transform:translateY(-40px);opacity:1!important;transition:all 1s ease}.embed-hot-issue .fade-0[data-v-d2162698]{transform:translateY(-80px);opacity:0;transition:all 1s ease}.embed-group-icon[data-v-3d95fcfa]{padding:8px;border-radius:3px}.embed-group-icon__custom-icon[data-v-3d95fcfa]{width:100%;background-size:100% 100%}.embed-group-icon__default-icon[data-v-3d95fcfa]{width:85px;margin:0 auto 8px;display:flex;align-items:center;justify-content:space-between}.embed-group-icon__default-icon .embed-group-icon-img[data-v-3d95fcfa]{display:inline-block;width:16px;height:16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACIklEQVRYR8WXvUsdQRDAfwNqCoNgIUgQTREsUqRSsDKmURERAunSpArEMimDCoqldtG/wE4I+EU6DTaCpRBISKEhSEAkEDWgzcgc+57v6d3e7b17voWrbj5+O7OzOyNkXKraArwAJoCnwCP3mYVj930D1oBtEbnKYlrShFS1E5gGXgNtafLu/z9gBZgVkT8+nUQAVX0AfATeA60ZHd8WuwAWgXkRuYyzEQvgdv0ZGMjp+LbaHvAyLhp3AFT1GbAJdBXkvGTmNzAmIgeVdqsA3M736+C8EqK/MhJlAJfznQLDnhRAS8dQ6UxUAswCUwWHPcncnIhYZREBuND/rOG0h3JbdTyxVJQAloB3oVZqlF8WkUlxN9xJwCVTo9+yul1WHQYwAnwpymqgnVED+ARMehT/AltA7E3m0bObdAxo98gsGcC2lYVHyErma+DOInFVfQ5YaSetHQP4DvR6hHpE5FdOgG7gyKP7wwDOgIceoVVgIWcKPgCvPLbPDUDz7K4gnbNGA0QpaGQEokOYBnAKbHjOQBMwnPMFjcowDWBQRHZ9OVfVHuAwx7mILqI0gMci4islq/dm4D9g0ci6yldxGoC1ZlaGSV2uOX0DvM3q2cmVH6M0gEC7mcRvnuMMKchkMVDopiFpAEB1S3bPANYZVzel9wgQ35arqr3zNvfVcyUPJqpqc0Bfnbynj2aqOg6sFwwQNpw6iBnAxrLQdJzXMp5fAxNM19YbZ6g9AAAAAElFTkSuQmCC);background-size:contain}.embed-group-icon__default-icon .embed-group-icon-title[data-v-3d95fcfa]{color:#fff;font-size:16px}.embed-group-icon__item[data-v-3d95fcfa]{cursor:pointer;background:#FFFFFF;color:#666;text-align:center;font-size:12px;font-weight:400;padding:8px 10px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;border-bottom:1px solid rgba(0,0,0,.5)}.embed-group-icon__item[data-v-3d95fcfa]:first-child{border-radius:3px 3px 0 0}.embed-group-icon__item[data-v-3d95fcfa]:last-child{border-radius:0 0 3px 3px;border:none}.embed-group-icon .embed-group-icon-disabled[data-v-3d95fcfa]{background-color:#d9d9d9}.embed-icon[data-v-06873b41]{position:fixed;z-index:2147482100}.embed-icon-default[data-v-06873b41]{cursor:pointer;border-radius:5px}.embed-icon-unread-num[data-v-06873b41]{position:absolute;right:0;top:-7px;display:inline-block;padding:0 4px;height:14px;line-height:14px;background:#EF1F1F;border-radius:10px;color:#fff;margin-left:4px;text-align:center;font-size:12px;border:1px solid #FFFFFF}.embed-icon-pcIcon0[data-v-06873b41],.embed-icon-pcIcon1[data-v-06873b41],.embed-icon-pcIcon2[data-v-06873b41],.embed-icon-pcIcon3[data-v-06873b41],.embed-icon-pcIcon4[data-v-06873b41],.embed-icon-pcIcon5[data-v-06873b41]{height:100%;display:block;background-size:contain;background-repeat:no-repeat}.embed-icon-pcIcon0[data-v-06873b41]{background-image:url(https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/pcIcon0.png)}.embed-icon-pcIcon1[data-v-06873b41]{background-image:url(https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/pcIcon1.png)}.embed-icon-pcIcon2[data-v-06873b41]{background-image:url(https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/pcIcon2.png)}.embed-icon-pcIcon3[data-v-06873b41]{background-image:url(https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/pcIcon3.png)}.embed-icon-pcIcon4[data-v-06873b41]{background-image:url(https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/pcIcon4.png)}.embed-icon-pcIcon5[data-v-06873b41]{background-image:url(https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/pcIcon5.png)}.embed-image-viewer__wrapper[data-v-3175e1d3]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:2147483647}.embed-image-viewer__mask[data-v-3175e1d3]{position:absolute;width:100%;height:100%;top:0;left:0;opacity:.5;background:#000}.embed-image-viewer__close[data-v-3175e1d3]{top:2.5rem;right:2.5rem;width:2.5rem;height:2.5rem;font-size:1.5rem}.embed-image-viewer__btn[data-v-3175e1d3]{position:absolute;z-index:1;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;border-radius:50%;opacity:.8;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-select:none;user-select:none;cursor:pointer}[class^=embed-icon-][data-v-3175e1d3]{font-style:normal;font-weight:400;font-variant:normal;text-transform:none;line-height:1;vertical-align:baseline;display:inline-block}.embed-image-viewer__actions[data-v-3175e1d3]{left:50%;bottom:1.875rem;-webkit-transform:translateX(-50%);transform:translate(-50%);width:17.625rem;height:2.75rem;padding:0 1.4375rem;background-color:#606266;border-color:#fff;border-radius:1.375rem}.embed-image-viewer__actions__inner[data-v-3175e1d3]{width:100%;height:100%;text-align:justify;cursor:default;font-size:1.4375rem;color:#fff;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-ms-flex-pack:distribute;justify-content:space-around}.embed-image-viewer__canvas[data-v-3175e1d3]{width:100%;height:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.embed-chat-tip[data-v-2d74f0c2]{position:fixed;z-index:2147482400;width:280px;height:76px;background:#FFFFFF;box-shadow:0 2px 9px #0000001f;border-radius:8px;display:flex;flex-direction:column}.embed-chat-tip-header[data-v-2d74f0c2]{flex:1;border-bottom:1px solid #EBEEF5;font-size:14px;color:#606260;overflow:hidden;padding:0 12px;display:flex;align-items:center;justify-content:space-between}.embed-chat-tip-header .embed-chat-tip-close-btn[data-v-2d74f0c2]{cursor:pointer;display:inline-block;width:14px;height:14px;background-image:url(data:image/png;base64,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);background-size:100%}.embed-chat-tip-message[data-v-2d74f0c2]{flex:1;line-height:36px;font-size:14px;color:#303133;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 12px}.embed-chat-tip-unread-num[data-v-2d74f0c2]{display:inline-block;padding:0 4px;height:14px;line-height:14px;background:#EF1F1F;border-radius:10px;color:#fff;margin-left:4px;text-align:center;font-size:12px;border:1px solid #FFFFFF}@keyframes play-audio-c97e24c7{0%{background-image:url(data:image/png;base64,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);background-size:100%}30%{background-image:url(data:image/png;base64,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);background-size:100%}60%{background-image:url(data:image/png;base64,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);background-size:100%}to{background-image:url(data:image/png;base64,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);background-size:100%}}.embed-digital-man[data-v-c97e24c7]{position:relative;display:flex;height:46px}.embed-digital-man-img[data-v-c97e24c7]{width:100%}.embed-digital-man-img-wrapper[data-v-c97e24c7]{width:80px;height:66px;position:relative;top:-20px;overflow:hidden}.embed-digital-man-btn[data-v-c97e24c7]{position:absolute;left:60px;bottom:8px;width:24px;height:24px;cursor:pointer}.embed-digital-man-btn-play[data-v-c97e24c7]{background:no-repeat center;background-size:100%;animation:play-audio-c97e24c7 1.5s linear infinite}.embed-digital-man-btn-stop[data-v-c97e24c7]{background:url(data:image/png;base64,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) no-repeat center;background-size:100%}.embed-digital-man-text[data-v-c97e24c7]{line-height:46px;margin-left:14px;font-size:14px;color:#fff}.embed-digital-man-company[data-v-c97e24c7]{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;flex:1;font-size:14px}.embed-digital-man-company .phone[data-v-c97e24c7]{margin-bottom:4px}.embed-chat-container[data-v-e2f257f5]{position:fixed;z-index:2147482400}.embed-chat-container .embed-chat[data-v-e2f257f5]{width:100%;height:100%;display:flex;flex-direction:column;border-radius:8px;box-shadow:0 4px 8px #0000001f}.embed-chat-container .embed-chat-header[data-v-e2f257f5]{height:46px;border-radius:8px 8px 0 0;flex:none;cursor:move}.embed-chat-container .embed-chat-header-content[data-v-e2f257f5]{height:100%;display:flex;align-items:center;justify-content:space-between;padding:0 12px}.embed-chat-container .embed-chat-header-content .embed-chat-title[data-v-e2f257f5]{color:#fff;font-size:14px}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar[data-v-e2f257f5]{display:flex;align-items:center}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar span[data-v-e2f257f5]:not(:last-child){margin-right:12px}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar .embed-chat-audio-icon[data-v-e2f257f5]{cursor:pointer;display:inline-block;width:16px;height:16px;background-image:url(data:image/png;base64,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);background-size:100%}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar .embed-chat-is-mute[data-v-e2f257f5]{background-image:url(data:image/png;base64,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)}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar .embed-chat-close-icon[data-v-e2f257f5]{cursor:pointer;display:inline-block;width:14px;height:14px;background-image:url(data:image/png;base64,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);background-size:100%}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar .embed-chat-size-icon[data-v-e2f257f5]{cursor:pointer;display:flex;align-items:center;width:16px;height:16px}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar .embed-chat-is-mini[data-v-e2f257f5]:before{display:inline-block;content:\"\";height:1px;width:14px;background:#FFFFFF}.embed-chat-container .embed-chat-header-content .embed-chat-toolbar .embed-chat-is-max[data-v-e2f257f5]:before{display:inline-block;content:\"\";height:8px;width:11px;border:1px solid #FFFFFF}.embed-chat-container .embed-chat-content[data-v-e2f257f5]{flex:1}.embed-chat-container .embed-chat-content .embed-chat-iframe[data-v-e2f257f5]{width:100%;height:100%;border:none}.embed-chat-container[data-v-e2f257f5] .embed-chat-unread-num{display:inline-block;padding:0 4px;height:14px;line-height:14px;background:#EF1F1F;border-radius:10px;color:#fff;margin-left:4px;text-align:center;font-size:12px;border:1px solid #FFFFFF}.embed-messageboard-form-select[data-v-c877883f]{position:relative;color:#000;font-size:12px}.embed-messageboard-form-select .embed-messageboard-form-select-input[data-v-c877883f]{height:34px;line-height:32px;border:1px solid #DCDFE6;border-radius:4px;display:flex;align-items:center;cursor:pointer}.embed-messageboard-form-select .embed-messageboard-form-select-input .embed-messageboard-form-icon-ext[data-v-c877883f]{flex-shrink:0;width:35px;height:32px;display:inline-block;text-align:center;background-color:#fff;vertical-align:top;border-right:1px solid #fff;border-radius:4px 0 0 4px;background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAAAb0lEQVQoFWNgIBEwgtQfPH5W8/+//22MDIyC/xn+v2dkYqwCiaOL2VsaX2eCSTAxM1XbWxs7gGiQQhBGFwOpBWsAmWxnYXQNJACiQXxsYnANIGccOnFOCyQAokF8bGIgefL8ANJJLBgOnibWr3B1AO+pkealweGNAAAAAElFTkSuQmCC) no-repeat 9px center}.embed-messageboard-form-select .embed-messageboard-form-select-input .embed-messageboard-form-select-value[data-v-c877883f]{flex:1;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.embed-messageboard-form-select .embed-messageboard-form-select-options[data-v-c877883f]{position:absolute;max-height:300px;overflow-y:auto;padding:0 10px;line-height:30px;border:1px solid #DCDFE6;background:#fff;width:100%;z-index:1}.embed-messageboard-form-select .embed-messageboard-form-select-options .embed-messageboard-form-select-option[data-v-c877883f]{color:#8a8c8d;height:30px;line-height:30px;border-bottom:1px solid #d5d5d5;cursor:pointer;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.embed-messageboard-form-select .embed-messageboard-form-select-options .embed-messageboard-form-select-option[data-v-c877883f]:hover{color:#000}.embed-messageboard-form-select .embed-messageboard-form-select-options .embed-messageboard-form-select-option[data-v-c877883f]:last-child{border-bottom:none}.embed-messageboard-form[data-v-39c4214d]{padding:8px 12px}.embed-messageboard-form textarea[data-v-39c4214d]::placeholder,.embed-messageboard-form input[data-v-39c4214d]::placeholder{color:#b0b4bb;font-size:12px}.embed-messageboard-form .embed-messageboard-form-item[data-v-39c4214d]{height:34px;width:215px;line-height:32px;margin:8px auto 0;border:1px solid #DCDFE6;position:relative;background-color:#fff;border-radius:4px;font-size:14px;display:flex;padding-right:4px}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-label[data-v-39c4214d]{width:35px;height:32px;display:inline-block;text-align:center;background-color:#fff;vertical-align:top;border-right:1px solid #fff;border-radius:4px 0 0 4px;flex-shrink:0}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-label.embed-messageboard-form-icon-name[data-v-39c4214d]{background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAAA9ElEQVQoFYWQsWoCQRCGZ9a1SB1IrUW0y4FCOKLxRVIIPoNtItb6FjHvcQl7cgorXJoUKey1V2HvbrwJWTn3BIeF/f9/vr8ZBGeICNUyvue4++j9IiIVESyaMIzvDCUfgLAGIMxfrYrypdPxNpaTVvBvIJlKCa9dvx2xV5H2k8RMctlnX5rPUAdu6GbCBa55t3BYLH5ubelfH6zn/6yAWHnbm93MAqw5s/6sECj9TFk6yK+YWoA1Z7w7ZSy+5qtxRlQXFZj2/Pa3Xf7tIv2QpTAUiOveU2sESsXNINTvReiSZoZZKYTcIpmGe75yiW6YLedXkiNBwGUBI8BE6wAAAABJRU5ErkJggg==) no-repeat 9px center}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-label.embed-messageboard-form-icon-phone[data-v-39c4214d]{background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAABT0lEQVQoFXVRMUvDUBC+9wyBFEkr6OaiIAjZaoZKQYro4CK42kVxcBNcRNAf4X9QN8HRTZO0aSMkkRQyuSiuLhZaQducd9EEh/SG3N133/fl3XsAf9Hq9Wacrl9FRJFhRTkdttxgPwFoAuIL0UuNurlbRE4xdrZd/z5ztt3gxn6MFiYJJAy/F2kYCiGQSZQiGI0mCzQFXwEFi6DTedNohQ29JEPui0KapvkOAnQnDOfK5f4YEdQiYoZJLqSAC/xMzg3D+BICzj6GyWUcx6rVDbcs72kz24+5+RXSslcoxXVjtXrnuMFeAnhKW92SgUrLLc9WtB02ZFEanvesW23/gYTrDJDr1O8EwGkHx2zCfXokLmq1pb4C09tUnliuf0Q5/zu9kZYIHDAvB7nhIGfF6YaHgNBEwJigCh1tsFZfOaCrH6ekog8JJT+g50Xz/+c/yfOLiRCRy8AAAAAASUVORK5CYII=) no-repeat 9px center}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-label.embed-messageboard-form-icon-email[data-v-39c4214d]{background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAABLElEQVQoFZVRvUrEQBCeWZKIInb25xMIuVSX4/bQB7AQrtFXsPYV9BEUCzu5xj8QbAQTNbnmYiMWphEEi3QHInd4u+NsZIVIinOKHeab+b6Z2QH4p6Gpjx6yDpHuIqKo4xMnOXUrQz/GNH1sTJQ+FwJ2GZxYglKqFDOxEOhpDfsu4oYzBWiAoBci2FnwVC8Igk9LMj7P87n3YtQHhFwLsVKOgITPQOLwY4wXg0G+ZAlJ8jbPxadAeIKATwb/IfDwsu1fcuu98XR0Fd0Pt6J02Pui4poQj2S72bcilSU7Lf/GRXeTZRaBYBlcb7vbap7ZYuMd82itfxcMw9WCoQOD11mlQ13BX8zhFq8aYO0uzWIA5E+rGt+AGHHYrQtNx+UocZJJHkvOcriq3AzRNw7yci13+R6bAAAAAElFTkSuQmCC) no-repeat 9px center}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-label.embed-messageboard-form-icon-address[data-v-39c4214d]{background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAABaUlEQVQoFXVRO0sDQRCe2VOjqJDCJwS0EBHsYpqQCCksLDWNv0DBRtFOsBEL/QkighYidmJlqXfeC9m9i9iIqa2SRkVENDvunm6QoFPsvL5v95sdgBYjIsu9qUx6XjTS0kpSNEUFZE4g1oGwDAQRAPUA4jgx3C7lsxcG1yTYHt9FhKfp/JTySBrguve9DXg9IgsPfpPA98XYlcfPNKharaZsXyw5Pp8zJNsTvo61MX18IBUY4LmOH2vPO0rWgCRcsANRLhYnXoDowcyUEECyIaW5pglK/zAyvETACklSdWUIdclkEicEpT2UAImEFGMb1JAr6tZMBw4eajwRznS1w52OE9M/ZLs8dKKo39SMv/bFrO2KPZP/vIBKMlujN3msyG2mGYa3GUm01d1Jm6b2PYPKSoVsoAY/tf1oXy+Pc9733vg8QQsXc7lc3RCaezAFx+PLRDCvlpYmwFV9ken964MgHo3jOP0X4AvsDpyljEcngwAAAABJRU5ErkJggg==) no-repeat 9px center}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-label.embed-messageboard-form-icon-ext[data-v-39c4214d]{background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAAAb0lEQVQoFWNgIBEwgtQfPH5W8/+//22MDIyC/xn+v2dkYqwCiaOL2VsaX2eCSTAxM1XbWxs7gGiQQhBGFwOpBWsAmWxnYXQNJACiQXxsYnANIGccOnFOCyQAokF8bGIgefL8ANJJLBgOnibWr3B1AO+pkealweGNAAAAAElFTkSuQmCC) no-repeat 9px center}.embed-messageboard-form .embed-messageboard-form-item .embed-messageboard-form-input[data-v-39c4214d]{flex:1;margin:0;padding:0;height:32px;line-height:32px;border:none;display:inline-block;outline:0;font-size:14px;font-weight:400}.embed-messageboard-form .embed-messageboard-form-content[data-v-39c4214d]{width:100%;box-sizing:border-box}.embed-messageboard-form .embed-messageboard-form-content>textarea[data-v-39c4214d]{box-sizing:border-box;width:100%;height:80px;padding:10px 12px;resize:none;overflow:hidden;border:1px solid #DCDFE6;background-color:#fff;text-align:left;outline:0;border-radius:4px;font-size:12px;line-height:18px}.embed-messageboard-form .embed-messageboard-select-title[data-v-39c4214d]{height:34px;width:215px;line-height:32px;margin:2px 0;position:relative;background-color:#fff;border-radius:4px;font-size:14px;display:flex;padding-right:4px;align-items:center}.embed-messageboard-form .embed-messageboard-select-title>label[data-v-39c4214d]{width:35px;height:32px;display:inline-block;text-align:center;background-color:#fff;vertical-align:top;border-right:1px solid #fff;border-radius:4px 0 0 4px;flex-shrink:0;background:#fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADKADAAQAAAABAAAADAAAAAATDPpdAAAAb0lEQVQoFWNgIBEwgtQfPH5W8/+//22MDIyC/xn+v2dkYqwCiaOL2VsaX2eCSTAxM1XbWxs7gGiQQhBGFwOpBWsAmWxnYXQNJACiQXxsYnANIGccOnFOCyQAokF8bGIgefL8ANJJLBgOnibWr3B1AO+pkealweGNAAAAAElFTkSuQmCC) no-repeat 9px center}.embed-messageboard-form .embed-messageboard-select-title>div[data-v-39c4214d]{flex:1;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;color:#606266;font-size:12px}.embed-messageboard-form .embed-messageboard-send[data-v-39c4214d]{margin-top:12px}.embed-messageboard-form .embed-messageboard-send .embed-messageboard-send-btn[data-v-39c4214d]{height:32px;width:64px;line-height:32px;text-align:center;border-radius:4px;font-size:12px;color:#fff;cursor:pointer;margin-left:auto;background-color:var(--488531de)}.embed-messageboard-form .embed-messageboard-send .embed-messageboard-send-disable[data-v-39c4214d]{background-color:#aaacad}.embed-messageboard-form .embed-messageboard-error .embed-messageboard-form-content[data-v-39c4214d],.embed-messageboard-form .embed-messageboard-error .embed-messageboard-form-item[data-v-39c4214d]{border-color:#e64552}.embed-messageboard-form .embed-messageboard-error .embed-messageboard-form-input[data-v-39c4214d]{color:#e64552}.embed-messageboard-form .embed-messageboard-error .embed-messageboard-form-error-tip[data-v-39c4214d]{margin-top:2px;color:#e64552;font-size:12px}.embed-messageboard-result[data-v-b38b337f]{height:270px;background-color:#ededed;position:relative}.embed-messageboard-result .embed-messageboard-result-box[data-v-b38b337f]{width:120px;position:absolute;top:50%;left:50%;margin-left:-60px;transform:translateY(-50%)}.embed-messageboard-result .embed-messageboard-result-box .embed-messageboard-result-icon[data-v-b38b337f]{height:72px;width:80px;margin:0 auto;background:#fff url(data:image/png;base64,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) no-repeat center center;background-color:transparent}.embed-messageboard-result .embed-messageboard-result-box .embed-messageboard-result-title[data-v-b38b337f]{color:#333;font-size:14px;text-align:center;margin-top:10px}.embed-messageboard-result .embed-messageboard-result-box .embed-messageboard-result-content[data-v-b38b337f]{color:#333;text-align:center;font-size:12px}.embed-messageboard-result .embed-messageboard-result-box .embed-messageboard-result-close[data-v-b38b337f]{height:30px;width:90px;font-size:12px;color:#fff;margin:20px auto 0;background-color:var(--85fcfdfc);text-align:center;line-height:30px;cursor:pointer;border-radius:4px}.embed-messageboard[data-v-afdef977]{position:fixed;z-index:2147482300}.embed-messageboard .embed-messageboard-base[data-v-afdef977]{position:fixed;height:auto;width:240px;border-radius:8px;z-index:3;box-shadow:0 8px 40px #0006}.embed-messageboard .embed-messageboard-base .embed-messageboard-container[data-v-afdef977]{color:#000;border-radius:8px;background-color:#fff}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-header[data-v-afdef977]{height:46px;line-height:46px;font-size:14px;background-color:var(--1d2fba71);border-top-left-radius:5px;border-top-right-radius:5px;display:flex;justify-content:space-between;cursor:move}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-header-title[data-v-afdef977]{color:#fff;margin-left:12px;line-height:46px;padding-left:2px;-webkit-user-select:none;user-select:none}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-header-close[data-v-afdef977]{display:inline-block;height:46px;width:34px;cursor:pointer;background:transparent url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAAAKUlEQVQ4EWNgGAXDIAQY////Lwv0RzsQs5Hon19A9ZVMJGoaVT48QwAAT7IFHprVPikAAAAASUVORK5CYII=) no-repeat;background-position:center;background-size:50%}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-header-close.embed-messageboard-header-max[data-v-afdef977]{background:transparent url(data:image/png;base64,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) no-repeat;background-position:center}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-text[data-v-afdef977]{padding:12px 0}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-text>p[data-v-afdef977]{word-break:break-all;line-height:18px;margin:0 8px 0 12px;background-color:transparent}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-text .embed-messageboard-company[data-v-afdef977]{font-size:14px;color:#303133;white-space:pre-line}.embed-messageboard .embed-messageboard-base .embed-messageboard-container .embed-messageboard-text .embed-messageboard-link[data-v-afdef977]{font-size:12px;color:#909399}.embed-messageboard .embed-messageboard-left-bottom[data-v-afdef977]{left:20px;bottom:10px;top:auto;right:auto}.embed-messageboard *{box-sizing:border-box}.embed-popover[data-v-70766355]{position:relative;width:100%}.embed-popover .embed-popover-content[data-v-70766355]{position:absolute}.embed-popover .embed-popover-content .embed-popover-close[data-v-70766355]{width:16px;height:16px;background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAdBJREFUWAntljtLxEAQx/MqBEVOrK6wtha7AwsbsbH32jvIF1DwW5yNTYokBFJFbC31c1gLXpmApQlxBswxLBsz+yg3sGSfs7/8d2cmnucep4BTwCngFHAKmCjgcxcnSfIEcxdRFC3X6/UHd504r6qqg7qun33fn4VheAW2vsU5tB3QxkR9AeNnbdu+p2l6OjFXOoxwTdO8wuB13/fnCCmdSDrZgKgcrNtCmetADnAAdgE2WoBbrlarT8IirbKPGFejcggH1TmULUBfco5bAncbx/EL2px6lADRmCqkCRzupwyoAmkKpw3IgbQBZwT4H6QtOGNAGWQQBDfgqY/EW9kOgfbER+sOikao40D4ALYe7WIoMYLDfdhxUISibQw1qByB86D+wA0l1JZYtwKId+7vWHcnAu173YxDIY0BJQ5xBxtoZxwKh3UjQAkc3rkNZhhbkLsjEcmn2iNwu/RFHQdhuWlR3FcLcApu2MQGpDIgF84WpBKgKpwNSDagLpwpJNuL8TcdYtvws6mcITCYU+/uuu4tz/O94QPG3mxAMHAE5cckfQ2QYOMLPnYG2Wd/DEy5vyzLwyzLTpQXShagckVRHEuGXJdTwCngFHAKCAr8Ao16flso/vkZAAAAAElFTkSuQmCC) no-repeat center;background-size:16px;position:absolute;top:14px;right:14px;cursor:pointer}.embed-popover .embed-popover-arrow[data-v-70766355]{position:absolute;border-width:6px;filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));display:block;width:0;height:0;border-color:transparent;border-style:solid;border-right-width:0;border-left-color:#eaecf3}.embed-popover .embed-popover-arrow[data-v-70766355]:after{content:\" \";border-width:6px;position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;right:1px;bottom:-6px;margin-left:-6px;border-right-width:0;border-left-color:#fff}.embed-form-select[data-v-87d6af73]{position:relative}.embed-form-select>input[data-v-87d6af73]{height:40px;font-size:14px;padding:0 10px;color:#606266;background:#FFF;border-radius:3px;border:1px solid #DCDFE6;width:100%;cursor:pointer}.embed-form-select>input[data-v-87d6af73]:focus{border-color:#4e6ef2;outline:0}.embed-form-select[data-v-87d6af73]:before{border:5px solid transparent;border-top:5px solid #c0c4cc;width:0;height:0;position:absolute;right:10px;content:\" \";border-top-color:#fff;top:15px;z-index:10}.embed-form-select[data-v-87d6af73]:after{border:5px solid transparent;border-top:5px solid #c0c4cc;width:0;height:0;position:absolute;top:17px;right:10px;content:\" \"}.embed-form-select .embed-form-select-options[data-v-87d6af73]{position:absolute;max-height:300px;overflow-y:auto;line-height:30px;border:1px solid #DCDFE6;background:#fff;width:100%;z-index:1;padding:8px 0;box-shadow:0 1px 5px #0003;color:#606266}.embed-form-select .embed-form-select-options .embed-form-select-option[data-v-87d6af73]{height:36px;line-height:36px;padding:0 10px;cursor:pointer;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.embed-form-select .embed-form-select-options .embed-form-select-option[data-v-87d6af73]:hover{background:#F5F7FA}.embed-form-select .embed-form-select-options .embed-form-select-option .embed-form-select-mult-check[data-v-87d6af73]{display:inline-block;width:18px;height:18px;border-radius:3px;transform:translateY(4px);margin-right:6px;border:1px solid #ddd}.embed-form-select .embed-form-select-options .embed-form-select-option .embed-form-select-mult-check.embed-form-select-check[data-v-87d6af73]{background:#4e6ef2;position:relative}.embed-form-select .embed-form-select-options .embed-form-select-option .embed-form-select-mult-check.embed-form-select-check[data-v-87d6af73]:before{content:\"\";width:2px;border-radius:10px;display:block;background:white;height:6px;transform:rotate(-45deg);position:absolute;top:6px;left:4px}.embed-form-select .embed-form-select-options .embed-form-select-option .embed-form-select-mult-check.embed-form-select-check[data-v-87d6af73]:after{content:\"\";height:10px;width:2px;border-radius:10px;display:block;background:white;position:absolute;top:3px;left:9px;transform:rotate(45deg)}.embed-popover-form-header .embed-popover-form-header-title[data-v-0a0a659f]{font-size:18px;font-weight:400;color:#333;line-height:18px;margin:0 0 12px}.embed-popover-form-header .embed-popover-form-header-desc[data-v-0a0a659f]{font-size:14px;font-weight:400;color:#909399;line-height:14px;margin:0 0 24px}.embed-popover-form-main[data-v-0a0a659f]{width:400px;padding-bottom:58px}.embed-popover-form-main .embed-popover-form-item[data-v-0a0a659f]{display:flex;margin-bottom:24px}.embed-popover-form-main .embed-popover-form-item>.embed-popover-form-label[data-v-0a0a659f]{width:80px;max-width:120px;flex-shrink:0;padding-right:10px;color:#606266;font-size:14px;height:40px;display:flex;justify-content:flex-end;align-items:center;text-align:right}.embed-popover-form-main .embed-popover-form-item>.embed-popover-form-label.form-label-required[data-v-0a0a659f]:before{content:\"*\";color:#e64552;margin-right:4px}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content[data-v-0a0a659f]{position:relative;flex:1}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content>div[data-v-0a0a659f],.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content input[data-v-0a0a659f],.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content textarea[data-v-0a0a659f]{width:100%}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content>div[data-v-0a0a659f]:active,.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content input[data-v-0a0a659f]:active,.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content textarea[data-v-0a0a659f]:active,.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content>div[data-v-0a0a659f]:focus,.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content input[data-v-0a0a659f]:focus,.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content textarea[data-v-0a0a659f]:focus{border-color:#4e6ef2;outline:0}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content>input[data-v-0a0a659f]{height:40px;font-size:14px;padding:0 10px;color:#606266;background:#FFF;border-radius:3px;border:1px solid #DCDFE6}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content>textarea[data-v-0a0a659f]{height:76px;font-size:14px;padding:10px;background:#FFF;color:#606266;border-radius:3px;border:1px solid #DCDFE6;resize:none}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content.embed-popover-form-content-error input[data-v-0a0a659f],.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content.embed-popover-form-content-error textarea[data-v-0a0a659f]{border:1px solid #E64552}.embed-popover-form-main .embed-popover-form-item .embed-popover-form-content .embed-popover-error-tip[data-v-0a0a659f]{position:absolute;margin-top:2px;color:#e64552;font-size:12px}.embed-popover-form-footer[data-v-0a0a659f]{background-color:#fff;position:absolute;bottom:0;left:0;width:100%;padding:20px;display:flex;justify-content:flex-end}.embed-popover-form-footer>button[data-v-0a0a659f]{cursor:pointer;border-radius:4px;border:1px solid #DCDFE6;height:38px;background-color:#fff;color:#606266;padding:0 20px;margin-left:12px}.embed-popover-form-footer .embed-popover-form-footer-submit[data-v-0a0a659f]{background-color:#4e6ef2;color:#fff;border:none}.embed-popover-form-result[data-v-0a0a659f]{width:360px;text-align:center}.embed-popover-form-result .embed-popover-result-icon[data-v-0a0a659f]{height:130px;text-align:center;overflow:hidden;background:url(data:image/png;base64,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) center 70px no-repeat;background-size:40px}.embed-popover-form-result .embed-popover-result-icon.embed-popover-error-icon[data-v-0a0a659f]{background:url(data:image/png;base64,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) center 70px no-repeat;background-size:40px}.embed-popover-form-result .embed-popover-result-title[data-v-0a0a659f]{font-size:18px;line-height:18px;font-weight:500;color:#333;margin:20px}.embed-popover-form-result .embed-popover-result-subtitle[data-v-0a0a659f]{line-height:22px;font-size:14px;font-weight:400;color:#666}.embed-popover-form-result .embed-popover-result-footer[data-v-0a0a659f]{margin-top:60px;display:flex;justify-content:flex-end}.embed-popover-form-result .embed-popover-result-footer>button[data-v-0a0a659f]{cursor:pointer;border-radius:4px;border:1px solid #DCDFE6;height:38px;background-color:#fff;color:#606266;padding:0 20px;margin-left:12px}.embed-popover-form-result .embed-popover-result-footer .embed-popover-result-submit[data-v-0a0a659f]{background-color:#4e6ef2;color:#fff;border:none}@keyframes embed-loading-rotate-aa327ef2{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.embed-popover-panel[data-v-aa327ef2]{background:#FFFFFF;box-shadow:0 2px 12px #0000001a;border:1px solid #EBEEF5;padding:20px;border-radius:4px;height:100%;overflow-y:auto}.embed-popover-panel .embed-popover-panel-phone[data-v-aa327ef2]{min-width:200px;max-height:500px;overflow-y:auto}.embed-popover-panel .embed-popover-panel-phone[data-v-aa327ef2]::-webkit-scrollbar{display:none}.embed-popover-panel .embed-popover-panel-phone>div[data-v-aa327ef2]{font-size:14px;color:#606266;margin-top:10px}.embed-popover-panel .embed-popover-panel-phone>div[data-v-aa327ef2]:first-child{margin-top:0}.embed-popover-panel .embed-popover-panel-qr-code-img[data-v-aa327ef2]{width:150px;height:150px}.embed-popover-panel .embed-popover-panel-qr-code[data-v-aa327ef2]{font-size:12px;color:#909399;margin-top:3px;text-align:center}.embed-popover-panel .embed-popover-panel-loading-wrapper[data-v-aa327ef2]{position:relative;width:150px;height:150px}.embed-popover-panel .embed-popover-panel-loading-wrapper .embed-popover-panel-loading[data-v-aa327ef2]{position:absolute;top:0;left:0;height:100%;width:100%;background-color:#fff;display:flex;justify-content:center;align-items:center}.embed-popover-panel .embed-popover-panel-loading-wrapper .embed-popover-panel-loading>img[data-v-aa327ef2]{animation:embed-loading-rotate-aa327ef2 2s linear infinite}.embed-components[data-v-51508b36]{position:fixed;z-index:2147482100;cursor:pointer;box-shadow:0 2px 6px #00000014;width:48px;right:2px;background-color:#4e6ef2;border-radius:4px}.embed-components-item[data-v-51508b36]{position:relative;border-radius:4px;padding:8px;background-size:22px;background-repeat:no-repeat;background-position:center 6px;background-color:#4e6ef2;text-align:center;word-break:break-all;cursor:pointer;color:#fff;font-size:14px;display:flex;flex-direction:column;align-items:center}.embed-components .embed-components-item-icon[data-v-51508b36]{width:100%;height:100%;background-repeat:no-repeat;background-position:center center;background-size:100%;display:inline-block;position:relative;left:-100%}.embed-components .embed-components-item-icon.embed-components-icon-1[data-v-51508b36]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA3FJREFUWAndmDtoVEEUhncjGoOCkYDgoxGD2ESiRFBEsIhYiLExdlqYRgsrSy3ETlRSCilFsUgqH4Wk0GAURA0KYiGSQkOEhBgf+IiarN+/O2eZ7N69d3fvZvfigS8zOzPnn3/nPmY2qVTCIx3HXyaTWUX+AdgHG2A9KD7CJDyC4XQ6/Z2yfoGxDhiCObCYp+Jj7RozCB1L7pBJWuEGLIDMlBsaqxzlti6JUYQ7YRwKI8xoUJ80Oss1WdY9iGAXgiOwEpqceIZyHu7APRgD3XsK3Ys74RAchmVgcy1Q/wn7uTefU8YLzLXDNPwFha3KTeqbo9Q1BjRWYbnSkuaWqPzQfgSa4BmYsMpfcDw0MaBTOaAHxteStl2RgKyIJpJPg4Vucn3zIxFpJbuV6zSkZXGqZEJYB9nNMAP2jSV4LiynnD5pSMiFtDXHinJyF40hqdeJqNDKvYHliwZV8UEaTsvuaT5mjlYsRdJd8Fevr2KREgno9oGF5rhdYmjpZpI+mwKlHgxtazUJaTlNimx8KiUc+ASRsomENS5J7y0J9NDe4tqqLpxGDwKzIG3FWtq1l4cHg/RgXIFZsPCfuIFwheheRAdMmNLX1pyXoTlQhY42eAIKPzHXkvsb+82PzAtf0KvbnI9pazOT/iW+RuNu12Hbko2rZaktMihszj10yks2sgZxvJdPvWCDcr2N+SsPesXJU37j102btNAhI29Qh0mdTJIS8rJdZuweXEddR6KkhLzIU95gUowV+bAVnKPHXppFgxrQIC/fNK8ZfE89CU+wPCnk5bUqZlBH9qQZHPUN3uLDU6jHZY5aCD3BYzAIuRXkx4saT8APqIdJpgkMzf0F+vCU9WGXOEXDWzr0PhwBCw2SeaEtagLihjTsF2HhtveKvi68vLRJipabLUZtB6EbNoKdomeoX3VfhGp1gf5WMs/CSdACCa1aN9qxDyPoxA9M7gILnaiH4qvWSAEzaRgG/+fEsRrJx5fB2CWwkMl3kIxtFiNnzJlX6pjX2MBMC/SDTs12cqaaud4wZ0yue20HXIBpKIxRGlbXzSCT6RQ8AVMwCb/Bwh4IW72HdNTPnFaBCcfBjJgxv5S5P3Ae6v9QMOl9kAmZFPrXhq2YVlM/N9srvaRFO0mlAjaeyfVPy4uwzbVNUX6AB4Jd4qtr/7+Kf3FtGuBj4gbJAAAAAElFTkSuQmCC)}.embed-components .embed-components-item-icon.embed-components-icon-2[data-v-51508b36]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAopJREFUWAntmM9LFVEUx0clUBPyd22EVrWSEBKUxNSN7QIrWlmLwEBEI9yIf0BthDbRJlxIRKgLEdcGUghCLSwoiCA3RZSCuchf9focZWC83Ddz7333qosOfJk5537P93zfvHnz40XR/wh0BHK5XBUYAvPgG1gGj8HJQCPNZTHRB9aALt5RLDVX88xk+COdK6XW73msmRwm7ipG8qVvzRQ9snBSAX7mc6Spn/c4Pq9UcWKll/2aRJ61ey2L4GM9abDDUvCqJb8wOl/hJ83XmFb6y2J9YVOzu5NHsCybfoBRRNZ9oBIgSRoscdA/VIMrDgbbHXqsWpJHcNmqM4p24A9Y9rjTOeFvp/0ilLUd8h73aQ6dDKwEW4oRXbpL8YbDiMJbGDyjc6TUDvU+nDwH5ROOGXzMZgNOOApHa0E5Ymr6h0JTOAcZygxvUx1p8pcZMmGXMTShMaWWBsO6SFHHyWmwqjpScrnUdKbIhF1i+HXFkC79QfFsWCcp6gwf17lSah+OzCSDy8GSYkiXfqfYkvJZI9YHwH3QBarSuFZriJ0BKyArfkO4qROn/kDT/JnaC3APXAQuT1P742huBOvAJKYgNcRG2X9o0gRHXnOfgQtxr9WWxm4g92GT2Ib0HDwxISucTfLLVuZiMo13gNxJQsereKb1Fmc9QM63kCFPVXvPCfJeYR00X6JpFlRbNxs2FBFCVZ9mjNrpfQ1RTLq8JpjM+BKTnAxKMyY/smkFC5J7jklvenKugGEgvz4f8RWRWm8GYyFEz4G5Ah3KnwFXYs0gWwbI9XLR0ehoEFM6UQy2g2lgckmSu8gtnU7wGoNPgV7wFLwHv4DEBngDRkBdcCM2AzB0woZ/rLn/ADHv6SG8aKYSAAAAAElFTkSuQmCC)}.embed-components .embed-components-item-icon.embed-components-icon-3[data-v-51508b36]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAVhJREFUWAntWMERgjAQBEvwp6M10KclWIB2oF9bsAYd+NkC7o55JOQMxIOIY24mI7nc3W42cDAWRbYJFGjbdoNxwKgxpjZiEGsjbaXsOk3gFf5ld23i+QP1q7Is7zbOwp6Y6x1+U5MjNDGJ7ZikYI2IlROVbtJAwbUNJxFs7YDU1yDocJKOODWnIF4mGJTnk8VA0zu961UxOKyBwVqi9dYSs15OsZH2FhQCUI4kReuGz/4e7BIuxG29nF85YqfnkC25eKwTOnIfHFvs2T8kmaD2yGMUPANsy6dsiDEWgzkqi2kzJOd87fYho2Px7XPri7PXuXl7HqOgnZfsOobg3igyiJyJ3Q8KDgQ5cjIOhfObJCCYtxRzxF5yCkcmqFU5K5gV1Cqgzf/Je7DR7lqR72FLCl4UANpUD1t6F/MTab5/YJpvvgokjxie5PCNbcQglvfv6thA/1nvCWeEXv39HqLzAAAAAElFTkSuQmCC)}.embed-components .embed-components-item-icon.embed-components-icon-4[data-v-51508b36]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA3pJREFUWAntmFuITVEYx89xGbcxoyaXkZASySW5lksaITPlQTTCA5kHlwdS3pQHIQnF08STklHMKKSJQky5PIgHYZpCijENGYypGXP8/tPs3TnrrH1m7332GfNwvvq191rff337P/uy1joTi+Ujfwf+7x2I93X5RCIxEs0KWA1LYAKMhQJohRZ4AQ1QH4/HP3PMfWCsFE7Ad/AbnQhvQFnOHFI8DgfgD2QTdQyeGqlRChbB3WxcGWO/0V4TiUkKFUKDcYEoml0UqczaJEVuR+HGo0YH/ctCm2Twdo/CrfRXQDFsAt0NW7yncyWMgT02AX3SDA9skkEF0Ay2OJJcEMFDm4i+XYauyUN3OFnn53wQonIY5yGe7fRzwcGcT3LaxjFZV0iuxMg7zb3U0TV9xxCUWzKo9VjryT8GzW3TPLT70U0m9xI2QLGh66L9FXRcAM/BV2jOU9G5vtT+RX+R3oRbcA9GQyloBZLJZmhi1fnEMXNgMMhKgbzPuIJiBpRBDbSAV7whcQp09+1BUpNpFPGLIlthPjwLWFBL5AUwX42YXtgmu/VAve2o9Y7qY3sKiyBI6FuogleY1IbEDRlsdFvhThIM04e2Cs7CUAgbetRaapc6BWSwxmmEPF5inEyeDDneHKYPSquatnVUZl6CRggT7QyaDh8tg/VeHQStUu8s+S+9OU1RPyz52h6DvSYrLQI/XdcQVXkIrzsXIL/eojmUlD9myatrlh5xjPnoKofzzoAAR5nY5qFfzAUmwjDymy2adeRGgObGtZa8una4W36Eerk1sXqJNcCMOXRoq19kJnrbmpS7QT8PbNFBp3I9N8oiuO8muIudCCqg2iL06vpJwsucxmj68DKnvHY3rgd1GDElJYnJLtiN6Jwh9GpqSdMXnKtoSzGYdJXxSeeZTjVvaROQq3itLVRK6MWl4yLo0eju1IE+IC3sep/0R0mj4yPQY3S3W5xHGWfSimFwI3RDLcxLE9BBv3ZB+jp1Xg65iA8UTV+V6NTEajWWweyDHDjU8hlNYE7bq7YITaY/2mytYm45/I7A5GVqpH0b2frrGU/hhfA2C5PHGesuHpGYMotwgVFwFMJshDVt9U9gUP+p2Al6ZNrtaHfjhHYvT+A0VIOT29c/7ixXwYSmphLQz9KUoG8m1MGdlMRAa2Aw6E+FgfYn5P0EvwP/AFSxNOb5oWNhAAAAAElFTkSuQmCC)}.embed-components .embed-components-item-icon.embed-components-icon-5[data-v-51508b36]{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAAAXNSR0IArs4c6QAAApVJREFUWAntmc1qFEEQx3dlIaJJNLlEQjQgKORuQIy5iE+QS/II8SkEBS9CLh6C4EHIIYdc/Lh59hII+ADCKiiCetCQL4JJ1t8/bA+d3q2ZCUxPMjAFtV3d1VX175qamd6eRqfTuQO34YMMvtGIRMTdz4i9jf7pcXiEVTgPTUbC2yD4YQ4AR8wZugCIy7GAFOy3KawtfiQ4+oCw7DpB+yvoF9mdw5mPw/meQnjmOmoFuOMNfG02m2+8fikiMd/2C0QJ/A7HVRKVorAkesCzyhEG78FXe5TFDeziap1M/8hyKcAmAfYByjV41JxUnEKPtkVAv05zaZYExroJVuAywArjALxM3DF1LBLgHXizy3vexJvI416/DPEiQaa9QAfIDpvaoxaXYMGb4IuX/E6JcvJeANs6cU/cO2ZJlAjwVKHOI+B+L5BkUTXgJBWRhDrDBSS2cjWcuubKlUSLV+F9ltTvMugB/jF1uXGUg2CaNVxvpP09eWUYRR0G7BJs0e3zWBKpCakcYO2Hre3jLa7L39TlxlE+wu2JDY8XZku7NW3begiw+wxe6VHEHzi0MCl05UqiBhy7YuoM1xkOMlC5ktDmR+e+Q92F/OEZmHn6Eiw6Whds+ts/4QVoa/PzHnb00ikZmHCDJbcPPQwzQewRsyTI9HcMPznjklod6eoswiQTcNdinnbDtC5W8QV3cyRqK82tNj8mYfwZ5TSXRedd1obEtD+FYpdY3/LMF2D/QLuvDc5+ohCfOWWVxJkDDAEow/7/ubtc/ufhpG7/SVZ9GXaZw8TUJy0fh7O57oSkZfI7OA9dS4wKFgie57OXMB4/1mJ+HSpyaTq73lZJPIaH4awvnf+YE4v07E27nwT2BSUZE0Octf0H3Ek7OqsUh/MAAAAASUVORK5CYII=)}.embed-components *{box-sizing:border-box}.embed-invite[data-v-c2e74e82]{position:fixed;z-index:2147482200;top:50%;left:50%;transform:translate(-50%,-50%)}.embed-invite .embed-invite-wrap[data-v-c2e74e82]{position:relative;height:178px;border-radius:12px;background-color:#fff;color:#303133;box-shadow:0 6px 28px #5a74b166}.embed-invite .embed-invite-avatar[data-v-c2e74e82]{position:absolute;width:60px;height:60px;border-radius:50%;border:1px solid #ffffff;border-color:var(--00651164);overflow:hidden;left:50%;top:-30px;transform:translate(-50%);background:var(--fd4c41ae);background-position:center center;background-color:#fff;background-size:contain}.embed-invite .embed-invite-close[data-v-c2e74e82]{width:18px;height:18px;position:absolute;right:8px;top:8px;left:auto;bottom:auto;cursor:pointer;background:transparent url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAMCAYAAAC0qUeeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpERDkzMDc0MDNDMjA2ODExODIyQUE2NEU0RkI1NTc5RCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDowOTZDOUU1RDJGNUExMUU1OTM1MUYzOEY2MTBGNUE3OCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDowOTZDOUU1QzJGNUExMUU1OTM1MUYzOEY2MTBGNUE3OCIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChNYWNpbnRvc2gpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6REQ5MzA3NDAzQzIwNjgxMTgyMkFBNjRFNEZCNTU3OUQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6REQ5MzA3NDAzQzIwNjgxMTgyMkFBNjRFNEZCNTU3OUQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4CChQ0AAAA1UlEQVR42pSQsQrCMBiEkyBdnSq6u3YSdy0iuDrWzVeQYvQFWgXxDdzsqKMgIuITiLNv4OTapV7kIqFO/vA1XO7SNie11i0hxBIMwUv8ThXswFThkYKQG14p6HHf+IkJR+DBjQ2QDErqkH5kwk8w4DoCC4YT6q9foWFO9sAVxKANOrxDn75Qzv/decmcwZz6ZgNK/DFuOHAauThNBOVwExzZ6Rp0wYr6RP8T9sEB1MEWTPiCmLpG3zdtZDx5BmNQMFxQN9h1ZsIzfsE24Y5tZA/mbwEGAMZYLnpGzdotAAAAAElFTkSuQmCC) no-repeat;background-position:center center;background-size:50%}.embed-invite .embed-invite-body[data-v-c2e74e82]{padding-top:32.5px;display:flex;justify-content:space-around;flex-direction:column;height:80%;box-sizing:content-box}.embed-invite .embed-invite-content[data-v-c2e74e82]{display:flex;justify-content:var(--03efa0c0)}.embed-invite .embed-invite-content .embed-invite-welcome[data-v-c2e74e82]{color:var(--ada3b1a8);max-width:var(--1f3a7b17);text-align:var(--addee264);word-break:break-all;height:80px;display:flex;align-items:center;justify-content:center;width:auto;padding:0 16px;font-size:14px;box-sizing:content-box}.embed-invite .embed-invite-footer[data-v-c2e74e82]{display:flex;justify-content:var(--9951f2ba);padding:0 16px 12px}.embed-invite .embed-invite-footer .embed-invite-chat-button[data-v-c2e74e82]{background-color:var(--00651164);color:#fff;border-radius:16px;min-width:82px;box-sizing:content-box;padding:0 16px;width:auto;border:none;display:inline-block;height:32px;line-height:32px;text-align:center;cursor:pointer;font-size:13px}.embed-invite .embed-invite-footer .embed-invite-lagency-button[data-v-c2e74e82]{display:inline-block;width:80px;height:32px;line-height:30px;text-align:center;border:1px solid #fff;border-radius:5px;cursor:pointer;font-size:13px;color:#fff;background-color:transparent}.embed-invite .embed-invite-footer .embed-invite-lagency-button.embed-invite-lagency-ok[data-v-c2e74e82]{color:#0085da;background-color:#fff;margin-left:8px}.embed-invite *{box-sizing:border-box}[class^=embed] div,[class^=embed] span,[class^=embed] applet,[class^=embed] object,[class^=embed] iframe,[class^=embed] h1,[class^=embed] h2,[class^=embed] h3,[class^=embed] h4,[class^=embed] h5,[class^=embed] h6,[class^=embed] p,[class^=embed] pre,[class^=embed] a,[class^=embed] em,[class^=embed] font,[class^=embed] img,[class^=embed] strong,[class^=embed] sub,[class^=embed] sup,[class^=embed] dl,[class^=embed] dt,[class^=embed] dd,[class^=embed] ol,[class^=embed] ul,[class^=embed] li,[class^=embed] fieldset,[class^=embed] form,[class^=embed] label,[class^=embed] legend,[class^=embed] table,[class^=embed] caption,[class^=embed] tbody,[class^=embed] tfoot,[class^=embed] thead,[class^=embed] tr,[class^=embed] th,[class^=embed] td{margin:0;padding:0;border:0;outline:0;font-style:normal;font-size:100%;vertical-align:baseline;font-family:Arial,Helvetica,Microsoft YaHei,sans-serif;overflow:initial;line-height:1.2}div[class^=embed]{overflow:initial}#aff-im-root{display:block!important}.vcode-body .bluelink{display:none}.vcode-mask{z-index:2147483646!important}.vcode-body{z-index:2147483647!important}")); document.head.appendChild(elementStyle);} catch(e) {console.error('vite-plugin-css-injected-by-js', e);} })();var AffIm=function(e){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var r=function(e){return e&&e.Math===Math&&e},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof t&&t)||r("object"==typeof t&&t)||function(){return this}()||Function("return this")(),i=function(e){try{return!!e()}catch(t){return!0}},a=!i((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),u=a,c=Function.prototype,l=c.apply,s=c.call,f="object"==typeof Reflect&&Reflect.apply||(u?s.bind(l):function(){return s.apply(l,arguments)}),v=a,d=Function.prototype,p=d.call,h=v&&d.bind.bind(p,p),g=v?h:function(e){return function(){return p.apply(e,arguments)}},m=g,y=m({}.toString),b=m("".slice),w=function(e){return b(y(e),8,-1)},A=w,_=g,x=function(e){if("Function"===A(e))return _(e)},S="object"==typeof document&&document.all,C={all:S,IS_HTMLDDA:void 0===S&&void 0!==S},k=C.all,E=C.IS_HTMLDDA?function(e){return"function"==typeof e||e===k}:function(e){return"function"==typeof e},T={},I=!i((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),O=a,R=Function.prototype.call,P=O?R.bind(R):function(){return R.apply(R,arguments)},j={},N={}.propertyIsEnumerable,M=Object.getOwnPropertyDescriptor,D=M&&!N.call({1:2},1);j.f=D?function(e){var t=M(this,e);return!!t&&t.enumerable}:N;var B,F,L=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},U=i,V=w,z=Object,H=g("".split),W=U((function(){return!z("z").propertyIsEnumerable(0)}))?function(e){return"String"===V(e)?H(e,""):z(e)}:z,q=function(e){return null==e},Q=q,K=TypeError,Z=function(e){if(Q(e))throw new K("Can't call method on "+e);return e},Y=W,G=Z,$=function(e){return Y(G(e))},X=E,J=C.all,ee=C.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:X(e)||e===J}:function(e){return"object"==typeof e?null!==e:X(e)},te={},ne=te,re=o,oe=E,ie=function(e){return oe(e)?e:void 0},ae=function(e,t){return arguments.length<2?ie(ne[e])||ie(re[e]):ne[e]&&ne[e][t]||re[e]&&re[e][t]},ue=g({}.isPrototypeOf),ce="undefined"!=typeof navigator&&String(navigator.userAgent)||"",le=o,se=ce,fe=le.process,ve=le.Deno,de=fe&&fe.versions||ve&&ve.version,pe=de&&de.v8;pe&&(F=(B=pe.split("."))[0]>0&&B[0]<4?1:+(B[0]+B[1])),!F&&se&&(!(B=se.match(/Edge\/(\d+)/))||B[1]>=74)&&(B=se.match(/Chrome\/(\d+)/))&&(F=+B[1]);var he=F,ge=he,me=i,ye=o.String,be=!!Object.getOwnPropertySymbols&&!me((function(){var e=Symbol("symbol detection");return!ye(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ge&&ge<41})),we=be&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ae=ae,_e=E,xe=ue,Se=Object,Ce=we?function(e){return"symbol"==typeof e}:function(e){var t=Ae("Symbol");return _e(t)&&xe(t.prototype,Se(e))},ke=String,Ee=function(e){try{return ke(e)}catch(t){return"Object"}},Te=E,Ie=Ee,Oe=TypeError,Re=function(e){if(Te(e))return e;throw new Oe(Ie(e)+" is not a function")},Pe=Re,je=q,Ne=function(e,t){var n=e[t];return je(n)?void 0:Pe(n)},Me=P,De=E,Be=ee,Fe=TypeError,Le={exports:{}},Ue=o,Ve=Object.defineProperty,ze=function(e,t){try{Ve(Ue,e,{value:t,configurable:!0,writable:!0})}catch(n){Ue[e]=t}return t},He="__core-js_shared__",We=o[He]||ze(He,{}),qe=We;(Le.exports=function(e,t){return qe[e]||(qe[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.34.0",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.34.0/LICENSE",source:"https://github.com/zloirock/core-js"});var Qe=Le.exports,Ke=Z,Ze=Object,Ye=function(e){return Ze(Ke(e))},Ge=Ye,$e=g({}.hasOwnProperty),Xe=Object.hasOwn||function(e,t){return $e(Ge(e),t)},Je=g,et=0,tt=Math.random(),nt=Je(1..toString),rt=function(e){return"Symbol("+(void 0===e?"":e)+")_"+nt(++et+tt,36)},ot=Qe,it=Xe,at=rt,ut=be,ct=we,lt=o.Symbol,st=ot("wks"),ft=ct?lt.for||lt:lt&&lt.withoutSetter||at,vt=function(e){return it(st,e)||(st[e]=ut&&it(lt,e)?lt[e]:ft("Symbol."+e)),st[e]},dt=P,pt=ee,ht=Ce,gt=Ne,mt=function(e,t){var n,r;if("string"===t&&De(n=e.toString)&&!Be(r=Me(n,e)))return r;if(De(n=e.valueOf)&&!Be(r=Me(n,e)))return r;if("string"!==t&&De(n=e.toString)&&!Be(r=Me(n,e)))return r;throw new Fe("Can't convert object to primitive value")},yt=TypeError,bt=vt("toPrimitive"),wt=function(e,t){if(!pt(e)||ht(e))return e;var n,r=gt(e,bt);if(r){if(void 0===t&&(t="default"),n=dt(r,e,t),!pt(n)||ht(n))return n;throw new yt("Can't convert object to primitive value")}return void 0===t&&(t="number"),mt(e,t)},At=Ce,_t=function(e){var t=wt(e,"string");return At(t)?t:t+""},xt=ee,St=o.document,Ct=xt(St)&&xt(St.createElement),kt=function(e){return Ct?St.createElement(e):{}},Et=kt,Tt=!I&&!i((function(){return 7!==Object.defineProperty(Et("div"),"a",{get:function(){return 7}}).a})),It=I,Ot=P,Rt=j,Pt=L,jt=$,Nt=_t,Mt=Xe,Dt=Tt,Bt=Object.getOwnPropertyDescriptor;T.f=It?Bt:function(e,t){if(e=jt(e),t=Nt(t),Dt)try{return Bt(e,t)}catch(n){}if(Mt(e,t))return Pt(!Ot(Rt.f,e,t),e[t])};var Ft=i,Lt=E,Ut=/#|\.prototype\./,Vt=function(e,t){var n=Ht[zt(e)];return n===qt||n!==Wt&&(Lt(t)?Ft(t):!!t)},zt=Vt.normalize=function(e){return String(e).replace(Ut,".").toLowerCase()},Ht=Vt.data={},Wt=Vt.NATIVE="N",qt=Vt.POLYFILL="P",Qt=Vt,Kt=Re,Zt=a,Yt=x(x.bind),Gt=function(e,t){return Kt(e),void 0===t?e:Zt?Yt(e,t):function(){return e.apply(t,arguments)}},$t={},Xt=I&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Jt=ee,en=String,tn=TypeError,nn=function(e){if(Jt(e))return e;throw new tn(en(e)+" is not an object")},rn=I,on=Tt,an=Xt,un=nn,cn=_t,ln=TypeError,sn=Object.defineProperty,fn=Object.getOwnPropertyDescriptor,vn="enumerable",dn="configurable",pn="writable";$t.f=rn?an?function(e,t,n){if(un(e),t=cn(t),un(n),"function"==typeof e&&"prototype"===t&&"value"in n&&pn in n&&!n[pn]){var r=fn(e,t);r&&r[pn]&&(e[t]=n.value,n={configurable:dn in n?n[dn]:r[dn],enumerable:vn in n?n[vn]:r[vn],writable:!1})}return sn(e,t,n)}:sn:function(e,t,n){if(un(e),t=cn(t),un(n),on)try{return sn(e,t,n)}catch(r){}if("get"in n||"set"in n)throw new ln("Accessors not supported");return"value"in n&&(e[t]=n.value),e};var hn=$t,gn=L,mn=I?function(e,t,n){return hn.f(e,t,gn(1,n))}:function(e,t,n){return e[t]=n,e},yn=o,bn=f,wn=x,An=E,_n=T.f,xn=Qt,Sn=te,Cn=Gt,kn=mn,En=Xe,Tn=function(e){var t=function(n,r,o){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(n);case 2:return new e(n,r)}return new e(n,r,o)}return bn(e,this,arguments)};return t.prototype=e.prototype,t},In=function(e,t){var n,r,o,i,a,u,c,l,s,f=e.target,v=e.global,d=e.stat,p=e.proto,h=v?yn:d?yn[f]:(yn[f]||{}).prototype,g=v?Sn:Sn[f]||kn(Sn,f,{})[f],m=g.prototype;for(i in t)r=!(n=xn(v?i:f+(d?".":"#")+i,e.forced))&&h&&En(h,i),u=g[i],r&&(c=e.dontCallGetSet?(s=_n(h,i))&&s.value:h[i]),a=r&&c?c:t[i],r&&typeof u==typeof a||(l=e.bind&&r?Cn(a,yn):e.wrap&&r?Tn(a):p&&An(a)?wn(a):a,(e.sham||a&&a.sham||u&&u.sham)&&kn(l,"sham",!0),kn(g,i,l),p&&(En(Sn,o=f+"Prototype")||kn(Sn,o,{}),kn(Sn[o],i,a),e.real&&m&&(n||!m[i])&&kn(m,i,a)))},On=g([].slice),Rn=g,Pn=Re,jn=ee,Nn=Xe,Mn=On,Dn=a,Bn=Function,Fn=Rn([].concat),Ln=Rn([].join),Un={},Vn=Dn?Bn.bind:function(e){var t=Pn(this),n=t.prototype,r=Mn(arguments,1),o=function(){var n=Fn(r,Mn(arguments));return this instanceof o?function(e,t,n){if(!Nn(Un,t)){for(var r=[],o=0;o<t;o++)r[o]="a["+o+"]";Un[t]=Bn("C,a","return new C("+Ln(r,",")+")")}return Un[t](e,n)}(t,n.length,n):t.apply(e,n)};return jn(n)&&(o.prototype=n),o},zn={};zn[vt("toStringTag")]="z";var Hn="[object z]"===String(zn),Wn=Hn,qn=E,Qn=w,Kn=vt("toStringTag"),Zn=Object,Yn="Arguments"===Qn(function(){return arguments}()),Gn=Wn?Qn:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Zn(e),Kn))?n:Yn?Qn(t):"Object"===(r=Qn(t))&&qn(t.callee)?"Arguments":r},$n=E,Xn=We,Jn=g(Function.toString);$n(Xn.inspectSource)||(Xn.inspectSource=function(e){return Jn(e)});var er=Xn.inspectSource,tr=g,nr=i,rr=E,or=Gn,ir=er,ar=function(){},ur=[],cr=ae("Reflect","construct"),lr=/^\s*(?:class|function)\b/,sr=tr(lr.exec),fr=!lr.test(ar),vr=function(e){if(!rr(e))return!1;try{return cr(ar,ur,e),!0}catch(t){return!1}},dr=function(e){if(!rr(e))return!1;switch(or(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return fr||!!sr(lr,ir(e))}catch(t){return!0}};dr.sham=!0;var pr=!cr||nr((function(){var e;return vr(vr.call)||!vr(Object)||!vr((function(){e=!0}))||e}))?dr:vr,hr=pr,gr=Ee,mr=TypeError,yr=function(e){if(hr(e))return e;throw new mr(gr(e)+" is not a constructor")},br={},wr=Math.ceil,Ar=Math.floor,_r=Math.trunc||function(e){var t=+e;return(t>0?Ar:wr)(t)},xr=_r,Sr=function(e){var t=+e;return t!=t||0===t?0:xr(t)},Cr=Sr,kr=Math.max,Er=Math.min,Tr=function(e,t){var n=Cr(e);return n<0?kr(n+t,0):Er(n,t)},Ir=Sr,Or=Math.min,Rr=function(e){return e>0?Or(Ir(e),9007199254740991):0},Pr=Rr,jr=function(e){return Pr(e.length)},Nr=$,Mr=Tr,Dr=jr,Br=function(e){return function(t,n,r){var o,i=Nr(t),a=Dr(i),u=Mr(r,a);if(e&&n!=n){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((e||u in i)&&i[u]===n)return e||u||0;return!e&&-1}},Fr={includes:Br(!0),indexOf:Br(!1)},Lr={},Ur=Xe,Vr=$,zr=Fr.indexOf,Hr=Lr,Wr=g([].push),qr=function(e,t){var n,r=Vr(e),o=0,i=[];for(n in r)!Ur(Hr,n)&&Ur(r,n)&&Wr(i,n);for(;t.length>o;)Ur(r,n=t[o++])&&(~zr(i,n)||Wr(i,n));return i},Qr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Kr=qr,Zr=Qr,Yr=Object.keys||function(e){return Kr(e,Zr)},Gr=I,$r=Xt,Xr=$t,Jr=nn,eo=$,to=Yr;br.f=Gr&&!$r?Object.defineProperties:function(e,t){Jr(e);for(var n,r=eo(t),o=to(t),i=o.length,a=0;i>a;)Xr.f(e,n=o[a++],r[n]);return e};var no,ro=ae("document","documentElement"),oo=rt,io=Qe("keys"),ao=function(e){return io[e]||(io[e]=oo(e))},uo=nn,co=br,lo=Qr,so=Lr,fo=ro,vo=kt,po="prototype",ho="script",go=ao("IE_PROTO"),mo=function(){},yo=function(e){return"<"+ho+">"+e+"</"+ho+">"},bo=function(e){e.write(yo("")),e.close();var t=e.parentWindow.Object;return e=null,t},wo=function(){try{no=new ActiveXObject("htmlfile")}catch(o){}var e,t,n;wo="undefined"!=typeof document?document.domain&&no?bo(no):(t=vo("iframe"),n="java"+ho+":",t.style.display="none",fo.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(yo("document.F=Object")),e.close(),e.F):bo(no);for(var r=lo.length;r--;)delete wo[po][lo[r]];return wo()};so[go]=!0;var Ao=Object.create||function(e,t){var n;return null!==e?(mo[po]=uo(e),n=new mo,mo[po]=null,n[go]=e):n=wo(),void 0===t?n:co.f(n,t)},_o=In,xo=f,So=Vn,Co=yr,ko=nn,Eo=ee,To=Ao,Io=i,Oo=ae("Reflect","construct"),Ro=Object.prototype,Po=[].push,jo=Io((function(){function e(){}return!(Oo((function(){}),[],e)instanceof e)})),No=!Io((function(){Oo((function(){}))})),Mo=jo||No;_o({target:"Reflect",stat:!0,forced:Mo,sham:Mo},{construct:function(e,t){Co(e),ko(t);var n=arguments.length<3?e:Co(arguments[2]);if(No&&!jo)return Oo(e,t,n);if(e===n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return xo(Po,r,t),new(xo(So,e,r))}var o=n.prototype,i=To(Eo(o)?o:Ro),a=xo(e,i,t);return Eo(a)?a:i}});const Do=n(te.Reflect.construct);var Bo=w,Fo=Array.isArray||function(e){return"Array"===Bo(e)},Lo=TypeError,Uo=function(e){if(e>9007199254740991)throw Lo("Maximum allowed index exceeded");return e},Vo=_t,zo=$t,Ho=L,Wo=function(e,t,n){var r=Vo(t);r in e?zo.f(e,r,Ho(0,n)):e[r]=n},qo=Fo,Qo=pr,Ko=ee,Zo=vt("species"),Yo=Array,Go=function(e){var t;return qo(e)&&(t=e.constructor,(Qo(t)&&(t===Yo||qo(t.prototype))||Ko(t)&&null===(t=t[Zo]))&&(t=void 0)),void 0===t?Yo:t},$o=function(e,t){return new(Go(e))(0===t?0:t)},Xo=i,Jo=he,ei=vt("species"),ti=function(e){return Jo>=51||!Xo((function(){var t=[];return(t.constructor={})[ei]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},ni=In,ri=i,oi=Fo,ii=ee,ai=Ye,ui=jr,ci=Uo,li=Wo,si=$o,fi=ti,vi=he,di=vt("isConcatSpreadable"),pi=vi>=51||!ri((function(){var e=[];return e[di]=!1,e.concat()[0]!==e})),hi=function(e){if(!ii(e))return!1;var t=e[di];return void 0!==t?!!t:oi(e)};ni({target:"Array",proto:!0,arity:1,forced:!pi||!fi("concat")},{concat:function(e){var t,n,r,o,i,a=ai(this),u=si(a,0),c=0;for(t=-1,r=arguments.length;t<r;t++)if(hi(i=-1===t?a:arguments[t]))for(o=ui(i),ci(c+o),n=0;n<o;n++,c++)n in i&&li(u,c,i[n]);else ci(c+1),li(u,c++,i);return u.length=c,u}});var gi=Gn,mi=String,yi=function(e){if("Symbol"===gi(e))throw new TypeError("Cannot convert a Symbol value to a string");return mi(e)},bi={},wi=qr,Ai=Qr.concat("length","prototype");bi.f=Object.getOwnPropertyNames||function(e){return wi(e,Ai)};var _i={},xi=Tr,Si=jr,Ci=Wo,ki=Array,Ei=Math.max,Ti=function(e,t,n){for(var r=Si(e),o=xi(t,r),i=xi(void 0===n?r:n,r),a=ki(Ei(i-o,0)),u=0;o<i;o++,u++)Ci(a,u,e[o]);return a.length=u,a},Ii=w,Oi=$,Ri=bi.f,Pi=Ti,ji="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];_i.f=function(e){return ji&&"Window"===Ii(e)?function(e){try{return Ri(e)}catch(t){return Pi(ji)}}(e):Ri(Oi(e))};var Ni={};Ni.f=Object.getOwnPropertySymbols;var Mi=mn,Di=function(e,t,n,r){return r&&r.enumerable?e[t]=n:Mi(e,t,n),e},Bi=$t,Fi=function(e,t,n){return Bi.f(e,t,n)},Li={},Ui=vt;Li.f=Ui;var Vi,zi,Hi,Wi=te,qi=Xe,Qi=Li,Ki=$t.f,Zi=function(e){var t=Wi.Symbol||(Wi.Symbol={});qi(t,e)||Ki(t,e,{value:Qi.f(e)})},Yi=P,Gi=ae,$i=vt,Xi=Di,Ji=function(){var e=Gi("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,r=$i("toPrimitive");t&&!t[r]&&Xi(t,r,(function(e){return Yi(n,this)}),{arity:1})},ea=Gn,ta=Hn?{}.toString:function(){return"[object "+ea(this)+"]"},na=Hn,ra=$t.f,oa=mn,ia=Xe,aa=ta,ua=vt("toStringTag"),ca=function(e,t,n,r){var o=n?e:e&&e.prototype;o&&(ia(o,ua)||ra(o,ua,{configurable:!0,value:t}),r&&!na&&oa(o,"toString",aa))},la=E,sa=o.WeakMap,fa=la(sa)&&/native code/.test(String(sa)),va=fa,da=o,pa=ee,ha=mn,ga=Xe,ma=We,ya=ao,ba=Lr,wa="Object already initialized",Aa=da.TypeError,_a=da.WeakMap;if(va||ma.state){var xa=ma.state||(ma.state=new _a);xa.get=xa.get,xa.has=xa.has,xa.set=xa.set,Vi=function(e,t){if(xa.has(e))throw new Aa(wa);return t.facade=e,xa.set(e,t),t},zi=function(e){return xa.get(e)||{}},Hi=function(e){return xa.has(e)}}else{var Sa=ya("state");ba[Sa]=!0,Vi=function(e,t){if(ga(e,Sa))throw new Aa(wa);return t.facade=e,ha(e,Sa,t),t},zi=function(e){return ga(e,Sa)?e[Sa]:{}},Hi=function(e){return ga(e,Sa)}}var Ca={set:Vi,get:zi,has:Hi,enforce:function(e){return Hi(e)?zi(e):Vi(e,{})},getterFor:function(e){return function(t){var n;if(!pa(t)||(n=zi(t)).type!==e)throw new Aa("Incompatible receiver, "+e+" required");return n}}},ka=Gt,Ea=W,Ta=Ye,Ia=jr,Oa=$o,Ra=g([].push),Pa=function(e){var t=1===e,n=2===e,r=3===e,o=4===e,i=6===e,a=7===e,u=5===e||i;return function(c,l,s,f){for(var v,d,p=Ta(c),h=Ea(p),g=Ia(h),m=ka(l,s),y=0,b=f||Oa,w=t?b(c,g):n||a?b(c,0):void 0;g>y;y++)if((u||y in h)&&(d=m(v=h[y],y,p),e))if(t)w[y]=d;else if(d)switch(e){case 3:return!0;case 5:return v;case 6:return y;case 2:Ra(w,v)}else switch(e){case 4:return!1;case 7:Ra(w,v)}return i?-1:r||o?o:w}},ja={forEach:Pa(0),map:Pa(1),filter:Pa(2),some:Pa(3),every:Pa(4),find:Pa(5),findIndex:Pa(6),filterReject:Pa(7)},Na=In,Ma=o,Da=P,Ba=g,Fa=I,La=be,Ua=i,Va=Xe,za=ue,Ha=nn,Wa=$,qa=_t,Qa=yi,Ka=L,Za=Ao,Ya=Yr,Ga=bi,$a=_i,Xa=Ni,Ja=T,eu=$t,tu=br,nu=j,ru=Di,ou=Fi,iu=Qe,au=Lr,uu=rt,cu=vt,lu=Li,su=Zi,fu=Ji,vu=ca,du=Ca,pu=ja.forEach,hu=ao("hidden"),gu="Symbol",mu="prototype",yu=du.set,bu=du.getterFor(gu),wu=Object[mu],Au=Ma.Symbol,_u=Au&&Au[mu],xu=Ma.RangeError,Su=Ma.TypeError,Cu=Ma.QObject,ku=Ja.f,Eu=eu.f,Tu=$a.f,Iu=nu.f,Ou=Ba([].push),Ru=iu("symbols"),Pu=iu("op-symbols"),ju=iu("wks"),Nu=!Cu||!Cu[mu]||!Cu[mu].findChild,Mu=function(e,t,n){var r=ku(wu,t);r&&delete wu[t],Eu(e,t,n),r&&e!==wu&&Eu(wu,t,r)},Du=Fa&&Ua((function(){return 7!==Za(Eu({},"a",{get:function(){return Eu(this,"a",{value:7}).a}})).a}))?Mu:Eu,Bu=function(e,t){var n=Ru[e]=Za(_u);return yu(n,{type:gu,tag:e,description:t}),Fa||(n.description=t),n},Fu=function(e,t,n){e===wu&&Fu(Pu,t,n),Ha(e);var r=qa(t);return Ha(n),Va(Ru,r)?(n.enumerable?(Va(e,hu)&&e[hu][r]&&(e[hu][r]=!1),n=Za(n,{enumerable:Ka(0,!1)})):(Va(e,hu)||Eu(e,hu,Ka(1,{})),e[hu][r]=!0),Du(e,r,n)):Eu(e,r,n)},Lu=function(e,t){Ha(e);var n=Wa(t),r=Ya(n).concat(Hu(n));return pu(r,(function(t){Fa&&!Da(Uu,n,t)||Fu(e,t,n[t])})),e},Uu=function(e){var t=qa(e),n=Da(Iu,this,t);return!(this===wu&&Va(Ru,t)&&!Va(Pu,t))&&(!(n||!Va(this,t)||!Va(Ru,t)||Va(this,hu)&&this[hu][t])||n)},Vu=function(e,t){var n=Wa(e),r=qa(t);if(n!==wu||!Va(Ru,r)||Va(Pu,r)){var o=ku(n,r);return!o||!Va(Ru,r)||Va(n,hu)&&n[hu][r]||(o.enumerable=!0),o}},zu=function(e){var t=Tu(Wa(e)),n=[];return pu(t,(function(e){Va(Ru,e)||Va(au,e)||Ou(n,e)})),n},Hu=function(e){var t=e===wu,n=Tu(t?Pu:Wa(e)),r=[];return pu(n,(function(e){!Va(Ru,e)||t&&!Va(wu,e)||Ou(r,Ru[e])})),r};La||(Au=function(){if(za(_u,this))throw new Su("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?Qa(arguments[0]):void 0,t=uu(e),n=function(e){var r=void 0===this?Ma:this;r===wu&&Da(n,Pu,e),Va(r,hu)&&Va(r[hu],t)&&(r[hu][t]=!1);var o=Ka(1,e);try{Du(r,t,o)}catch(i){if(!(i instanceof xu))throw i;Mu(r,t,o)}};return Fa&&Nu&&Du(wu,t,{configurable:!0,set:n}),Bu(t,e)},ru(_u=Au[mu],"toString",(function(){return bu(this).tag})),ru(Au,"withoutSetter",(function(e){return Bu(uu(e),e)})),nu.f=Uu,eu.f=Fu,tu.f=Lu,Ja.f=Vu,Ga.f=$a.f=zu,Xa.f=Hu,lu.f=function(e){return Bu(cu(e),e)},Fa&&ou(_u,"description",{configurable:!0,get:function(){return bu(this).description}})),Na({global:!0,constructor:!0,wrap:!0,forced:!La,sham:!La},{Symbol:Au}),pu(Ya(ju),(function(e){su(e)})),Na({target:gu,stat:!0,forced:!La},{useSetter:function(){Nu=!0},useSimple:function(){Nu=!1}}),Na({target:"Object",stat:!0,forced:!La,sham:!Fa},{create:function(e,t){return void 0===t?Za(e):Lu(Za(e),t)},defineProperty:Fu,defineProperties:Lu,getOwnPropertyDescriptor:Vu}),Na({target:"Object",stat:!0,forced:!La},{getOwnPropertyNames:zu}),fu(),vu(Au,gu),au[hu]=!0;var Wu=be&&!!Symbol.for&&!!Symbol.keyFor,qu=In,Qu=ae,Ku=Xe,Zu=yi,Yu=Qe,Gu=Wu,$u=Yu("string-to-symbol-registry"),Xu=Yu("symbol-to-string-registry");qu({target:"Symbol",stat:!0,forced:!Gu},{for:function(e){var t=Zu(e);if(Ku($u,t))return $u[t];var n=Qu("Symbol")(t);return $u[t]=n,Xu[n]=t,n}});var Ju=In,ec=Xe,tc=Ce,nc=Ee,rc=Wu,oc=Qe("symbol-to-string-registry");Ju({target:"Symbol",stat:!0,forced:!rc},{keyFor:function(e){if(!tc(e))throw new TypeError(nc(e)+" is not a symbol");if(ec(oc,e))return oc[e]}});var ic=Fo,ac=E,uc=w,cc=yi,lc=g([].push),sc=In,fc=ae,vc=f,dc=P,pc=g,hc=i,gc=E,mc=Ce,yc=On,bc=function(e){if(ac(e))return e;if(ic(e)){for(var t=e.length,n=[],r=0;r<t;r++){var o=e[r];"string"==typeof o?lc(n,o):"number"!=typeof o&&"Number"!==uc(o)&&"String"!==uc(o)||lc(n,cc(o))}var i=n.length,a=!0;return function(e,t){if(a)return a=!1,t;if(ic(this))return t;for(var r=0;r<i;r++)if(n[r]===e)return t}}},wc=be,Ac=String,_c=fc("JSON","stringify"),xc=pc(/./.exec),Sc=pc("".charAt),Cc=pc("".charCodeAt),kc=pc("".replace),Ec=pc(1..toString),Tc=/[\uD800-\uDFFF]/g,Ic=/^[\uD800-\uDBFF]$/,Oc=/^[\uDC00-\uDFFF]$/,Rc=!wc||hc((function(){var e=fc("Symbol")("stringify detection");return"[null]"!==_c([e])||"{}"!==_c({a:e})||"{}"!==_c(Object(e))})),Pc=hc((function(){return'"\\udf06\\ud834"'!==_c("\udf06\ud834")||'"\\udead"'!==_c("\udead")})),jc=function(e,t){var n=yc(arguments),r=bc(t);if(gc(r)||void 0!==e&&!mc(e))return n[1]=function(e,t){if(gc(r)&&(t=dc(r,this,Ac(e),t)),!mc(t))return t},vc(_c,null,n)},Nc=function(e,t,n){var r=Sc(n,t-1),o=Sc(n,t+1);return xc(Ic,e)&&!xc(Oc,o)||xc(Oc,e)&&!xc(Ic,r)?"\\u"+Ec(Cc(e,0),16):e};_c&&sc({target:"JSON",stat:!0,arity:3,forced:Rc||Pc},{stringify:function(e,t,n){var r=yc(arguments),o=vc(Rc?jc:_c,null,r);return Pc&&"string"==typeof o?kc(o,Tc,Nc):o}});var Mc=Ni,Dc=Ye;In({target:"Object",stat:!0,forced:!be||i((function(){Mc.f(1)}))},{getOwnPropertySymbols:function(e){var t=Mc.f;return t?t(Dc(e)):[]}}),Zi("asyncIterator"),Zi("hasInstance"),Zi("isConcatSpreadable"),Zi("iterator"),Zi("match"),Zi("matchAll"),Zi("replace"),Zi("search"),Zi("species"),Zi("split");var Bc=Ji;Zi("toPrimitive"),Bc();var Fc=ae,Lc=ca;Zi("toStringTag"),Lc(Fc("Symbol"),"Symbol"),Zi("unscopables"),ca(o.JSON,"JSON",!0);var Uc,Vc,zc,Hc=te.Symbol,Wc={},qc=I,Qc=Xe,Kc=Function.prototype,Zc=qc&&Object.getOwnPropertyDescriptor,Yc=Qc(Kc,"name"),Gc={EXISTS:Yc,PROPER:Yc&&"something"===function(){}.name,CONFIGURABLE:Yc&&(!qc||qc&&Zc(Kc,"name").configurable)},$c=!i((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),Xc=Xe,Jc=E,el=Ye,tl=$c,nl=ao("IE_PROTO"),rl=Object,ol=rl.prototype,il=tl?rl.getPrototypeOf:function(e){var t=el(e);if(Xc(t,nl))return t[nl];var n=t.constructor;return Jc(n)&&t instanceof n?n.prototype:t instanceof rl?ol:null},al=i,ul=E,cl=ee,ll=Ao,sl=il,fl=Di,vl=vt("iterator"),dl=!1;[].keys&&("next"in(zc=[].keys())?(Vc=sl(sl(zc)))!==Object.prototype&&(Uc=Vc):dl=!0);var pl=!cl(Uc)||al((function(){var e={};return Uc[vl].call(e)!==e}));ul((Uc=pl?{}:ll(Uc))[vl])||fl(Uc,vl,(function(){return this}));var hl={IteratorPrototype:Uc,BUGGY_SAFARI_ITERATORS:dl},gl=hl.IteratorPrototype,ml=Ao,yl=L,bl=ca,wl=Wc,Al=function(){return this},_l=function(e,t,n,r){var o=t+" Iterator";return e.prototype=ml(gl,{next:yl(+!r,n)}),bl(e,o,!1,!0),wl[o]=Al,e},xl=g,Sl=Re,Cl=E,kl=String,El=TypeError,Tl=function(e,t,n){try{return xl(Sl(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(r){}},Il=nn,Ol=function(e){if("object"==typeof e||Cl(e))return e;throw new El("Can't set "+kl(e)+" as a prototype")},Rl=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Tl(Object.prototype,"__proto__","set"))(n,[]),t=n instanceof Array}catch(r){}return function(n,r){return Il(n),Ol(r),t?e(n,r):n.__proto__=r,n}}():void 0),Pl=In,jl=P,Nl=Gc,Ml=_l,Dl=il,Bl=ca,Fl=Di,Ll=Wc,Ul=hl,Vl=Nl.PROPER,zl=Ul.BUGGY_SAFARI_ITERATORS,Hl=vt("iterator"),Wl="keys",ql="values",Ql="entries",Kl=function(){return this},Zl=function(e,t,n,r,o,i,a){Ml(n,t,r);var u,c,l,s=function(e){if(e===o&&h)return h;if(!zl&&e&&e in d)return d[e];switch(e){case Wl:case ql:case Ql:return function(){return new n(this,e)}}return function(){return new n(this)}},f=t+" Iterator",v=!1,d=e.prototype,p=d[Hl]||d["@@iterator"]||o&&d[o],h=!zl&&p||s(o),g="Array"===t&&d.entries||p;if(g&&(u=Dl(g.call(new e)))!==Object.prototype&&u.next&&(Bl(u,f,!0,!0),Ll[f]=Kl),Vl&&o===ql&&p&&p.name!==ql&&(v=!0,h=function(){return jl(p,this)}),o)if(c={values:s(ql),keys:i?h:s(Wl),entries:s(Ql)},a)for(l in c)(zl||v||!(l in d))&&Fl(d,l,c[l]);else Pl({target:t,proto:!0,forced:zl||v},c);return a&&d[Hl]!==h&&Fl(d,Hl,h,{name:o}),Ll[t]=h,c},Yl=function(e,t){return{value:e,done:t}},Gl=$,$l=Wc,Xl=Ca;$t.f;var Jl=Zl,es=Yl,ts="Array Iterator",ns=Xl.set,rs=Xl.getterFor(ts);Jl(Array,"Array",(function(e,t){ns(this,{type:ts,target:Gl(e),index:0,kind:t})}),(function(){var e=rs(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=void 0,es(void 0,!0);switch(e.kind){case"keys":return es(n,!1);case"values":return es(t[n],!1)}return es([n,t[n]],!1)}),"values"),$l.Arguments=$l.Array;var os={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},is=o,as=ca,us=Wc;for(var cs in os)as(is[cs],cs),us[cs]=us.Array;var ls=Hc,ss=vt,fs=$t.f,vs=ss("metadata"),ds=Function.prototype;void 0===ds[vs]&&fs(ds,vs,{value:null}),Zi("asyncDispose"),Zi("dispose"),Zi("metadata");var ps=ls,hs=g,gs=ae("Symbol"),ms=gs.keyFor,ys=hs(gs.prototype.valueOf),bs=gs.isRegisteredSymbol||function(e){try{return void 0!==ms(ys(e))}catch(t){return!1}};In({target:"Symbol",stat:!0},{isRegisteredSymbol:bs});for(var ws=Qe,As=ae,_s=g,xs=Ce,Ss=vt,Cs=As("Symbol"),ks=Cs.isWellKnownSymbol,Es=As("Object","getOwnPropertyNames"),Ts=_s(Cs.prototype.valueOf),Is=ws("wks"),Os=0,Rs=Es(Cs),Ps=Rs.length;Os<Ps;Os++)try{var js=Rs[Os];xs(Cs[js])&&Ss(js)}catch(kK){}var Ns=function(e){if(ks&&ks(e))return!0;try{for(var t=Ts(e),n=0,r=Es(Is),o=r.length;n<o;n++)if(Is[r[n]]==t)return!0}catch(kK){}return!1};In({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Ns}),Zi("matcher"),Zi("observable"),In({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:bs}),In({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Ns}),Zi("metadataKey"),Zi("patternMatch"),Zi("replaceAll");var Ms=ps;const Ds=n(Ms);const Bs=n(Ms);var Fs=g,Ls=Sr,Us=yi,Vs=Z,zs=Fs("".charAt),Hs=Fs("".charCodeAt),Ws=Fs("".slice),qs=function(e){return function(t,n){var r,o,i=Us(Vs(t)),a=Ls(n),u=i.length;return a<0||a>=u?e?"":void 0:(r=Hs(i,a))<55296||r>56319||a+1===u||(o=Hs(i,a+1))<56320||o>57343?e?zs(i,a):r:e?Ws(i,a,a+2):o-56320+(r-55296<<10)+65536}},Qs={codeAt:qs(!1),charAt:qs(!0)},Ks=Qs.charAt,Zs=yi,Ys=Ca,Gs=Zl,$s=Yl,Xs="String Iterator",Js=Ys.set,ef=Ys.getterFor(Xs);Gs(String,"String",(function(e){Js(this,{type:Xs,string:Zs(e),index:0})}),(function(){var e,t=ef(this),n=t.string,r=t.index;return r>=n.length?$s(void 0,!0):(e=Ks(n,r),t.index+=e.length,$s(e,!1))}));var tf=Gn,nf=Ne,rf=q,of=Wc,af=vt("iterator"),uf=function(e){if(!rf(e))return nf(e,af)||nf(e,"@@iterator")||of[tf(e)]},cf=uf;const lf=n(cf);const sf=n(cf);In({target:"Array",stat:!0},{isArray:Fo});var ff=te.Array.isArray;const vf=n(ff);const df=n(ff);var pf={exports:{}},hf=In,gf=I,mf=$t.f;hf({target:"Object",stat:!0,forced:Object.defineProperty!==mf,sham:!gf},{defineProperty:mf});var yf=te.Object,bf=pf.exports=function(e,t,n){return yf.defineProperty(e,t,n)};yf.defineProperty.sham&&(bf.sham=!0);var wf=pf.exports;const Af=n(wf);var _f=Li.f("iterator");const xf=n(_f);function Sf(e){return(Sf="function"==typeof Ds&&"symbol"==typeof xf?function(e){return typeof e}:function(e){return e&&"function"==typeof Ds&&e.constructor===Ds&&e!==Ds.prototype?"symbol":typeof e})(e)}const Cf=n(Li.f("toPrimitive"));function kf(e){var t=function(e,t){if("object"!=Sf(e)||!e)return e;var n=e[Cf];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Sf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Sf(t)?t:String(t)}function Ef(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Af(e,kf(r.key),r)}}function Tf(e,t,n){return t&&Ef(e.prototype,t),n&&Ef(e,n),Af(e,"prototype",{writable:!1}),e}function If(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}In({target:"Object",stat:!0,sham:!I},{create:Ao});var Of=te.Object,Rf=function(e,t){return Of.create(e,t)};In({target:"Object",stat:!0},{setPrototypeOf:Rl});var Pf=te.Object.setPrototypeOf,jf=Vn;In({target:"Function",proto:!0,forced:Function.bind!==jf},{bind:jf});var Nf=o,Mf=te,Df=function(e,t){var n=Mf[e+"Prototype"],r=n&&n[t];if(r)return r;var o=Nf[e],i=o&&o.prototype;return i&&i[t]},Bf=Df("Function","bind"),Ff=ue,Lf=Bf,Uf=Function.prototype,Vf=function(e){var t=e.bind;return e===Uf||Ff(Uf,e)&&t===Uf.bind?Lf:t},zf=Ye,Hf=il,Wf=$c;In({target:"Object",stat:!0,forced:i((function(){Hf(1)})),sham:!Wf},{getPrototypeOf:function(e){return Hf(zf(e))}});var qf=te.Object.getPrototypeOf;var Qf=I,Kf=Fo,Zf=TypeError,Yf=Object.getOwnPropertyDescriptor,Gf=Qf&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(kK){return kK instanceof TypeError}}()?function(e,t){if(Kf(e)&&!Yf(e,"length").writable)throw new Zf("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},$f=Ye,Xf=jr,Jf=Gf,ev=Uo;In({target:"Array",proto:!0,arity:1,forced:i((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(kK){return kK instanceof TypeError}}()},{push:function(e){var t=$f(this),n=Xf(t),r=arguments.length;ev(n+r);for(var o=0;o<r;o++)t[n]=arguments[o],n++;return Jf(t,n),n}});var tv=Df("Array","push"),nv=ue,rv=tv,ov=Array.prototype,iv=function(e){var t=e.push;return e===ov||nv(ov,e)&&t===ov.push?rv:t};const av=n(iv);var uv=In,cv=Fo,lv=pr,sv=ee,fv=Tr,vv=jr,dv=$,pv=Wo,hv=vt,gv=On,mv=ti("slice"),yv=hv("species"),bv=Array,wv=Math.max;uv({target:"Array",proto:!0,forced:!mv},{slice:function(e,t){var n,r,o,i=dv(this),a=vv(i),u=fv(e,a),c=fv(void 0===t?a:t,a);if(cv(i)&&(n=i.constructor,(lv(n)&&(n===bv||cv(n.prototype))||sv(n)&&null===(n=n[yv]))&&(n=void 0),n===bv||void 0===n))return gv(i,u,c);for(r=new(void 0===n?bv:n)(wv(c-u,0)),o=0;u<c;u++,o++)u in i&&pv(r,o,i[u]);return r.length=o,r}});var Av=Df("Array","slice"),_v=ue,xv=Av,Sv=Array.prototype,Cv=function(e){var t=e.slice;return e===Sv||_v(Sv,e)&&t===Sv.slice?xv:t};const kv=n(Cv);var Ev=P,Tv=nn,Iv=Ne,Ov=function(e,t,n){var r,o;Tv(e);try{if(!(r=Iv(e,"return"))){if("throw"===t)throw n;return n}r=Ev(r,e)}catch(kK){o=!0,r=kK}if("throw"===t)throw n;if(o)throw r;return Tv(r),n},Rv=nn,Pv=Ov,jv=Wc,Nv=vt("iterator"),Mv=Array.prototype,Dv=function(e){return void 0!==e&&(jv.Array===e||Mv[Nv]===e)},Bv=P,Fv=Re,Lv=nn,Uv=Ee,Vv=uf,zv=TypeError,Hv=function(e,t){var n=arguments.length<2?Vv(e):t;if(Fv(n))return Lv(Bv(n,e));throw new zv(Uv(e)+" is not iterable")},Wv=Gt,qv=P,Qv=Ye,Kv=function(e,t,n,r){try{return r?t(Rv(n)[0],n[1]):t(n)}catch(kK){Pv(e,"throw",kK)}},Zv=Dv,Yv=pr,Gv=jr,$v=Wo,Xv=Hv,Jv=uf,ed=Array,td=function(e){var t=Qv(e),n=Yv(this),r=arguments.length,o=r>1?arguments[1]:void 0,i=void 0!==o;i&&(o=Wv(o,r>2?arguments[2]:void 0));var a,u,c,l,s,f,v=Jv(t),d=0;if(!v||this===ed&&Zv(v))for(a=Gv(t),u=n?new this(a):ed(a);a>d;d++)f=i?o(t[d],d):t[d],$v(u,d,f);else for(s=(l=Xv(t,v)).next,u=n?new this:[];!(c=qv(s,l)).done;d++)f=i?Kv(l,o,[c.value,d],!0):c.value,$v(u,d,f);return u.length=d,u},nd=vt("iterator"),rd=!1;try{var od=0,id={next:function(){return{done:!!od++}},return:function(){rd=!0}};id[nd]=function(){return this},Array.from(id,(function(){throw 2}))}catch(kK){}var ad=function(e,t){try{if(!t&&!rd)return!1}catch(kK){return!1}var n=!1;try{var r={};r[nd]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(kK){}return n},ud=td;In({target:"Array",stat:!0,forced:!ad((function(e){Array.from(e)}))},{from:ud});var cd=te.Array.from;const ld=n(cd);function sd(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function fd(e,t){var n;if(e){if("string"==typeof e)return sd(e,t);var r=kv(n=Object.prototype.toString.call(e)).call(n,8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?ld(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?sd(e,t):void 0}}function vd(e,t){return function(e){if(vf(e))return e}(e)||function(e,t){var n=null==e?null:void 0!==Ds&&lf(e)||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(av(u).call(u,r.value),u.length!==t);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||fd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dd(e){return function(e){if(vf(e))return sd(e)}(e)||function(e){if(void 0!==Ds&&null!=lf(e)||null!=e["@@iterator"])return ld(e)}(e)||fd(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var pd=Df("Array","concat"),hd=ue,gd=pd,md=Array.prototype;const yd=n((function(e){var t=e.concat;return e===md||hd(md,e)&&t===md.concat?gd:t}));var bd=i,wd=function(e,t){var n=[][e];return!!n&&bd((function(){n.call(null,t||function(){return 1},1)}))},Ad=ja.forEach,_d=wd("forEach")?[].forEach:function(e){return Ad(this,e,arguments.length>1?arguments[1]:void 0)};In({target:"Array",proto:!0,forced:[].forEach!==_d},{forEach:_d});var xd=Df("Array","forEach"),Sd=Gn,Cd=Xe,kd=ue,Ed=xd,Td=Array.prototype,Id={DOMTokenList:!0,NodeList:!0},Od=function(e){var t=e.forEach;return e===Td||kd(Td,e)&&t===Td.forEach||Cd(Id,Sd(e))?Ed:t};const Rd=n(Od);var Pd=ee,jd=w,Nd=vt("match"),Md=function(e){var t;return Pd(e)&&(void 0!==(t=e[Nd])?!!t:"RegExp"===jd(e))},Dd=TypeError,Bd=function(e){if(Md(e))throw new Dd("The method doesn't accept regular expressions");return e},Fd=vt("match"),Ld=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[Fd]=!1,"/./"[e](t)}catch(r){}}return!1},Ud=In,Vd=x;T.f;var zd=Rr,Hd=yi,Wd=Bd,qd=Z,Qd=Ld,Kd=Vd("".startsWith),Zd=Vd("".slice),Yd=Math.min;Ud({target:"String",proto:!0,forced:!Qd("startsWith")},{startsWith:function(e){var t=Hd(qd(this));Wd(e);var n=zd(Yd(arguments.length>1?arguments[1]:void 0,t.length)),r=Hd(e);return Kd?Kd(t,r,n):Zd(t,n,n+r.length)===r}});var Gd=Df("String","startsWith"),$d=ue,Xd=Gd,Jd=String.prototype;const ep=n((function(e){var t=e.startsWith;return"string"==typeof e||e===Jd||$d(Jd,e)&&t===Jd.startsWith?Xd:t}));const tp=n(Cv);var np=Fr.includes;In({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function(e){return np(this,e,arguments.length>1?arguments[1]:void 0)}});var rp=Df("Array","includes"),op=In,ip=Bd,ap=Z,up=yi,cp=Ld,lp=g("".indexOf);op({target:"String",proto:!0,forced:!cp("includes")},{includes:function(e){return!!~lp(up(ap(this)),up(ip(e)),arguments.length>1?arguments[1]:void 0)}});var sp=Df("String","includes"),fp=ue,vp=rp,dp=sp,pp=Array.prototype,hp=String.prototype;const gp=n((function(e){var t=e.includes;return e===pp||fp(pp,e)&&t===pp.includes?vp:"string"==typeof e||e===hp||fp(hp,e)&&t===hp.includes?dp:t}));var mp=ae,yp=bi,bp=Ni,wp=nn,Ap=g([].concat),_p=mp("Reflect","ownKeys")||function(e){var t=yp.f(wp(e)),n=bp.f;return n?Ap(t,n(e)):t},xp=Xe,Sp=_p,Cp=T,kp=$t,Ep=ee,Tp=mn,Ip=Error,Op=g("".replace),Rp=String(new Ip("zxcasd").stack),Pp=/\n\s*at [^:]*:[^\n]*/,jp=Pp.test(Rp),Np=L,Mp=!i((function(){var e=new Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",Np(1,7)),7!==e.stack)})),Dp=mn,Bp=function(e,t){if(jp&&"string"==typeof e&&!Ip.prepareStackTrace)for(;t--;)e=Op(e,Pp,"");return e},Fp=Mp,Lp=Error.captureStackTrace,Up=Gt,Vp=P,zp=nn,Hp=Ee,Wp=Dv,qp=jr,Qp=ue,Kp=Hv,Zp=uf,Yp=Ov,Gp=TypeError,$p=function(e,t){this.stopped=e,this.result=t},Xp=$p.prototype,Jp=function(e,t,n){var r,o,i,a,u,c,l,s=n&&n.that,f=!(!n||!n.AS_ENTRIES),v=!(!n||!n.IS_RECORD),d=!(!n||!n.IS_ITERATOR),p=!(!n||!n.INTERRUPTED),h=Up(t,s),g=function(e){return r&&Yp(r,"normal",e),new $p(!0,e)},m=function(e){return f?(zp(e),p?h(e[0],e[1],g):h(e[0],e[1])):p?h(e,g):h(e)};if(v)r=e.iterator;else if(d)r=e;else{if(!(o=Zp(e)))throw new Gp(Hp(e)+" is not iterable");if(Wp(o)){for(i=0,a=qp(e);a>i;i++)if((u=m(e[i]))&&Qp(Xp,u))return u;return new $p(!1)}r=Kp(e,o)}for(c=v?e.next:r.next;!(l=Vp(c,r)).done;){try{u=m(l.value)}catch(kK){Yp(r,"throw",kK)}if("object"==typeof u&&u&&Qp(Xp,u))return u}return new $p(!1)},eh=yi,th=In,nh=ue,rh=il,oh=Rl,ih=function(e,t,n){for(var r=Sp(t),o=kp.f,i=Cp.f,a=0;a<r.length;a++){var u=r[a];xp(e,u)||n&&xp(n,u)||o(e,u,i(t,u))}},ah=Ao,uh=mn,ch=L,lh=function(e,t){Ep(t)&&"cause"in t&&Tp(e,"cause",t.cause)},sh=function(e,t,n,r){Fp&&(Lp?Lp(e,t):Dp(e,"stack",Bp(n,r)))},fh=Jp,vh=function(e,t){return void 0===e?arguments.length<2?"":t:eh(e)},dh=vt("toStringTag"),ph=Error,hh=[].push,gh=function(e,t){var n,r=nh(mh,this);oh?n=oh(new ph,r?rh(this):mh):(n=r?this:ah(mh),uh(n,dh,"Error")),void 0!==t&&uh(n,"message",vh(t)),sh(n,gh,n.stack,1),arguments.length>2&&lh(n,arguments[2]);var o=[];return fh(e,hh,{that:o}),uh(n,"errors",o),n};oh?oh(gh,ph):ih(gh,ph,{name:!0});var mh=gh.prototype=ah(ph.prototype,{constructor:ch(1,gh),message:ch(1,""),name:ch(1,"AggregateError")});th({global:!0,constructor:!0,arity:2},{AggregateError:gh});var yh,bh,wh,Ah,_h="process"===w(o.process),xh=ae,Sh=Fi,Ch=I,kh=vt("species"),Eh=function(e){var t=xh(e);Ch&&t&&!t[kh]&&Sh(t,kh,{configurable:!0,get:function(){return this}})},Th=ue,Ih=TypeError,Oh=function(e,t){if(Th(t,e))return e;throw new Ih("Incorrect invocation")},Rh=nn,Ph=yr,jh=q,Nh=vt("species"),Mh=function(e,t){var n,r=Rh(e).constructor;return void 0===r||jh(n=Rh(r)[Nh])?t:Ph(n)},Dh=TypeError,Bh=function(e,t){if(e<t)throw new Dh("Not enough arguments");return e},Fh=/(?:ipad|iphone|ipod).*applewebkit/i.test(ce),Lh=o,Uh=f,Vh=Gt,zh=E,Hh=Xe,Wh=i,qh=ro,Qh=On,Kh=kt,Zh=Bh,Yh=Fh,Gh=_h,$h=Lh.setImmediate,Xh=Lh.clearImmediate,Jh=Lh.process,eg=Lh.Dispatch,tg=Lh.Function,ng=Lh.MessageChannel,rg=Lh.String,og=0,ig={},ag="onreadystatechange";Wh((function(){yh=Lh.location}));var ug=function(e){if(Hh(ig,e)){var t=ig[e];delete ig[e],t()}},cg=function(e){return function(){ug(e)}},lg=function(e){ug(e.data)},sg=function(e){Lh.postMessage(rg(e),yh.protocol+"//"+yh.host)};$h&&Xh||($h=function(e){Zh(arguments.length,1);var t=zh(e)?e:tg(e),n=Qh(arguments,1);return ig[++og]=function(){Uh(t,void 0,n)},bh(og),og},Xh=function(e){delete ig[e]},Gh?bh=function(e){Jh.nextTick(cg(e))}:eg&&eg.now?bh=function(e){eg.now(cg(e))}:ng&&!Yh?(Ah=(wh=new ng).port2,wh.port1.onmessage=lg,bh=Vh(Ah.postMessage,Ah)):Lh.addEventListener&&zh(Lh.postMessage)&&!Lh.importScripts&&yh&&"file:"!==yh.protocol&&!Wh(sg)?(bh=sg,Lh.addEventListener("message",lg,!1)):bh=ag in Kh("script")?function(e){qh.appendChild(Kh("script"))[ag]=function(){qh.removeChild(this),ug(e)}}:function(e){setTimeout(cg(e),0)});var fg={set:$h,clear:Xh},vg=function(){this.head=null,this.tail=null};vg.prototype={add:function(e){var t={item:e,next:null},n=this.tail;n?n.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var dg,pg,hg,gg,mg,yg=vg,bg=/ipad|iphone|ipod/i.test(ce)&&"undefined"!=typeof Pebble,wg=/web0s(?!.*chrome)/i.test(ce),Ag=o,_g=Gt,xg=T.f,Sg=fg.set,Cg=yg,kg=Fh,Eg=bg,Tg=wg,Ig=_h,Og=Ag.MutationObserver||Ag.WebKitMutationObserver,Rg=Ag.document,Pg=Ag.process,jg=Ag.Promise,Ng=xg(Ag,"queueMicrotask"),Mg=Ng&&Ng.value;if(!Mg){var Dg=new Cg,Bg=function(){var e,t;for(Ig&&(e=Pg.domain)&&e.exit();t=Dg.get();)try{t()}catch(kK){throw Dg.head&&dg(),kK}e&&e.enter()};kg||Ig||Tg||!Og||!Rg?!Eg&&jg&&jg.resolve?((gg=jg.resolve(void 0)).constructor=jg,mg=_g(gg.then,gg),dg=function(){mg(Bg)}):Ig?dg=function(){Pg.nextTick(Bg)}:(Sg=_g(Sg,Ag),dg=function(){Sg(Bg)}):(pg=!0,hg=Rg.createTextNode(""),new Og(Bg).observe(hg,{characterData:!0}),dg=function(){hg.data=pg=!pg}),Mg=function(e){Dg.head||dg(),Dg.add(e)}}var Fg=Mg,Lg=function(e){try{return{error:!1,value:e()}}catch(kK){return{error:!0,value:kK}}},Ug=o.Promise,Vg="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,zg=!Vg&&!_h&&"object"==typeof window&&"object"==typeof document,Hg=o,Wg=Ug,qg=E,Qg=Qt,Kg=er,Zg=vt,Yg=zg,Gg=Vg,$g=he,Xg=Wg&&Wg.prototype,Jg=Zg("species"),em=!1,tm=qg(Hg.PromiseRejectionEvent),nm={CONSTRUCTOR:Qg("Promise",(function(){var e=Kg(Wg),t=e!==String(Wg);if(!t&&66===$g)return!0;if(!Xg.catch||!Xg.finally)return!0;if(!$g||$g<51||!/native code/.test(e)){var n=new Wg((function(e){e(1)})),r=function(e){e((function(){}),(function(){}))};if((n.constructor={})[Jg]=r,!(em=n.then((function(){}))instanceof r))return!0}return!t&&(Yg||Gg)&&!tm})),REJECTION_EVENT:tm,SUBCLASSING:em},rm={},om=Re,im=TypeError,am=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw new im("Bad Promise constructor");t=e,n=r})),this.resolve=om(t),this.reject=om(n)};rm.f=function(e){return new am(e)};var um,cm,lm=In,sm=_h,fm=o,vm=P,dm=Di,pm=ca,hm=Eh,gm=Re,mm=E,ym=ee,bm=Oh,wm=Mh,Am=fg.set,_m=Fg,xm=function(e,t){},Sm=Lg,Cm=yg,km=Ca,Em=Ug,Tm=nm,Im=rm,Om="Promise",Rm=Tm.CONSTRUCTOR,Pm=Tm.REJECTION_EVENT,jm=km.getterFor(Om),Nm=km.set,Mm=Em&&Em.prototype,Dm=Em,Bm=Mm,Fm=fm.TypeError,Lm=fm.document,Um=fm.process,Vm=Im.f,zm=Vm,Hm=!!(Lm&&Lm.createEvent&&fm.dispatchEvent),Wm="unhandledrejection",qm=function(e){var t;return!(!ym(e)||!mm(t=e.then))&&t},Qm=function(e,t){var n,r,o,i=t.value,a=1===t.state,u=a?e.ok:e.fail,c=e.resolve,l=e.reject,s=e.domain;try{u?(a||(2===t.rejection&&$m(t),t.rejection=1),!0===u?n=i:(s&&s.enter(),n=u(i),s&&(s.exit(),o=!0)),n===e.promise?l(new Fm("Promise-chain cycle")):(r=qm(n))?vm(r,n,c,l):c(n)):l(i)}catch(kK){s&&!o&&s.exit(),l(kK)}},Km=function(e,t){e.notified||(e.notified=!0,_m((function(){for(var n,r=e.reactions;n=r.get();)Qm(n,e);e.notified=!1,t&&!e.rejection&&Ym(e)})))},Zm=function(e,t,n){var r,o;Hm?((r=Lm.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),fm.dispatchEvent(r)):r={promise:t,reason:n},!Pm&&(o=fm["on"+e])?o(r):e===Wm&&xm("Unhandled promise rejection",n)},Ym=function(e){vm(Am,fm,(function(){var t,n=e.facade,r=e.value;if(Gm(e)&&(t=Sm((function(){sm?Um.emit("unhandledRejection",r,n):Zm(Wm,n,r)})),e.rejection=sm||Gm(e)?2:1,t.error))throw t.value}))},Gm=function(e){return 1!==e.rejection&&!e.parent},$m=function(e){vm(Am,fm,(function(){var t=e.facade;sm?Um.emit("rejectionHandled",t):Zm("rejectionhandled",t,e.value)}))},Xm=function(e,t,n){return function(r){e(t,r,n)}},Jm=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,Km(e,!0))},ey=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw new Fm("Promise can't be resolved itself");var r=qm(t);r?_m((function(){var n={done:!1};try{vm(r,t,Xm(ey,n,e),Xm(Jm,n,e))}catch(kK){Jm(n,kK,e)}})):(e.value=t,e.state=1,Km(e,!1))}catch(kK){Jm({done:!1},kK,e)}}};Rm&&(Bm=(Dm=function(e){bm(this,Bm),gm(e),vm(um,this);var t=jm(this);try{e(Xm(ey,t),Xm(Jm,t))}catch(kK){Jm(t,kK)}}).prototype,(um=function(e){Nm(this,{type:Om,done:!1,notified:!1,parent:!1,reactions:new Cm,rejection:!1,state:0,value:void 0})}).prototype=dm(Bm,"then",(function(e,t){var n=jm(this),r=Vm(wm(this,Dm));return n.parent=!0,r.ok=!mm(e)||e,r.fail=mm(t)&&t,r.domain=sm?Um.domain:void 0,0===n.state?n.reactions.add(r):_m((function(){Qm(r,n)})),r.promise})),cm=function(){var e=new um,t=jm(e);this.promise=e,this.resolve=Xm(ey,t),this.reject=Xm(Jm,t)},Im.f=Vm=function(e){return e===Dm||undefined===e?new cm(e):zm(e)}),lm({global:!0,constructor:!0,wrap:!0,forced:Rm},{Promise:Dm}),pm(Dm,Om,!1,!0),hm(Om);var ty=Ug,ny=nm.CONSTRUCTOR||!ad((function(e){ty.all(e).then(void 0,(function(){}))})),ry=P,oy=Re,iy=rm,ay=Lg,uy=Jp;In({target:"Promise",stat:!0,forced:ny},{all:function(e){var t=this,n=iy.f(t),r=n.resolve,o=n.reject,i=ay((function(){var n=oy(t.resolve),i=[],a=0,u=1;uy(e,(function(e){var c=a++,l=!1;u++,ry(n,t,e).then((function(e){l||(l=!0,i[c]=e,--u||r(i))}),o)})),--u||r(i)}));return i.error&&o(i.value),n.promise}});var cy=In,ly=nm.CONSTRUCTOR;Ug&&Ug.prototype,cy({target:"Promise",proto:!0,forced:ly,real:!0},{catch:function(e){return this.then(void 0,e)}});var sy=P,fy=Re,vy=rm,dy=Lg,py=Jp;In({target:"Promise",stat:!0,forced:ny},{race:function(e){var t=this,n=vy.f(t),r=n.reject,o=dy((function(){var o=fy(t.resolve);py(e,(function(e){sy(o,t,e).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}});var hy=P,gy=rm;In({target:"Promise",stat:!0,forced:nm.CONSTRUCTOR},{reject:function(e){var t=gy.f(this);return hy(t.reject,void 0,e),t.promise}});var my=nn,yy=ee,by=rm,wy=function(e,t){if(my(e),yy(t)&&t.constructor===e)return t;var n=by.f(e);return(0,n.resolve)(t),n.promise},Ay=In,_y=Ug,xy=nm.CONSTRUCTOR,Sy=wy,Cy=ae("Promise"),ky=!xy;Ay({target:"Promise",stat:!0,forced:true},{resolve:function(e){return Sy(ky&&this===Cy?_y:this,e)}});var Ey=P,Ty=Re,Iy=rm,Oy=Lg,Ry=Jp;In({target:"Promise",stat:!0,forced:ny},{allSettled:function(e){var t=this,n=Iy.f(t),r=n.resolve,o=n.reject,i=Oy((function(){var n=Ty(t.resolve),o=[],i=0,a=1;Ry(e,(function(e){var u=i++,c=!1;a++,Ey(n,t,e).then((function(e){c||(c=!0,o[u]={status:"fulfilled",value:e},--a||r(o))}),(function(e){c||(c=!0,o[u]={status:"rejected",reason:e},--a||r(o))}))})),--a||r(o)}));return i.error&&o(i.value),n.promise}});var Py=P,jy=Re,Ny=ae,My=rm,Dy=Lg,By=Jp,Fy="No one promise resolved";In({target:"Promise",stat:!0,forced:ny},{any:function(e){var t=this,n=Ny("AggregateError"),r=My.f(t),o=r.resolve,i=r.reject,a=Dy((function(){var r=jy(t.resolve),a=[],u=0,c=1,l=!1;By(e,(function(e){var s=u++,f=!1;c++,Py(r,t,e).then((function(e){f||l||(l=!0,o(e))}),(function(e){f||l||(f=!0,a[s]=e,--c||i(new n(a,Fy)))}))})),--c||i(new n(a,Fy))}));return a.error&&i(a.value),r.promise}});var Ly=rm;In({target:"Promise",stat:!0},{withResolvers:function(){var e=Ly.f(this);return{promise:e.promise,resolve:e.resolve,reject:e.reject}}});var Uy=In,Vy=Ug,zy=i,Hy=ae,Wy=E,qy=Mh,Qy=wy,Ky=Vy&&Vy.prototype;Uy({target:"Promise",proto:!0,real:!0,forced:!!Vy&&zy((function(){Ky.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=qy(this,Hy("Promise")),n=Wy(e);return this.then(n?function(n){return Qy(t,e()).then((function(){return n}))}:e,n?function(n){return Qy(t,e()).then((function(){throw n}))}:e)}});var Zy=te.Promise,Yy=rm,Gy=Lg;In({target:"Promise",stat:!0,forced:!0},{try:function(e){var t=Yy.f(this),n=Gy(e);return(n.error?t.reject:t.resolve)(n.value),t.promise}});var $y=Zy;const Xy=n($y);const Jy=n($y);var eb=In,tb=Date,nb=g(tb.prototype.getTime);eb({target:"Date",stat:!0},{now:function(){return nb(new tb)}});const rb=n(te.Date.now);var ob=ja.map;In({target:"Array",proto:!0,forced:!ti("map")},{map:function(e){return ob(this,e,arguments.length>1?arguments[1]:void 0)}});var ib=Df("Array","map"),ab=ue,ub=ib,cb=Array.prototype;const lb=n((function(e){var t=e.map;return e===cb||ab(cb,e)&&t===cb.map?ub:t}));const sb=n(Rf);var fb=Ye,vb=Yr;In({target:"Object",stat:!0,forced:i((function(){vb(1)}))},{keys:function(e){return vb(fb(e))}});const db=n(te.Object.keys);const pb=n(wf);const hb=n(cd);var gb=ja.some;In({target:"Array",proto:!0,forced:!wd("some")},{some:function(e){return gb(this,e,arguments.length>1?arguments[1]:void 0)}});var mb=Df("Array","some"),yb=ue,bb=mb,wb=Array.prototype;const Ab=n((function(e){var t=e.some;return e===wb||yb(wb,e)&&t===wb.some?bb:t}));var _b={exports:{}},xb=i((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),Sb=i,Cb=ee,kb=w,Eb=xb,Tb=Object.isExtensible,Ib=Sb((function(){Tb(1)}))||Eb?function(e){return!!Cb(e)&&((!Eb||"ArrayBuffer"!==kb(e))&&(!Tb||Tb(e)))}:Tb,Ob=!i((function(){return Object.isExtensible(Object.preventExtensions({}))})),Rb=In,Pb=g,jb=Lr,Nb=ee,Mb=Xe,Db=$t.f,Bb=bi,Fb=_i,Lb=Ib,Ub=Ob,Vb=!1,zb=rt("meta"),Hb=0,Wb=function(e){Db(e,zb,{value:{objectID:"O"+Hb++,weakData:{}}})},qb=_b.exports={enable:function(){qb.enable=function(){},Vb=!0;var e=Bb.f,t=Pb([].splice),n={};n[zb]=1,e(n).length&&(Bb.f=function(n){for(var r=e(n),o=0,i=r.length;o<i;o++)if(r[o]===zb){t(r,o,1);break}return r},Rb({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Fb.f}))},fastKey:function(e,t){if(!Nb(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!Mb(e,zb)){if(!Lb(e))return"F";if(!t)return"E";Wb(e)}return e[zb].objectID},getWeakData:function(e,t){if(!Mb(e,zb)){if(!Lb(e))return!0;if(!t)return!1;Wb(e)}return e[zb].weakData},onFreeze:function(e){return Ub&&Vb&&Lb(e)&&!Mb(e,zb)&&Wb(e),e}};jb[zb]=!0;var Qb=_b.exports,Kb=In,Zb=o,Yb=Qb,Gb=i,$b=mn,Xb=Jp,Jb=Oh,ew=E,tw=ee,nw=q,rw=ca,ow=$t.f,iw=ja.forEach,aw=I,uw=Ca.set,cw=Ca.getterFor,lw=function(e,t,n){var r,o=-1!==e.indexOf("Map"),i=-1!==e.indexOf("Weak"),a=o?"set":"add",u=Zb[e],c=u&&u.prototype,l={};if(aw&&ew(u)&&(i||c.forEach&&!Gb((function(){(new u).entries().next()})))){var s=(r=t((function(t,n){uw(Jb(t,s),{type:e,collection:new u}),nw(n)||Xb(n,t[a],{that:t,AS_ENTRIES:o})}))).prototype,f=cw(e);iw(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"===e||"set"===e;!(e in c)||i&&"clear"===e||$b(s,e,(function(n,r){var o=f(this).collection;if(!t&&i&&!tw(n))return"get"===e&&void 0;var a=o[e](0===n?0:n,r);return t?this:a}))})),i||ow(s,"size",{configurable:!0,get:function(){return f(this).collection.size}})}else r=n.getConstructor(t,e,o,a),Yb.enable();return rw(r,e,!1,!0),l[e]=r,Kb({global:!0,forced:!0},l),i||n.setStrong(r,e,o),r},sw=Di,fw=function(e,t,n){for(var r in t)n&&n.unsafe&&e[r]?e[r]=t[r]:sw(e,r,t[r],n);return e},vw=Ao,dw=Fi,pw=fw,hw=Gt,gw=Oh,mw=q,yw=Jp,bw=Zl,ww=Yl,Aw=Eh,_w=I,xw=Qb.fastKey,Sw=Ca.set,Cw=Ca.getterFor,kw={getConstructor:function(e,t,n,r){var o=e((function(e,o){gw(e,i),Sw(e,{type:t,index:vw(null),first:void 0,last:void 0,size:0}),_w||(e.size=0),mw(o)||yw(o,e[r],{that:e,AS_ENTRIES:n})})),i=o.prototype,a=Cw(t),u=function(e,t,n){var r,o,i=a(e),u=c(e,t);return u?u.value=n:(i.last=u={index:o=xw(t,!0),key:t,value:n,previous:r=i.last,next:void 0,removed:!1},i.first||(i.first=u),r&&(r.next=u),_w?i.size++:e.size++,"F"!==o&&(i.index[o]=u)),e},c=function(e,t){var n,r=a(e),o=xw(t);if("F"!==o)return r.index[o];for(n=r.first;n;n=n.next)if(n.key===t)return n};return pw(i,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,_w?e.size=0:this.size=0},delete:function(e){var t=this,n=a(t),r=c(t,e);if(r){var o=r.next,i=r.previous;delete n.index[r.index],r.removed=!0,i&&(i.next=o),o&&(o.previous=i),n.first===r&&(n.first=o),n.last===r&&(n.last=i),_w?n.size--:t.size--}return!!r},forEach:function(e){for(var t,n=a(this),r=hw(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),pw(i,n?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return u(this,0===e?0:e,t)}}:{add:function(e){return u(this,e=0===e?0:e,e)}}),_w&&dw(i,"size",{configurable:!0,get:function(){return a(this).size}}),o},setStrong:function(e,t,n){var r=t+" Iterator",o=Cw(t),i=Cw(r);bw(e,t,(function(e,t){Sw(this,{type:r,target:e,state:o(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?ww("keys"===t?n.key:"values"===t?n.value:[n.key,n.value],!1):(e.target=void 0,ww(void 0,!0))}),n?"entries":"values",!n,!0),Aw(t)}};lw("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),kw);var Ew=te.Set,Tw=Ee,Iw=TypeError,Ow=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"add"in e&&"delete"in e&&"keys"in e)return e;throw new Iw(Tw(e)+" is not a set")},Rw=function(e,t){return 1===t?function(t,n){return t[e](n)}:function(t,n,r){return t[e](n,r)}},Pw=Rw,jw=ae("Set"),Nw=jw.prototype,Mw={Set:jw,add:Pw("add",1),has:Pw("has",1),remove:Pw("delete",1),proto:Nw},Dw=P,Bw=function(e,t,n){for(var r,o,i=n?e:e.iterator,a=e.next;!(r=Dw(a,i)).done;)if(void 0!==(o=t(r.value)))return o},Fw=Bw,Lw=function(e,t,n){return n?Fw(e.keys(),t,!0):e.forEach(t)},Uw=Lw,Vw=Mw.Set,zw=Mw.add,Hw=function(e){var t=new Vw;return Uw(e,(function(e){zw(t,e)})),t},Ww=function(e){return e.size},qw=Re,Qw=nn,Kw=P,Zw=Sr,Yw=function(e){return{iterator:e,next:e.next,done:!1}},Gw="Invalid size",$w=RangeError,Xw=TypeError,Jw=Math.max,eA=function(e,t,n,r){this.set=e,this.size=t,this.has=n,this.keys=r};eA.prototype={getIterator:function(){return Yw(Qw(Kw(this.keys,this.set)))},includes:function(e){return Kw(this.has,this.set,e)}};var tA=function(e){Qw(e);var t=+e.size;if(t!=t)throw new Xw(Gw);var n=Zw(t);if(n<0)throw new $w(Gw);return new eA(e,Jw(n,0),qw(e.has),qw(e.keys))},nA=Ow,rA=Hw,oA=Ww,iA=tA,aA=Lw,uA=Bw,cA=Mw.has,lA=Mw.remove,sA=function(e){var t=nA(this),n=iA(e),r=rA(t);return oA(t)<=n.size?aA(t,(function(e){n.includes(e)&&lA(r,e)})):uA(n.getIterator(),(function(e){cA(t,e)&&lA(r,e)})),r},fA=function(){return!1},vA=sA;In({target:"Set",proto:!0,real:!0,forced:!fA()},{difference:vA});var dA=Ow,pA=Ww,hA=tA,gA=Lw,mA=Bw,yA=Mw.Set,bA=Mw.add,wA=Mw.has,AA=function(e){var t=dA(this),n=hA(e),r=new yA;return pA(t)>n.size?mA(n.getIterator(),(function(e){wA(t,e)&&bA(r,e)})):gA(t,(function(e){n.includes(e)&&bA(r,e)})),r},_A=AA;In({target:"Set",proto:!0,real:!0,forced:!fA()},{intersection:_A});var xA=Ow,SA=Mw.has,CA=Ww,kA=tA,EA=Lw,TA=Bw,IA=Ov,OA=function(e){var t=xA(this),n=kA(e);if(CA(t)<=n.size)return!1!==EA(t,(function(e){if(n.includes(e))return!1}),!0);var r=n.getIterator();return!1!==TA(r,(function(e){if(SA(t,e))return IA(r,"normal",!1)}))},RA=OA;In({target:"Set",proto:!0,real:!0,forced:!fA()},{isDisjointFrom:RA});var PA=Ow,jA=Ww,NA=Lw,MA=tA,DA=function(e){var t=PA(this),n=MA(e);return!(jA(t)>n.size)&&!1!==NA(t,(function(e){if(!n.includes(e))return!1}),!0)},BA=DA;In({target:"Set",proto:!0,real:!0,forced:!fA()},{isSubsetOf:BA});var FA=Ow,LA=Mw.has,UA=Ww,VA=tA,zA=Bw,HA=Ov,WA=function(e){var t=FA(this),n=VA(e);if(UA(t)<n.size)return!1;var r=n.getIterator();return!1!==zA(r,(function(e){if(!LA(t,e))return HA(r,"normal",!1)}))},qA=WA;In({target:"Set",proto:!0,real:!0,forced:!fA()},{isSupersetOf:qA});var QA=Ow,KA=Hw,ZA=tA,YA=Bw,GA=Mw.add,$A=Mw.has,XA=Mw.remove,JA=function(e){var t=QA(this),n=ZA(e).getIterator(),r=KA(t);return YA(n,(function(e){$A(t,e)?XA(r,e):GA(r,e)})),r},e_=JA;In({target:"Set",proto:!0,real:!0,forced:!fA()},{symmetricDifference:e_});var t_=Ow,n_=Mw.add,r_=Hw,o_=tA,i_=Bw,a_=function(e){var t=t_(this),n=o_(e).getIterator(),r=r_(t);return i_(n,(function(e){n_(r,e)})),r},u_=a_;In({target:"Set",proto:!0,real:!0,forced:!fA()},{union:u_});var c_=Ew,l_=Gt,s_=P,f_=Re,v_=yr,d_=q,p_=Jp,h_=[].push,g_=function(e){var t,n,r,o,i=arguments.length,a=i>1?arguments[1]:void 0;return v_(this),(t=void 0!==a)&&f_(a),d_(e)?new this:(n=[],t?(r=0,o=l_(a,i>2?arguments[2]:void 0),p_(e,(function(e){s_(h_,n,o(e,r++))}))):p_(e,h_,{that:n}),new this(n))};In({target:"Set",stat:!0,forced:!0},{from:g_});var m_=On,y_=function(){return new this(m_(arguments))};In({target:"Set",stat:!0,forced:!0},{of:y_});var b_=Ow,w_=Mw.add;In({target:"Set",proto:!0,real:!0,forced:!0},{addAll:function(){for(var e=b_(this),t=0,n=arguments.length;t<n;t++)w_(e,arguments[t]);return e}});var A_=Ow,__=Mw.remove;In({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=A_(this),n=!0,r=0,o=arguments.length;r<o;r++)e=__(t,arguments[r]),n=n&&e;return!!n}});var x_=Gt,S_=Ow,C_=Lw;In({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=S_(this),n=x_(e,arguments.length>1?arguments[1]:void 0);return!1!==C_(t,(function(e){if(!n(e,e,t))return!1}),!0)}});var k_=Gn,E_=Xe,T_=q,I_=Wc,O_=vt("iterator"),R_=Object,P_=E,j_=function(e){if(T_(e))return!1;var t=R_(e);return void 0!==t[O_]||"@@iterator"in t||E_(I_,k_(t))},N_=ee,M_=ae("Set"),D_=function(e){return function(e){return N_(e)&&"number"==typeof e.size&&P_(e.has)&&P_(e.keys)}(e)?e:j_(e)?new M_(e):e},B_=P,F_=D_,L_=sA;In({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){return B_(L_,this,F_(e))}});var U_=Gt,V_=Ow,z_=Lw,H_=Mw.Set,W_=Mw.add;In({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=V_(this),n=U_(e,arguments.length>1?arguments[1]:void 0),r=new H_;return z_(t,(function(e){n(e,e,t)&&W_(r,e)})),r}});var q_=Gt,Q_=Ow,K_=Lw;In({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=Q_(this),n=q_(e,arguments.length>1?arguments[1]:void 0),r=K_(t,(function(e){if(n(e,e,t))return{value:e}}),!0);return r&&r.value}});var Z_=P,Y_=D_,G_=AA;In({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){return Z_(G_,this,Y_(e))}});var $_=P,X_=D_,J_=OA;In({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){return $_(J_,this,X_(e))}});var ex=P,tx=D_,nx=DA;In({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){return ex(nx,this,tx(e))}});var rx=P,ox=D_,ix=WA;In({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){return rx(ix,this,ox(e))}});var ax=In,ux=g,cx=Ow,lx=Lw,sx=yi,fx=ux([].join),vx=ux([].push);ax({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=cx(this),n=void 0===e?",":sx(e),r=[];return lx(t,(function(e){vx(r,e)})),fx(r,n)}});var dx=Gt,px=Ow,hx=Lw,gx=Mw.Set,mx=Mw.add;In({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=px(this),n=dx(e,arguments.length>1?arguments[1]:void 0),r=new gx;return hx(t,(function(e){mx(r,n(e,e,t))})),r}});var yx=Re,bx=Ow,wx=Lw,Ax=TypeError;In({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=bx(this),n=arguments.length<2,r=n?void 0:arguments[1];if(yx(e),wx(t,(function(o){n?(n=!1,r=o):r=e(r,o,o,t)})),n)throw new Ax("Reduce of empty set with no initial value");return r}});var _x=Gt,xx=Ow,Sx=Lw;In({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=xx(this),n=_x(e,arguments.length>1?arguments[1]:void 0);return!0===Sx(t,(function(e){if(n(e,e,t))return!0}),!0)}});var Cx=P,kx=D_,Ex=JA;In({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){return Cx(Ex,this,kx(e))}});var Tx=P,Ix=D_,Ox=a_;In({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){return Tx(Ox,this,Ix(e))}});const Rx=n(c_);var Px,jx="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,Nx=o,Mx=f,Dx=E,Bx=jx,Fx=ce,Lx=On,Ux=Bh,Vx=Nx.Function,zx=/MSIE .\./.test(Fx)||Bx&&((Px=Nx.Bun.version.split(".")).length<3||"0"===Px[0]&&(Px[1]<3||"3"===Px[1]&&"0"===Px[2])),Hx=function(e,t){var n=t?2:1;return zx?function(r,o){var i=Ux(arguments.length,1)>n,a=Dx(r)?r:Vx(r),u=i?Lx(arguments,n):[],c=i?function(){Mx(a,this,u)}:a;return t?e(c,o):e(c)}:e},Wx=In,qx=o,Qx=Hx(qx.setInterval,!0);Wx({global:!0,bind:!0,forced:qx.setInterval!==Qx},{setInterval:Qx});var Kx=In,Zx=o,Yx=Hx(Zx.setTimeout,!0);Kx({global:!0,bind:!0,forced:Zx.setTimeout!==Yx},{setTimeout:Yx});const Gx=n(te.setTimeout);var $x=g,Xx=fw,Jx=Qb.getWeakData,eS=Oh,tS=nn,nS=q,rS=ee,oS=Jp,iS=Xe,aS=Ca.set,uS=Ca.getterFor,cS=ja.find,lS=ja.findIndex,sS=$x([].splice),fS=0,vS=function(e){return e.frozen||(e.frozen=new dS)},dS=function(){this.entries=[]},pS=function(e,t){return cS(e.entries,(function(e){return e[0]===t}))};dS.prototype={get:function(e){var t=pS(this,e);if(t)return t[1]},has:function(e){return!!pS(this,e)},set:function(e,t){var n=pS(this,e);n?n[1]=t:this.entries.push([e,t])},delete:function(e){var t=lS(this.entries,(function(t){return t[0]===e}));return~t&&sS(this.entries,t,1),!!~t}};var hS,gS=Ob,mS=o,yS=g,bS=fw,wS=Qb,AS=lw,_S={getConstructor:function(e,t,n,r){var o=e((function(e,o){eS(e,i),aS(e,{type:t,id:fS++,frozen:void 0}),nS(o)||oS(o,e[r],{that:e,AS_ENTRIES:n})})),i=o.prototype,a=uS(t),u=function(e,t,n){var r=a(e),o=Jx(tS(t),!0);return!0===o?vS(r).set(t,n):o[r.id]=n,e};return Xx(i,{delete:function(e){var t=a(this);if(!rS(e))return!1;var n=Jx(e);return!0===n?vS(t).delete(e):n&&iS(n,t.id)&&delete n[t.id]},has:function(e){var t=a(this);if(!rS(e))return!1;var n=Jx(e);return!0===n?vS(t).has(e):n&&iS(n,t.id)}}),Xx(i,n?{get:function(e){var t=a(this);if(rS(e)){var n=Jx(e);return!0===n?vS(t).get(e):n?n[t.id]:void 0}},set:function(e,t){return u(this,e,t)}}:{add:function(e){return u(this,e,!0)}}),o}},xS=ee,SS=Ca.enforce,CS=i,kS=fa,ES=Object,TS=Array.isArray,IS=ES.isExtensible,OS=ES.isFrozen,RS=ES.isSealed,PS=ES.freeze,jS=ES.seal,NS={},MS={},DS=!mS.ActiveXObject&&"ActiveXObject"in mS,BS=function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},FS=AS("WeakMap",BS,_S),LS=FS.prototype,US=yS(LS.set);if(kS)if(DS){hS=_S.getConstructor(BS,"WeakMap",!0),wS.enable();var VS=yS(LS.delete),zS=yS(LS.has),HS=yS(LS.get);bS(LS,{delete:function(e){if(xS(e)&&!IS(e)){var t=SS(this);return t.frozen||(t.frozen=new hS),VS(this,e)||t.frozen.delete(e)}return VS(this,e)},has:function(e){if(xS(e)&&!IS(e)){var t=SS(this);return t.frozen||(t.frozen=new hS),zS(this,e)||t.frozen.has(e)}return zS(this,e)},get:function(e){if(xS(e)&&!IS(e)){var t=SS(this);return t.frozen||(t.frozen=new hS),zS(this,e)?HS(this,e):t.frozen.get(e)}return HS(this,e)},set:function(e,t){if(xS(e)&&!IS(e)){var n=SS(this);n.frozen||(n.frozen=new hS),zS(this,e)?US(this,e,t):n.frozen.set(e,t)}else US(this,e,t);return this}})}else gS&&CS((function(){var e=PS([]);return US(new FS,e,1),!OS(e)}))&&bS(LS,{set:function(e,t){var n;return TS(e)&&(OS(e)?n=NS:RS(e)&&(n=MS)),US(this,e,t),n===NS&&PS(e),n===MS&&jS(e),this}});var WS=te.WeakMap,qS=Ee,QS=TypeError,KS=function(e){if("object"==typeof e&&"has"in e&&"get"in e&&"set"in e)return e;throw new QS(qS(e)+" is not a weakmap")},ZS=Rw,YS={WeakMap:ae("WeakMap"),set:ZS("set",2),get:ZS("get",1),has:ZS("has",1),remove:ZS("delete",1)},GS=KS,$S=YS.get,XS=YS.has,JS=YS.set;In({target:"WeakMap",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var n,r,o=GS(this);return XS(o,e)?(n=$S(o,e),"update"in t&&(n=t.update(n,e,o),JS(o,e,n)),n):(r=t.insert(e,o),JS(o,e,r),r)}}),In({target:"WeakMap",stat:!0,forced:!0},{from:g_}),In({target:"WeakMap",stat:!0,forced:!0},{of:y_});var eC=KS,tC=YS.remove;In({target:"WeakMap",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=eC(this),n=!0,r=0,o=arguments.length;r<o;r++)e=tC(t,arguments[r]),n=n&&e;return!!n}});var nC=P,rC=Re,oC=E,iC=nn,aC=TypeError,uC=function(e,t){var n,r=iC(this),o=rC(r.get),i=rC(r.has),a=rC(r.set),u=arguments.length>2?arguments[2]:void 0;if(!oC(t)&&!oC(u))throw new aC("At least one callback required");return nC(i,r,e)?(n=nC(o,r,e),oC(t)&&(n=t(n),nC(a,r,e,n))):oC(u)&&(n=u(),nC(a,r,e,n)),n};In({target:"WeakMap",proto:!0,real:!0,forced:!0},{upsert:uC});const cC=n(WS);var lC=ja.filter;In({target:"Array",proto:!0,forced:!ti("filter")},{filter:function(e){return lC(this,e,arguments.length>1?arguments[1]:void 0)}});var sC=Df("Array","filter"),fC=ue,vC=sC,dC=Array.prototype;const pC=n((function(e){var t=e.filter;return e===dC||fC(dC,e)&&t===dC.filter?vC:t}));var hC="\t\n\v\f\r                　\u2028\u2029\ufeff",gC=Z,mC=yi,yC=hC,bC=g("".replace),wC=RegExp("^["+yC+"]+"),AC=RegExp("(^|[^"+yC+"])["+yC+"]+$"),_C=function(e){return function(t){var n=mC(gC(t));return 1&e&&(n=bC(n,wC,"")),2&e&&(n=bC(n,AC,"$1")),n}},xC={start:_C(1),end:_C(2),trim:_C(3)},SC=Gc.PROPER,CC=i,kC=hC,EC=xC.trim;In({target:"String",proto:!0,forced:function(e){return CC((function(){return!!kC[e]()||"​᠎"!=="​᠎"[e]()||SC&&kC[e].name!==e}))}("trim")},{trim:function(){return EC(this)}});var TC=Df("String","trim"),IC=ue,OC=TC,RC=String.prototype;const PC=n((function(e){var t=e.trim;return"string"==typeof e||e===RC||IC(RC,e)&&t===RC.trim?OC:t}));var jC=Ee,NC=TypeError,MC=function(e,t){if(!delete e[t])throw new NC("Cannot delete property "+jC(t)+" of "+jC(e))},DC=In,BC=Ye,FC=Tr,LC=Sr,UC=jr,VC=Gf,zC=Uo,HC=$o,WC=Wo,qC=MC,QC=ti("splice"),KC=Math.max,ZC=Math.min;DC({target:"Array",proto:!0,forced:!QC},{splice:function(e,t){var n,r,o,i,a,u,c=BC(this),l=UC(c),s=FC(e,l),f=arguments.length;for(0===f?n=r=0:1===f?(n=0,r=l-s):(n=f-2,r=ZC(KC(LC(t),0),l-s)),zC(l+n-r),o=HC(c,r),i=0;i<r;i++)(a=s+i)in c&&WC(o,i,c[a]);if(o.length=r,n<r){for(i=s;i<l-r;i++)u=i+n,(a=i+r)in c?c[u]=c[a]:qC(c,u);for(i=l;i>l-r+n;i--)qC(c,i-1)}else if(n>r)for(i=l-r;i>s;i--)u=i+n-1,(a=i+r-1)in c?c[u]=c[a]:qC(c,u);for(i=0;i<n;i++)c[i+s]=arguments[i+2];return VC(c,l-r+n),o}});var YC=Df("Array","splice"),GC=ue,$C=YC,XC=Array.prototype;const JC=n((function(e){var t=e.splice;return e===XC||GC(XC,e)&&t===XC.splice?$C:t}));function ek(e,t,n){return(t=kf(t))in e?Af(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var tk=te,nk=f;tk.JSON||(tk.JSON={stringify:JSON.stringify});var rk=function(e,t,n){return nk(tk.JSON.stringify,null,arguments)};const ok=n(rk);const ik=n(Vf);var ak=In,uk=Fr.indexOf,ck=wd,lk=x([].indexOf),sk=!!lk&&1/lk([1],1,-0)<0;ak({target:"Array",proto:!0,forced:sk||!ck("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return sk?lk(this,e,t)||0:uk(this,e,t)}});var fk=Df("Array","indexOf"),vk=ue,dk=fk,pk=Array.prototype;const hk=n((function(e){var t=e.indexOf;return e===pk||vk(pk,e)&&t===pk.indexOf?dk:t}));lw("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),kw);var gk=Rw,mk=ae("Map"),yk={Map:mk,set:gk("set",2),get:gk("get",1),has:gk("has",1),remove:gk("delete",1),proto:mk.prototype},bk=In,wk=Re,Ak=Z,_k=Jp,xk=yk.Map,Sk=yk.has,Ck=yk.get,kk=yk.set,Ek=g([].push);bk({target:"Map",stat:!0,forced:true},{groupBy:function(e,t){Ak(e),wk(t);var n=new xk,r=0;return _k(e,(function(e){var o=t(e,r++);Sk(n,o)?Ek(Ck(n,o),e):kk(n,o,[e])})),n}});var Tk=te.Map;In({target:"Map",stat:!0,forced:!0},{from:g_}),In({target:"Map",stat:!0,forced:!0},{of:y_});var Ik=Ee,Ok=TypeError,Rk=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"get"in e&&"set"in e&&"delete"in e&&"entries"in e)return e;throw new Ok(Ik(e)+" is not a map")},Pk=Rk,jk=yk.remove;In({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=Pk(this),n=!0,r=0,o=arguments.length;r<o;r++)e=jk(t,arguments[r]),n=n&&e;return!!n}});var Nk=Rk,Mk=yk.get,Dk=yk.has,Bk=yk.set;In({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var n,r,o=Nk(this);return Dk(o,e)?(n=Mk(o,e),"update"in t&&(n=t.update(n,e,o),Bk(o,e,n)),n):(r=t.insert(e,o),Bk(o,e,r),r)}});var Fk=Bw,Lk=function(e,t,n){return n?Fk(e.entries(),(function(e){return t(e[1],e[0])}),!0):e.forEach(t)},Uk=Gt,Vk=Rk,zk=Lk;In({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=Vk(this),n=Uk(e,arguments.length>1?arguments[1]:void 0);return!1!==zk(t,(function(e,r){if(!n(e,r,t))return!1}),!0)}});var Hk=Gt,Wk=Rk,qk=Lk,Qk=yk.Map,Kk=yk.set;In({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=Wk(this),n=Hk(e,arguments.length>1?arguments[1]:void 0),r=new Qk;return qk(t,(function(e,o){n(e,o,t)&&Kk(r,o,e)})),r}});var Zk=Gt,Yk=Rk,Gk=Lk;In({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=Yk(this),n=Zk(e,arguments.length>1?arguments[1]:void 0),r=Gk(t,(function(e,r){if(n(e,r,t))return{value:e}}),!0);return r&&r.value}});var $k=Gt,Xk=Rk,Jk=Lk;In({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=Xk(this),n=$k(e,arguments.length>1?arguments[1]:void 0),r=Jk(t,(function(e,r){if(n(e,r,t))return{key:r}}),!0);return r&&r.key}});var eE=function(e,t){return e===t||e!=e&&t!=t},tE=Rk,nE=Lk;In({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===nE(tE(this),(function(t){if(eE(t,e))return!0}),!0)}});var rE=P,oE=Jp,iE=E,aE=Re,uE=yk.Map;In({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var n=new(iE(this)?this:uE);aE(t);var r=aE(n.set);return oE(e,(function(e){rE(r,n,t(e),e)})),n}});var cE=Rk,lE=Lk;In({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=lE(cE(this),(function(t,n){if(t===e)return{key:n}}),!0);return t&&t.key}});var sE=Gt,fE=Rk,vE=Lk,dE=yk.Map,pE=yk.set;In({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=fE(this),n=sE(e,arguments.length>1?arguments[1]:void 0),r=new dE;return vE(t,(function(e,o){pE(r,n(e,o,t),e)})),r}});var hE=Gt,gE=Rk,mE=Lk,yE=yk.Map,bE=yk.set;In({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=gE(this),n=hE(e,arguments.length>1?arguments[1]:void 0),r=new yE;return mE(t,(function(e,o){bE(r,o,n(e,o,t))})),r}});var wE=Rk,AE=Jp,_E=yk.set;In({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=wE(this),n=arguments.length,r=0;r<n;)AE(arguments[r++],(function(e,n){_E(t,e,n)}),{AS_ENTRIES:!0});return t}});var xE=Re,SE=Rk,CE=Lk,kE=TypeError;In({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=SE(this),n=arguments.length<2,r=n?void 0:arguments[1];if(xE(e),CE(t,(function(o,i){n?(n=!1,r=o):r=e(r,o,i,t)})),n)throw new kE("Reduce of empty map with no initial value");return r}});var EE=Gt,TE=Rk,IE=Lk;In({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=TE(this),n=EE(e,arguments.length>1?arguments[1]:void 0);return!0===IE(t,(function(e,r){if(n(e,r,t))return!0}),!0)}});var OE=Re,RE=Rk,PE=TypeError,jE=yk.get,NE=yk.has,ME=yk.set;In({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var n=RE(this),r=arguments.length;OE(t);var o=NE(n,e);if(!o&&r<3)throw new PE("Updating absent value");var i=o?jE(n,e):OE(r>2?arguments[2]:void 0)(e,n);return ME(n,e,t(i,e,n)),n}}),In({target:"Map",proto:!0,real:!0,forced:!0},{upsert:uC}),In({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:uC});const DE=n(Tk);var BE=Ti,FE=Math.floor,LE=function(e,t){var n=e.length,r=FE(n/2);return n<8?UE(e,t):VE(e,LE(BE(e,0,r),t),LE(BE(e,r),t),t)},UE=function(e,t){for(var n,r,o=e.length,i=1;i<o;){for(r=i,n=e[i];r&&t(e[r-1],n)>0;)e[r]=e[--r];r!==i++&&(e[r]=n)}return e},VE=function(e,t,n,r){for(var o=t.length,i=n.length,a=0,u=0;a<o||u<i;)e[a+u]=a<o&&u<i?r(t[a],n[u])<=0?t[a++]:n[u++]:a<o?t[a++]:n[u++];return e},zE=LE,HE=ce.match(/firefox\/(\d+)/i),WE=!!HE&&+HE[1],qE=/MSIE|Trident/.test(ce),QE=ce.match(/AppleWebKit\/(\d+)\./),KE=!!QE&&+QE[1],ZE=In,YE=g,GE=Re,$E=Ye,XE=jr,JE=MC,eT=yi,tT=i,nT=zE,rT=wd,oT=WE,iT=qE,aT=he,uT=KE,cT=[],lT=YE(cT.sort),sT=YE(cT.push),fT=tT((function(){cT.sort(void 0)})),vT=tT((function(){cT.sort(null)})),dT=rT("sort"),pT=!tT((function(){if(aT)return aT<70;if(!(oT&&oT>3)){if(iT)return!0;if(uT)return uT<603;var e,t,n,r,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)cT.push({k:t+r,v:n})}for(cT.sort((function(e,t){return t.v-e.v})),r=0;r<cT.length;r++)t=cT[r].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}}));ZE({target:"Array",proto:!0,forced:fT||!vT||!dT||!pT},{sort:function(e){void 0!==e&&GE(e);var t=$E(this);if(pT)return void 0===e?lT(t):lT(t,e);var n,r,o=[],i=XE(t);for(r=0;r<i;r++)r in t&&sT(o,t[r]);for(nT(o,function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:eT(t)>eT(n)?1:-1}}(e)),n=XE(o),r=0;r<n;)t[r]=o[r++];for(;r<i;)JE(t,r++);return t}});var hT=Df("Array","sort"),gT=ue,mT=hT,yT=Array.prototype;const bT=n((function(e){var t=e.sort;return e===yT||gT(yT,e)&&t===yT.sort?mT:t}));var wT=Ye,AT=Tr,_T=jr,xT=function(e){for(var t=wT(this),n=_T(t),r=arguments.length,o=AT(r>1?arguments[1]:void 0,n),i=r>2?arguments[2]:void 0,a=void 0===i?n:AT(i,n);a>o;)t[o++]=e;return t};In({target:"Array",proto:!0},{fill:xT});var ST=Df("Array","fill"),CT=ue,kT=ST,ET=Array.prototype;const TT=n((function(e){var t=e.fill;return e===ET||CT(ET,e)&&t===ET.fill?kT:t}));var IT=o,OT=i,RT=g,PT=yi,jT=xC.trim,NT=hC,MT=IT.parseInt,DT=IT.Symbol,BT=DT&&DT.iterator,FT=/^[+-]?0x/i,LT=RT(FT.exec),UT=8!==MT(NT+"08")||22!==MT(NT+"0x16")||BT&&!OT((function(){MT(Object(BT))}))?function(e,t){var n=jT(PT(e));return MT(n,t>>>0||(LT(FT,n)?16:10))}:MT;In({global:!0,forced:parseInt!==UT},{parseInt:UT});const VT=n(te.parseInt);var zT=Df("Array","values"),HT=Gn,WT=Xe,qT=ue,QT=zT,KT=Array.prototype,ZT={DOMTokenList:!0,NodeList:!0};const YT=n((function(e){var t=e.values;return e===KT||qT(KT,e)&&t===KT.values||WT(ZT,HT(e))?QT:t}));const GT=n(te.Symbol.for);var $T=I,XT=nn,JT=_t,eI=$t;In({target:"Reflect",stat:!0,forced:i((function(){Reflect.defineProperty(eI.f({},1,{value:1}),1,{value:2})})),sham:!$T},{defineProperty:function(e,t,n){XT(e);var r=JT(t);XT(n);try{return eI.f(e,r,n),!0}catch(kK){return!1}}});const tI=n(te.Reflect.defineProperty);In({target:"Reflect",stat:!0},{ownKeys:_p});const nI=n(te.Reflect.ownKeys);var rI=Re,oI=Ye,iI=W,aI=jr,uI=TypeError,cI=function(e){return function(t,n,r,o){var i=oI(t),a=iI(i),u=aI(i);rI(n);var c=e?u-1:0,l=e?-1:1;if(r<2)for(;;){if(c in a){o=a[c],c+=l;break}if(c+=l,e?c<0:u<=c)throw new uI("Reduce of empty array with no initial value")}for(;e?c>=0:u>c;c+=l)c in a&&(o=n(o,a[c],c,i));return o}},lI={left:cI(!1),right:cI(!0)}.left;In({target:"Array",proto:!0,forced:!_h&&he>79&&he<83||!wd("reduce")},{reduce:function(e){var t=arguments.length;return lI(this,e,t,t>1?arguments[1]:void 0)}});var sI=Df("Array","reduce"),fI=ue,vI=sI,dI=Array.prototype;const pI=n((function(e){var t=e.reduce;return e===dI||fI(dI,e)&&t===dI.reduce?vI:t}));var hI=In,gI=ja.findIndex,mI="findIndex",yI=!0;mI in[]&&Array(1)[mI]((function(){yI=!1})),hI({target:"Array",proto:!0,forced:yI},{findIndex:function(e){return gI(this,e,arguments.length>1?arguments[1]:void 0)}});var bI=Df("Array","findIndex"),wI=ue,AI=bI,_I=Array.prototype;const xI=n((function(e){var t=e.findIndex;return e===_I||wI(_I,e)&&t===_I.findIndex?AI:t}));var SI=In,CI=x;T.f;var kI=Rr,EI=yi,TI=Bd,II=Z,OI=Ld,RI=CI("".endsWith),PI=CI("".slice),jI=Math.min;SI({target:"String",proto:!0,forced:!OI("endsWith")},{endsWith:function(e){var t=EI(II(this));TI(e);var n=arguments.length>1?arguments[1]:void 0,r=t.length,o=void 0===n?r:jI(kI(n),r),i=EI(e);return RI?RI(t,i,o):PI(t,o-i.length,o)===i}});var NI=Df("String","endsWith"),MI=ue,DI=NI,BI=String.prototype;const FI=n((function(e){var t=e.endsWith;return"string"==typeof e||e===BI||MI(BI,e)&&t===BI.endsWith?DI:t}));var LI=In,UI=i,VI=_i.f;LI({target:"Object",stat:!0,forced:UI((function(){return!Object.getOwnPropertyNames(1)}))},{getOwnPropertyNames:VI});var zI=te.Object;const HI=n((function(e){return zI.getOwnPropertyNames(e)}));var WI=Xe,qI=function(e){return void 0!==e&&(WI(e,"value")||WI(e,"writable"))},QI=P,KI=ee,ZI=nn,YI=qI,GI=T,$I=il;In({target:"Reflect",stat:!0},{get:function e(t,n){var r,o,i=arguments.length<3?t:arguments[2];return ZI(t)===i?t[n]:(r=GI.f(t,n))?YI(r)?r.value:void 0===r.get?void 0:QI(r.get,i):KI(o=$I(t))?e(o,n,i):void 0}});const XI=n(te.Reflect.get);var JI=P,eO=nn,tO=ee,nO=qI,rO=$t,oO=T,iO=il,aO=L;In({target:"Reflect",stat:!0,forced:i((function(){var e=function(){},t=rO.f(new e,"a",{configurable:!0});return!1!==Reflect.set(e.prototype,"a",1,t)}))},{set:function e(t,n,r){var o,i,a,u=arguments.length<4?t:arguments[3],c=oO.f(eO(t),n);if(!c){if(tO(i=iO(t)))return e(i,n,r,u);c=aO(0)}if(nO(c)){if(!1===c.writable||!tO(u))return!1;if(o=oO.f(u,n)){if(o.get||o.set||!1===o.writable)return!1;o.value=r,rO.f(u,n,o)}else rO.f(u,n,aO(0,r))}else{if(void 0===(a=c.set))return!1;JI(a,u,r)}return!0}});const uO=n(te.Reflect.set);var cO=In,lO=nn,sO=T.f;cO({target:"Reflect",stat:!0},{deleteProperty:function(e,t){var n=sO(lO(e),t);return!(n&&!n.configurable)&&delete e[t]}});const fO=n(te.Reflect.deleteProperty);In({target:"Reflect",stat:!0},{has:function(e,t){return t in e}});const vO=n(te.Reflect.has);var dO=nn,pO=il;In({target:"Reflect",stat:!0,sham:!$c},{getPrototypeOf:function(e){return pO(dO(e))}});const hO=n(te.Reflect.getPrototypeOf);const gO=n(_f);var mO=Ib;In({target:"Object",stat:!0,forced:Object.isExtensible!==mO},{isExtensible:mO});const yO=n(te.Object.isExtensible);var bO=I,wO=g,AO=P,_O=i,xO=Yr,SO=Ni,CO=j,kO=Ye,EO=W,TO=Object.assign,IO=Object.defineProperty,OO=wO([].concat),RO=!TO||_O((function(){if(bO&&1!==TO({b:1},TO(IO({},"a",{enumerable:!0,get:function(){IO(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol("assign detection"),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach((function(e){t[e]=e})),7!==TO({},e)[n]||xO(TO({},t)).join("")!==r}))?function(e,t){for(var n=kO(e),r=arguments.length,o=1,i=SO.f,a=CO.f;r>o;)for(var u,c=EO(arguments[o++]),l=i?OO(xO(c),i(c)):xO(c),s=l.length,f=0;s>f;)u=l[f++],bO&&!AO(a,c,u)||(n[u]=c[u]);return n}:TO,PO=RO;In({target:"Object",stat:!0,arity:2,forced:Object.assign!==PO},{assign:PO});const jO=n(te.Object.assign);In({target:"Object",stat:!0},{is:Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}});const NO=n(te.Object.is);var MO=o,DO=i,BO=yi,FO=xC.trim,LO=hC,UO=g("".charAt),VO=MO.parseFloat,zO=MO.Symbol,HO=zO&&zO.iterator,WO=1/VO(LO+"-0")!=-1/0||HO&&!DO((function(){VO(Object(HO))}))?function(e){var t=FO(BO(e)),n=VO(t);return 0===n&&"-"===UO(t,0)?-0:n}:VO;In({global:!0,forced:parseFloat!==WO},{parseFloat:WO});const qO=n(te.parseFloat);var QO=o;In({global:!0,forced:QO.globalThis!==QO},{globalThis:QO});const KO=n(o);var ZO=Sr,YO=yi,GO=Z,$O=RangeError,XO=Df("Array","entries"),JO=Gn,eR=Xe,tR=ue,nR=XO,rR=Array.prototype,oR={DOMTokenList:!0,NodeList:!0};const iR=n((function(e){var t=e.entries;return e===rR||tR(rR,e)&&t===rR.entries||eR(oR,JO(e))?nR:t}));var aR;function uR(e,t){for(var n=sb(null),r=e.split(","),o=0;o<r.length;o++)n[r[o]]=!0;return t?function(e){return!!n[e.toLowerCase()]}:function(e){return!!n[e]}}var cR,lR={},sR=[],fR=function(){},vR=function(){return!1},dR=/^on[^a-z]/,pR=function(e){return dR.test(e)},hR=function(e){return ep(e).call(e,"onUpdate:")},gR=jO,mR=function(e,t){var n=hk(e).call(e,t);n>-1&&JC(e).call(e,n,1)},yR=Object.prototype.hasOwnProperty,bR=function(e,t){return yR.call(e,t)},wR=df,AR=function(e){return"[object Map]"===IR(e)},_R=function(e){return"[object Set]"===IR(e)},xR=function(e){return"function"==typeof e},SR=function(e){return"string"==typeof e},CR=function(e){return"symbol"===Sf(e)},kR=function(e){return null!==e&&"object"===Sf(e)},ER=function(e){return kR(e)&&xR(e.then)&&xR(e.catch)},TR=Object.prototype.toString,IR=function(e){return TR.call(e)},OR=function(e){var t;return tp(t=IR(e)).call(t,8,-1)},RR=function(e){return"[object Object]"===IR(e)},PR=function(e){return SR(e)&&"NaN"!==e&&"-"!==e[0]&&""+VT(e,10)===e},jR=uR(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),NR=function(e){var t=sb(null);return function(n){return t[n]||(t[n]=e(n))}},MR=/-(\w)/g,DR=NR((function(e){return e.replace(MR,(function(e,t){return t?t.toUpperCase():""}))})),BR=/\B([A-Z])/g,FR=NR((function(e){return e.replace(BR,"-$1").toLowerCase()})),LR=NR((function(e){return e.charAt(0).toUpperCase()+tp(e).call(e,1)})),UR=NR((function(e){return e?"on".concat(LR(e)):""})),VR=function(e,t){return!NO(e,t)},zR=function(e,t){for(var n=0;n<e.length;n++)e[n](t)},HR=function(e,t,n){pb(e,t,{configurable:!0,enumerable:!1,value:n})},WR=function(e){var t=qO(e);return isNaN(t)?e:t},qR=function(){return cR||(cR=void 0!==KO?KO:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})};function QR(e){if(wR(e)){for(var t={},n=0;n<e.length;n++){var r=e[n],o=SR(r)?GR(r):QR(r);if(o)for(var i in o)t[i]=o[i]}return t}return SR(e)||kR(e)?e:void 0}ek(ek(ek(ek(ek(ek(ek(ek(ek(ek(aR={},1,"TEXT"),2,"CLASS"),4,"STYLE"),8,"PROPS"),16,"FULL_PROPS"),32,"HYDRATE_EVENTS"),64,"STABLE_FRAGMENT"),128,"KEYED_FRAGMENT"),256,"UNKEYED_FRAGMENT"),512,"NEED_PATCH"),ek(ek(ek(ek(aR,1024,"DYNAMIC_SLOTS"),2048,"DEV_ROOT_FRAGMENT"),-1,"HOISTED"),-2,"BAIL"),ek(ek(ek({},1,"STABLE"),2,"DYNAMIC"),3,"FORWARDED");var KR=/;(?![^(]*\))/g,ZR=/:([^]+)/,YR=/\/\*[^]*?\*\//g;function GR(e){var t,n={};return Rd(t=e.replace(YR,"").split(KR)).call(t,(function(e){if(e){var t,r,o=e.split(ZR);o.length>1&&(n[PC(t=o[0]).call(t)]=PC(r=o[1]).call(r))}})),n}function $R(e){var t="";if(SR(e))t=e;else if(wR(e))for(var n=0;n<e.length;n++){var r=$R(e[n]);r&&(t+=r+" ")}else if(kR(e))for(var o in e)e[o]&&(t+=o+" ");return PC(t).call(t)}var XR=uR("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function JR(e){return!!e||""===e}var eP,tP,nP,rP,oP=function(e){return SR(e)?e:null==e?"":wR(e)||kR(e)&&(e.toString===TR||!xR(e.toString))?ok(e,iP,2):String(e)},iP=function e(t,n){return n&&n.__v_isRef?e(t,n.value):AR(n)?ek({},"Map(".concat(n.size,")"),pI(r=dd(iR(n).call(n))).call(r,(function(e,t){var n=vd(t,2),r=n[0],o=n[1];return e["".concat(r," =>")]=o,e}),{})):_R(n)?ek({},"Set(".concat(n.size,")"),dd(YT(n).call(n))):!kR(n)||wR(n)||RR(n)?n:String(n);var r};function aP(e,t){var n=void 0!==Bs&&sf(e)||e["@@iterator"];if(!n){if(df(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return uP(e,t);var r=tp(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return hb(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uP(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw i}}}}function uP(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var cP=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];If(this,e),this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=rP,!t&&rP&&(this.index=(rP.scopes||(rP.scopes=[])).push(this)-1)}return Tf(e,[{key:"active",get:function(){return this._active}},{key:"run",value:function(e){if(this._active){var t=rP;try{return rP=this,e()}finally{rP=t}}}},{key:"on",value:function(){rP=this}},{key:"off",value:function(){rP=this.parent}},{key:"stop",value:function(e){if(this._active){var t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}]),e}();function lP(){return rP}var sP,fP=function(e){var t=new Rx(e);return t.w=0,t.n=0,t},vP=function(e){return(e.w&gP)>0},dP=function(e){return(e.n&gP)>0},pP=new cC,hP=0,gP=1,mP=30,yP=Bs(""),bP=Bs(""),wP=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2?arguments[2]:void 0;If(this,e),this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rP;t&&t.active&&t.effects.push(e)}(this,r)}return Tf(e,[{key:"run",value:function(){if(!this.active)return this.fn();for(var e=sP,t=_P;e;){if(e===this)return;e=e.parent}try{return this.parent=sP,sP=this,_P=!0,gP=1<<++hP,hP<=mP?function(e){var t=e.deps;if(t.length)for(var n=0;n<t.length;n++)t[n].w|=gP}(this):AP(this),this.fn()}finally{hP<=mP&&function(e){var t=e.deps;if(t.length){for(var n=0,r=0;r<t.length;r++){var o=t[r];vP(o)&&!dP(o)?o.delete(e):t[n++]=o,o.w&=~gP,o.n&=~gP}t.length=n}}(this),gP=1<<--hP,sP=this.parent,_P=t,this.parent=void 0,this.deferStop&&this.stop()}}},{key:"stop",value:function(){sP===this?this.deferStop=!0:this.active&&(AP(this),this.onStop&&this.onStop(),this.active=!1)}}]),e}();function AP(e){var t=e.deps;if(t.length){for(var n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var _P=!0,xP=[];function SP(){xP.push(_P),_P=!1}function CP(){var e=xP.pop();_P=void 0===e||e}function kP(e,t,n){if(_P&&sP){var r=pP.get(e);r||pP.set(e,r=new DE);var o=r.get(n);o||r.set(n,o=fP()),EP(o)}}function EP(e,t){var n=!1;hP<=mP?dP(e)||(e.n|=gP,n=!vP(e)):n=!e.has(sP),n&&(e.add(sP),sP.deps.push(e))}function TP(e,t,n,r,o,i){var a=pP.get(e);if(a){var u=[];if("clear"===t)u=dd(YT(a).call(a));else if("length"===n&&wR(e)){var c=Number(r);Rd(a).call(a,(function(e,t){("length"===t||t>=c)&&u.push(e)}))}else switch(void 0!==n&&u.push(a.get(n)),t){case"add":wR(e)?PR(n)&&u.push(a.get("length")):(u.push(a.get(yP)),AR(e)&&u.push(a.get(bP)));break;case"delete":wR(e)||(u.push(a.get(yP)),AR(e)&&u.push(a.get(bP)));break;case"set":AR(e)&&u.push(a.get(yP))}if(1===u.length)u[0]&&IP(u[0]);else{var l,s=[],f=aP(u);try{for(f.s();!(l=f.n()).done;){var v=l.value;v&&s.push.apply(s,dd(v))}}catch(d){f.e(d)}finally{f.f()}IP(fP(s))}}}function IP(e,t){var n,r=wR(e)?e:dd(e),o=aP(r);try{for(o.s();!(n=o.n()).done;){var i=n.value;i.computed&&OP(i,t)}}catch(l){o.e(l)}finally{o.f()}var a,u=aP(r);try{for(u.s();!(a=u.n()).done;){var c=a.value;c.computed||OP(c,t)}}catch(l){u.e(l)}finally{u.f()}}function OP(e,t){(e!==sP||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}var RP=uR("__proto__,__v_isRef,__isVue"),PP=new Rx(pC(eP=lb(tP=pC(nP=HI(Bs)).call(nP,(function(e){return"arguments"!==e&&"caller"!==e}))).call(tP,(function(e){return Bs[e]}))).call(eP,CR)),jP=LP(),NP=LP(!1,!0),MP=LP(!0),DP=BP();function BP(){var e,t,n={};return Rd(e=["includes","indexOf","lastIndexOf"]).call(e,(function(e){n[e]=function(){for(var t=xj(this),n=0,r=this.length;n<r;n++)kP(t,0,n+"");for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];var u=t[e].apply(t,i);return-1===u||!1===u?t[e].apply(t,dd(lb(i).call(i,xj))):u}})),Rd(t=["push","pop","shift","unshift","splice"]).call(t,(function(e){n[e]=function(){SP();for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=xj(this)[e].apply(this,n);return CP(),o}})),n}function FP(e){var t=xj(this);return kP(t,0,e),t.hasOwnProperty(e)}function LP(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,r,o){if("__v_isReactive"===r)return!e;if("__v_isReadonly"===r)return e;if("__v_isShallow"===r)return t;if("__v_raw"===r&&o===(e?t?hj:pj:t?dj:vj).get(n))return n;var i=wR(n);if(!e){if(i&&bR(DP,r))return XI(DP,r,o);if("hasOwnProperty"===r)return FP}var a=XI(n,r,o);return(CR(r)?PP.has(r):RP(r))?a:(e||kP(n,0,r),t?a:Ij(a)?i&&PR(r)?a:a.value:kR(a)?e?mj(a):gj(a):a)}}function UP(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n,r,o){var i=t[n];if(wj(i)&&Ij(i)&&!Ij(r))return!1;if(!e&&(Aj(r)||wj(r)||(i=xj(i),r=xj(r)),!wR(t)&&Ij(i)&&!Ij(r)))return i.value=r,!0;var a=wR(t)&&PR(n)?Number(n)<t.length:bR(t,n),u=uO(t,n,r,o);return t===xj(o)&&(a?VR(r,i)&&TP(t,"set",n,r):TP(t,"add",n,r)),u}}var VP={get:jP,set:UP(),deleteProperty:function(e,t){var n=bR(e,t);e[t];var r=fO(e,t);return r&&n&&TP(e,"delete",t,void 0),r},has:function(e,t){var n=vO(e,t);return CR(t)&&PP.has(t)||kP(e,0,t),n},ownKeys:function(e){return kP(e,0,wR(e)?"length":yP),nI(e)}},zP={get:MP,set:function(e,t){return!0},deleteProperty:function(e,t){return!0}},HP=gR({},VP,{get:NP,set:UP(!0)}),WP=function(e){return e},qP=function(e){return hO(e)};function QP(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=xj(e=e.__v_raw),i=xj(t);n||(t!==i&&kP(o,0,t),kP(o,0,i));var a=qP(o).has,u=r?WP:n?kj:Cj;return a.call(o,t)?u(e.get(t)):a.call(o,i)?u(e.get(i)):void(e!==o&&e.get(t))}function KP(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.__v_raw,r=xj(n),o=xj(e);return t||(e!==o&&kP(r,0,e),kP(r,0,o)),e===o?n.has(e):n.has(e)||n.has(o)}function ZP(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e=e.__v_raw,!t&&kP(xj(e),0,yP),XI(e,"size",e)}function YP(e){e=xj(e);var t=xj(this);return qP(t).has.call(t,e)||(t.add(e),TP(t,"add",e,e)),this}function GP(e,t){t=xj(t);var n=xj(this),r=qP(n),o=r.has,i=r.get,a=o.call(n,e);a||(e=xj(e),a=o.call(n,e));var u=i.call(n,e);return n.set(e,t),a?VR(t,u)&&TP(n,"set",e,t):TP(n,"add",e,t),this}function $P(e){var t=xj(this),n=qP(t),r=n.has,o=n.get,i=r.call(t,e);i||(e=xj(e),i=r.call(t,e)),o&&o.call(t,e);var a=t.delete(e);return i&&TP(t,"delete",e,void 0),a}function XP(){var e=xj(this),t=0!==e.size,n=e.clear();return t&&TP(e,"clear",void 0,void 0),n}function JP(e,t){return function(n,r){var o=this,i=o.__v_raw,a=xj(i),u=t?WP:e?kj:Cj;return!e&&kP(a,0,yP),Rd(i).call(i,(function(e,t){return n.call(r,u(e),u(t),o)}))}}function ej(e,t,n){return function(){var r=this.__v_raw,o=xj(r),i=AR(o),a="entries"===e||e===gO&&i,u="keys"===e&&i,c=r[e].apply(r,arguments),l=n?WP:t?kj:Cj;return!t&&kP(o,0,u?bP:yP),ek({next:function(){var e=c.next(),t=e.value,n=e.done;return n?{value:t,done:n}:{value:a?[l(t[0]),l(t[1])]:l(t),done:n}}},gO,(function(){return this}))}}function tj(e){return function(){return"delete"!==e&&this}}function nj(){var e={get:function(e){return QP(this,e)},get size(){return ZP(this)},has:KP,add:YP,set:GP,delete:$P,clear:XP,forEach:JP(!1,!1)},t={get:function(e){return QP(this,e,!1,!0)},get size(){return ZP(this)},has:KP,add:YP,set:GP,delete:$P,clear:XP,forEach:JP(!1,!0)},n={get:function(e){return QP(this,e,!0)},get size(){return ZP(this,!0)},has:function(e){return KP.call(this,e,!0)},add:tj("add"),set:tj("set"),delete:tj("delete"),clear:tj("clear"),forEach:JP(!0,!1)},r={get:function(e){return QP(this,e,!0,!0)},get size(){return ZP(this,!0)},has:function(e){return KP.call(this,e,!0)},add:tj("add"),set:tj("set"),delete:tj("delete"),clear:tj("clear"),forEach:JP(!0,!0)},o=["keys","values","entries",gO];return Rd(o).call(o,(function(o){e[o]=ej(o,!1,!1),n[o]=ej(o,!0,!1),t[o]=ej(o,!1,!0),r[o]=ej(o,!0,!0)})),[e,n,t,r]}var rj=vd(nj(),4),oj=rj[0],ij=rj[1],aj=rj[2],uj=rj[3];function cj(e,t){var n=t?e?uj:aj:e?ij:oj;return function(t,r,o){return"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:XI(bR(n,r)&&r in t?n:t,r,o)}}var lj={get:cj(!1,!1)},sj={get:cj(!1,!0)},fj={get:cj(!0,!1)},vj=new cC,dj=new cC,pj=new cC,hj=new cC;function gj(e){return wj(e)?e:yj(e,!1,VP,lj,vj)}function mj(e){return yj(e,!0,zP,fj,pj)}function yj(e,t,n,r,o){if(!kR(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;var i=o.get(e);if(i)return i;var a,u=(a=e).__v_skip||!yO(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(OR(a));if(0===u)return e;var c=new Proxy(e,2===u?r:n);return o.set(e,c),c}function bj(e){return wj(e)?bj(e.__v_raw):!(!e||!e.__v_isReactive)}function wj(e){return!(!e||!e.__v_isReadonly)}function Aj(e){return!(!e||!e.__v_isShallow)}function _j(e){return bj(e)||wj(e)}function xj(e){var t=e&&e.__v_raw;return t?xj(t):e}function Sj(e){return HR(e,"__v_skip",!0),e}var Cj=function(e){return kR(e)?gj(e):e},kj=function(e){return kR(e)?mj(e):e};function Ej(e){_P&&sP&&EP((e=xj(e)).dep||(e.dep=fP()))}function Tj(e,t){var n=(e=xj(e)).dep;n&&IP(n)}function Ij(e){return!(!e||!0!==e.__v_isRef)}function Oj(e){return function(e,t){if(Ij(e))return e;return new Rj(e,t)}(e,!1)}var Rj=function(){function e(t,n){If(this,e),this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:xj(t),this._value=n?t:Cj(t)}return Tf(e,[{key:"value",get:function(){return Ej(this),this._value},set:function(e){var t=this.__v_isShallow||Aj(e)||wj(e);e=t?e:xj(e),VR(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Cj(e),Tj(this))}}]),e}();function Pj(e){return Ij(e)?e.value:e}var jj={get:function(e,t,n){return Pj(XI(e,t,n))},set:function(e,t,n,r){var o=e[t];return Ij(o)&&!Ij(n)?(o.value=n,!0):uO(e,t,n,r)}};function Nj(e){return bj(e)?e:new Proxy(e,jj)}var Mj=function(){function e(t){var n=this;If(this,e),this.dep=void 0,this.__v_isRef=!0;var r=t((function(){return Ej(n)}),(function(){return Tj(n)})),o=r.get,i=r.set;this._get=o,this._set=i}return Tf(e,[{key:"value",get:function(){return this._get()},set:function(e){this._set(e)}}]),e}();function Dj(e){var t=wR(e)?new Array(e.length):{};for(var n in e)t[n]=Fj(e,n);return t}var Bj=function(){function e(t,n,r){If(this,e),this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0}return Tf(e,[{key:"value",get:function(){var e=this._object[this._key];return void 0===e?this._defaultValue:e},set:function(e){this._object[this._key]=e}},{key:"dep",get:function(){return e=xj(this._object),t=this._key,null==(n=pP.get(e))?void 0:n.get(t);var e,t,n}}]),e}();function Fj(e,t,n){var r=e[t];return Ij(r)?r:new Bj(e,t,n)}var Lj,Uj=function(){function e(t,n,r,o){var i=this;If(this,e),this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new wP(t,(function(){i._dirty||(i._dirty=!0,Tj(i))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=r}return Tf(e,[{key:"value",get:function(){var e=xj(this);return Ej(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value},set:function(e){this._setter(e)}}]),e}();function Vj(e,t,n,r){var o;try{o=r?e.apply(void 0,dd(r)):e()}catch(i){Hj(i,t,n)}return o}function zj(e,t,n,r){if(xR(e)){var o=Vj(e,t,n,r);return o&&ER(o)&&o.catch((function(e){Hj(e,t,n)})),o}for(var i=[],a=0;a<e.length;a++)i.push(zj(e[a],t,n,r));return i}function Hj(e,t,n){if(t&&t.vnode,t){for(var r=t.parent,o=t.proxy,i=n;r;){var a=r.ec;if(a)for(var u=0;u<a.length;u++)if(!1===a[u](e,o,i))return;r=r.parent}var c=t.appContext.config.errorHandler;if(c)return void Vj(c,null,10,[e,o,i])}}ek(ek(ek(ek(ek(ek(ek(ek(ek(ek(Lj={},"sp","serverPrefetch hook"),"bc","beforeCreate hook"),"c","created hook"),"bm","beforeMount hook"),"m","mounted hook"),"bu","beforeUpdate hook"),"u","updated"),"bum","beforeUnmount hook"),"um","unmounted hook"),"a","activated hook"),ek(ek(ek(ek(ek(ek(ek(ek(ek(ek(Lj,"da","deactivated hook"),"ec","errorCaptured hook"),"rtc","renderTracked hook"),"rtg","renderTriggered hook"),0,"setup function"),1,"render function"),2,"watcher getter"),3,"watcher callback"),4,"watcher cleanup function"),5,"native event handler"),ek(ek(ek(ek(ek(ek(ek(ek(ek(Lj,6,"component event handler"),7,"vnode hook"),8,"directive hook"),9,"transition hook"),10,"app errorHandler"),11,"app warnHandler"),12,"ref function"),13,"async component loader"),14,"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://new-issue.vuejs.org/?repo=vuejs/core");var Wj=!1,qj=!1,Qj=[],Kj=0,Zj=[],Yj=null,Gj=0,$j=Jy.resolve(),Xj=null;function Jj(e){var t=Xj||$j;return e?t.then(this?ik(e).call(e,this):e):t}function eN(e){Qj.length&&gp(Qj).call(Qj,e,Wj&&e.allowRecurse?Kj+1:Kj)||(null==e.id?Qj.push(e):JC(Qj).call(Qj,function(e){for(var t=Kj+1,n=Qj.length;t<n;){var r=t+n>>>1;oN(Qj[r])<e?t=r+1:n=r}return t}(e.id),0,e),tN())}function tN(){Wj||qj||(qj=!0,Xj=$j.then(aN))}function nN(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Wj?Kj+1:0;t<Qj.length;t++){var n=Qj[t];n&&n.pre&&(JC(Qj).call(Qj,t,1),t--,n())}}function rN(e){if(Zj.length){var t,n=dd(new Rx(Zj));if(Zj.length=0,Yj)return void(t=Yj).push.apply(t,dd(n));for(bT(Yj=n).call(Yj,(function(e,t){return oN(e)-oN(t)})),Gj=0;Gj<Yj.length;Gj++)Yj[Gj]();Yj=null,Gj=0}}var oN=function(e){return null==e.id?1/0:e.id},iN=function(e,t){var n=oN(e)-oN(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function aN(e){qj=!1,Wj=!0,bT(Qj).call(Qj,iN);try{for(Kj=0;Kj<Qj.length;Kj++){var t=Qj[Kj];t&&!1!==t.active&&Vj(t,null,14)}}finally{Kj=0,Qj.length=0,rN(),Wj=!1,Xj=null,(Qj.length||Zj.length)&&aN()}}function uN(e,t){if(!e.isUnmounted){for(var n=e.vnode.props||lR,r=arguments.length,o=new Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];var a,u=o,c=ep(t).call(t,"update:"),l=c&&tp(t).call(t,7);if(l&&l in n){var s=n["".concat("modelValue"===l?"model":l,"Modifiers")]||lR,f=s.number;PC(s)&&(u=lb(o).call(o,(function(e){return SR(e)?PC(e).call(e):e}))),f&&(u=lb(o).call(o,WR))}var v=n[a=UR(t)]||n[a=UR(DR(t))];!v&&c&&(v=n[a=UR(FR(t))]),v&&zj(v,e,6,u);var d=n[a+"Once"];if(d){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,zj(d,e,6,u)}}}function cN(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;var i=e.emits,a={},u=!1;if(!xR(e)){var c,l,s=function(e){var n=cN(e,t,!0);n&&(u=!0,gR(a,n))};if(!n&&t.mixins.length)Rd(c=t.mixins).call(c,s);if(e.extends&&s(e.extends),e.mixins)Rd(l=e.mixins).call(l,s)}return i||u?(wR(i)?Rd(i).call(i,(function(e){return a[e]=null})):gR(a,i),kR(e)&&r.set(e,a),a):(kR(e)&&r.set(e,null),null)}function lN(e,t){return!(!e||!pR(t))&&(t=tp(t).call(t,2).replace(/Once$/,""),bR(e,t[0].toLowerCase()+tp(t).call(t,1))||bR(e,FR(t))||bR(e,t))}var sN=null,fN=null;function vN(e){var t=sN;return sN=e,fN=e&&e.type.__scopeId||null,t}function dN(e){fN=e}function pN(){fN=null}function hN(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:sN;if(!t)return e;if(e._n)return e;var n=function n(){n._d&&KM(-1);var r,o=vN(t);try{r=e.apply(void 0,arguments)}finally{vN(o),n._d&&KM(1)}return r};return n._n=!0,n._c=!0,n._d=!0,n}function gN(e){var t,n,r=e.type,o=e.vnode,i=e.proxy,a=e.withProxy,u=e.props,c=vd(e.propsOptions,1)[0],l=e.slots,s=e.attrs,f=e.emit,v=e.render,d=e.renderCache,p=e.data,h=e.setupState,g=e.ctx,m=e.inheritAttrs,y=vN(e);try{if(4&o.shapeFlag){var b=a||i;t=uD(v.call(b,b,d,u,h,p,g)),n=s}else{var w=r;0,t=uD(w.length>1?w(u,{attrs:s,slots:l,emit:f}):w(u,null)),n=r.props?s:mN(s)}}catch(C){HM.length=0,Hj(C,e,1),t=rD(VM)}var A,_=t;if(n&&!1!==m){var x=db(n),S=_.shapeFlag;x.length&&7&S&&(c&&Ab(x).call(x,hR)&&(n=yN(n,c)),_=oD(_,n))}o.dirs&&((_=oD(_)).dirs=_.dirs?yd(A=_.dirs).call(A,o.dirs):o.dirs);return o.transition&&(_.transition=o.transition),t=_,vN(y),t}var mN=function(e){var t;for(var n in e)("class"===n||"style"===n||pR(n))&&((t||(t={}))[n]=e[n]);return t},yN=function(e,t){var n={};for(var r in e)hR(r)&&tp(r).call(r,9)in t||(n[r]=e[r]);return n};function bN(e,t,n){var r=db(t);if(r.length!==db(e).length)return!0;for(var o=0;o<r.length;o++){var i=r[o];if(t[i]!==e[i]&&!lN(n,i))return!0}return!1}function wN(e,t){return xN(e,null,t)}var AN={};function _N(e,t,n){return xN(e,t,n)}function xN(e,t){var n,r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:lR,i=o.immediate,a=o.deep,u=o.flush;o.onTrack,o.onTrigger;var c,l,s=lP()===(null==(r=hD)?void 0:r.scope)?hD:null,f=!1,v=!1;if(Ij(e)?(c=function(){return e.value},f=Aj(e)):bj(e)?(c=function(){return e},a=!0):wR(e)?(v=!0,f=Ab(e).call(e,(function(e){return bj(e)||Aj(e)})),c=function(){return lb(e).call(e,(function(e){return Ij(e)?e.value:bj(e)?kN(e):xR(e)?Vj(e,s,2):void 0}))}):c=xR(e)?t?function(){return Vj(e,s,2)}:function(){if(!s||!s.isUnmounted)return l&&l(),zj(e,s,3,[h])}:fR,t&&a){var d=c;c=function(){return kN(d())}}var p,h=function(e){l=w.onStop=function(){Vj(e,s,4)}};if(_D){if(h=fR,t?i&&zj(t,s,3,[c(),v?[]:void 0,h]):c(),"sync"!==u)return fR;var g=TD();p=g.__watcherHandles||(g.__watcherHandles=[])}var m,y=v?TT(n=new Array(e.length)).call(n,AN):AN,b=function(){if(w.active)if(t){var e=w.run();(a||f||(v?Ab(e).call(e,(function(e,t){return VR(e,y[t])})):VR(e,y)))&&(l&&l(),zj(t,s,3,[e,y===AN?void 0:v&&y[0]===AN?[]:y,h]),y=e)}else w.run()};b.allowRecurse=!!t,"sync"===u?m=b:"post"===u?m=function(){return MM(b,s&&s.suspense)}:(b.pre=!0,s&&(b.id=s.uid),m=function(){return eN(b)});var w=new wP(c,m);if(t)i?b():y=w.run();else if("post"===u){var A;MM(ik(A=w.run).call(A,w),s&&s.suspense)}else w.run();var _=function(){w.stop(),s&&s.scope&&mR(s.scope.effects,w)};return p&&p.push(_),_}function SN(e,t,n){var r,o=this.proxy,i=SR(e)?gp(e).call(e,".")?CN(o,e):function(){return o[e]}:ik(e).call(e,o,o);xR(t)?r=t:(r=t.handler,n=t);var a=hD;yD(this);var u=xN(i,ik(r).call(r,o),n);return a?yD(a):bD(),u}function CN(e,t){var n=t.split(".");return function(){for(var t=e,r=0;r<n.length&&t;r++)t=t[n[r]];return t}}function kN(e,t){if(!kR(e)||e.__v_skip)return e;if((t=t||new Rx).has(e))return e;if(t.add(e),Ij(e))kN(e.value,t);else if(wR(e))for(var n=0;n<e.length;n++)kN(e[n],t);else if(_R(e)||AR(e))Rd(e).call(e,(function(e){kN(e,t)}));else if(RR(e))for(var r in e)kN(e[r],t);return e}function EN(e,t){var n=sN;if(null===n)return e;for(var r=CD(n)||n.proxy,o=e.dirs||(e.dirs=[]),i=0;i<t.length;i++){var a=vd(t[i],4),u=a[0],c=a[1],l=a[2],s=a[3],f=void 0===s?lR:s;u&&(xR(u)&&(u={mounted:u,updated:u}),u.deep&&kN(c),o.push({dir:u,instance:r,value:c,oldValue:void 0,arg:l,modifiers:f}))}return e}function TN(e,t,n,r){for(var o=e.dirs,i=t&&t.dirs,a=0;a<o.length;a++){var u=o[a];i&&(u.oldValue=i[a].value);var c=u.dir[r];c&&(SP(),zj(c,n,8,[e.el,u,e,t]),CP())}}function IN(e,t){return xR(e)?function(){return gR({name:e.name},t,{setup:e})}():e}var ON=function(e){return!!e.type.__asyncLoader},RN=function(e){return e.type.__isKeepAlive};function PN(e,t){NN(e,"a",t)}function jN(e,t){NN(e,"da",t)}function NN(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:hD,r=e.__wdc||(e.__wdc=function(){for(var t=n;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(DN(t,r,n),n)for(var o=n.parent;o&&o.parent;)RN(o.parent.vnode)&&MN(r,t,n,o),o=o.parent}function MN(e,t,n,r){var o=DN(t,e,r,!0);HN((function(){mR(r[t],o)}),n)}function DN(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:hD,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(n){var o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=function(){if(!n.isUnmounted){SP(),yD(n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];var a=zj(t,n,e,o);return bD(),CP(),a}});return r?o.unshift(i):o.push(i),i}}var BN=function(e){return function(t){return(!_D||"sp"===e)&&DN(e,(function(){return t.apply(void 0,arguments)}),arguments.length>1&&void 0!==arguments[1]?arguments[1]:hD)}},FN=BN("bm"),LN=BN("m"),UN=BN("bu"),VN=BN("u"),zN=BN("bum"),HN=BN("um"),WN=BN("sp"),qN=BN("rtg"),QN=BN("rtc");function KN(e){DN("ec",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:hD)}var ZN=GT("v-ndc");function YN(e,t,n,r){var o,i=n&&n[r];if(wR(e)||SR(e)){o=new Array(e.length);for(var a=0,u=e.length;a<u;a++)o[a]=t(e[a],a,void 0,i&&i[a])}else if("number"==typeof e){o=new Array(e);for(var c=0;c<e;c++)o[c]=t(c+1,c,void 0,i&&i[c])}else if(kR(e))if(sf(e))o=hb(e,(function(e,n){return t(e,n,void 0,i&&i[n])}));else{var l=db(e);o=new Array(l.length);for(var s=0,f=l.length;s<f;s++){var v=l[s];o[s]=t(e[v],v,s,i&&i[s])}}else o=[];return n&&(n[r]=o),o}function GN(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(sN.isCE||sN.parent&&ON(sN.parent)&&sN.parent.isCE)return"default"!==t&&(n.name=t),rD("slot",n,r&&r());var i=e[t];i&&i._c&&(i._d=!1),qM();var a=i&&$N(i(n)),u=GM(LM,{key:n.key||a&&a.key||"_".concat(t)},a||(r?r():[]),a&&1===e._?64:-2);return!o&&u.scopeId&&(u.slotScopeIds=[u.scopeId+"-s"]),i&&i._c&&(i._d=!0),u}function $N(e){return Ab(e).call(e,(function(e){return!$M(e)||e.type!==VM&&!(e.type===LM&&!$N(e.children))}))?e:null}var XN=function e(t){return t?wD(t)?CD(t)||t.proxy:e(t.parent):null},JN=gR(sb(null),{$:function(e){return e},$el:function(e){return e.vnode.el},$data:function(e){return e.data},$props:function(e){return e.props},$attrs:function(e){return e.attrs},$slots:function(e){return e.slots},$refs:function(e){return e.refs},$parent:function(e){return XN(e.parent)},$root:function(e){return XN(e.root)},$emit:function(e){return e.emit},$options:function(e){return uM(e)},$forceUpdate:function(e){return e.f||(e.f=function(){return eN(e.update)})},$nextTick:function(e){return e.n||(e.n=ik(Jj).call(Jj,e.proxy))},$watch:function(e){return ik(SN).call(SN,e)}}),eM=function(e,t){return e!==lR&&!e.__isScriptSetup&&bR(e,t)},tM={get:function(e,t){var n,r=e._,o=r.ctx,i=r.setupState,a=r.data,u=r.props,c=r.accessCache,l=r.type,s=r.appContext;if("$"!==t[0]){var f=c[t];if(void 0!==f)switch(f){case 1:return i[t];case 2:return a[t];case 4:return o[t];case 3:return u[t]}else{if(eM(i,t))return c[t]=1,i[t];if(a!==lR&&bR(a,t))return c[t]=2,a[t];if((n=r.propsOptions[0])&&bR(n,t))return c[t]=3,u[t];if(o!==lR&&bR(o,t))return c[t]=4,o[t];rM&&(c[t]=0)}}var v,d,p=JN[t];return p?("$attrs"===t&&kP(r,0,t),p(r)):(v=l.__cssModules)&&(v=v[t])?v:o!==lR&&bR(o,t)?(c[t]=4,o[t]):(d=s.config.globalProperties,bR(d,t)?d[t]:void 0)},set:function(e,t,n){var r=e._,o=r.data,i=r.setupState,a=r.ctx;return eM(i,t)?(i[t]=n,!0):o!==lR&&bR(o,t)?(o[t]=n,!0):!bR(r.props,t)&&(("$"!==t[0]||!(tp(t).call(t,1)in r))&&(a[t]=n,!0))},has:function(e,t){var n,r=e._,o=r.data,i=r.setupState,a=r.accessCache,u=r.ctx,c=r.appContext,l=r.propsOptions;return!!a[t]||o!==lR&&bR(o,t)||eM(i,t)||(n=l[0])&&bR(n,t)||bR(u,t)||bR(JN,t)||bR(c.config.globalProperties,t)},defineProperty:function(e,t,n){return null!=n.get?e._.accessCache[t]=0:bR(n,"value")&&this.set(e,t,n.value,null),tI(e,t,n)}};function nM(e){return wR(e)?pI(e).call(e,(function(e,t){return e[t]=null,e}),{}):e}var rM=!0;function oM(e){var t=uM(e),n=e.proxy,r=e.ctx;rM=!1,t.beforeCreate&&iM(t.beforeCreate,e,"bc");var o=t.data,i=t.computed,a=t.methods,u=t.watch,c=t.provide,l=t.inject,s=t.created,f=t.beforeMount,v=t.mounted,d=t.beforeUpdate,p=t.updated,h=t.activated,g=t.deactivated;t.beforeDestroy;var m=t.beforeUnmount;t.destroyed;var y=t.unmounted,b=t.render,w=t.renderTracked,A=t.renderTriggered,_=t.errorCaptured,x=t.serverPrefetch,S=t.expose,C=t.inheritAttrs,k=t.components,E=t.directives;if(t.filters,l&&function(e,t){wR(e)&&(e=fM(e));var n=function(){var n,o=e[r];Ij(n=kR(o)?"default"in o?bM(o.from||r,o.default,!0):bM(o.from||r):bM(o))?pb(t,r,{enumerable:!0,configurable:!0,get:function(){return n.value},set:function(e){return n.value=e}}):t[r]=n};for(var r in e)n()}(l,r),a)for(var T in a){var I=a[T];xR(I)&&(r[T]=ik(I).call(I,n))}if(o){var O=o.call(n,n);kR(O)&&(e.data=gj(O))}if(rM=!0,i){var R=function(e){var t,o,a=i[e],u=xR(a)?ik(a).call(a,n,n):xR(a.get)?ik(t=a.get).call(t,n,n):fR,c=!xR(a)&&xR(a.set)?ik(o=a.set).call(o,n):fR,l=kD({get:u,set:c});pb(r,e,{enumerable:!0,configurable:!0,get:function(){return l.value},set:function(e){return l.value=e}})};for(var P in i)R(P)}if(u)for(var j in u)aM(u[j],r,n,j);if(c){var N,M=xR(c)?c.call(n):c;Rd(N=nI(M)).call(N,(function(e){!function(e,t){if(hD){var n=hD.provides,r=hD.parent&&hD.parent.provides;r===n&&(n=hD.provides=sb(r)),n[e]=t}else;}(e,M[e])}))}function D(e,t){wR(t)?Rd(t).call(t,(function(t){return e(ik(t).call(t,n))})):t&&e(ik(t).call(t,n))}if(s&&iM(s,e,"c"),D(FN,f),D(LN,v),D(UN,d),D(VN,p),D(PN,h),D(jN,g),D(KN,_),D(QN,w),D(qN,A),D(zN,m),D(HN,y),D(WN,x),wR(S))if(S.length){var B=e.exposed||(e.exposed={});Rd(S).call(S,(function(e){pb(B,e,{get:function(){return n[e]},set:function(t){return n[e]=t}})}))}else e.exposed||(e.exposed={});b&&e.render===fR&&(e.render=b),null!=C&&(e.inheritAttrs=C),k&&(e.components=k),E&&(e.directives=E)}function iM(e,t,n){zj(wR(e)?lb(e).call(e,(function(e){return ik(e).call(e,t.proxy)})):ik(e).call(e,t.proxy),t,n)}function aM(e,t,n,r){var o=gp(r).call(r,".")?CN(n,r):function(){return n[r]};if(SR(e)){var i=t[e];xR(i)&&_N(o,i)}else if(xR(e))_N(o,ik(e).call(e,n));else if(kR(e))if(wR(e))Rd(e).call(e,(function(e){return aM(e,t,n,r)}));else{var a,u=xR(e.handler)?ik(a=e.handler).call(a,n):t[e.handler];xR(u)&&_N(o,u,e)}}function uM(e){var t,n=e.type,r=n.mixins,o=n.extends,i=e.appContext,a=i.mixins,u=i.optionsCache,c=i.config.optionMergeStrategies,l=u.get(n);return l?t=l:a.length||r||o?(t={},a.length&&Rd(a).call(a,(function(e){return cM(t,e,c,!0)})),cM(t,n,c)):t=n,kR(n)&&u.set(n,t),t}function cM(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=t.mixins,i=t.extends;for(var a in i&&cM(e,i,n,!0),o&&Rd(o).call(o,(function(t){return cM(e,t,n,!0)})),t)if(r&&"expose"===a);else{var u=lM[a]||n&&n[a];e[a]=u?u(e[a],t[a]):t[a]}return e}var lM={data:sM,props:pM,emits:pM,methods:dM,computed:dM,beforeCreate:vM,created:vM,beforeMount:vM,mounted:vM,beforeUpdate:vM,updated:vM,beforeDestroy:vM,beforeUnmount:vM,destroyed:vM,unmounted:vM,activated:vM,deactivated:vM,errorCaptured:vM,serverPrefetch:vM,components:dM,directives:dM,watch:function(e,t){if(!e)return t;if(!t)return e;var n=gR(sb(null),e);for(var r in t)n[r]=vM(e[r],t[r]);return n},provide:sM,inject:function(e,t){return dM(fM(e),fM(t))}};function sM(e,t){return t?e?function(){return gR(xR(e)?e.call(this,this):e,xR(t)?t.call(this,this):t)}:t:e}function fM(e){if(wR(e)){for(var t={},n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function vM(e,t){var n;return e?dd(new Rx(yd(n=[]).call(n,e,t))):t}function dM(e,t){return e?gR(sb(null),e,t):t}function pM(e,t){var n;return e?wR(e)&&wR(t)?dd(new Rx(yd(n=[]).call(n,dd(e),dd(t)))):gR(sb(null),nM(e),nM(null!=t?t:{})):t}function hM(){return{app:null,config:{isNativeTag:vR,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:sb(null),optionsCache:new cC,propsCache:new cC,emitsCache:new cC}}var gM=0;function mM(e,t){return function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;xR(n)||(n=gR({},n)),null==r||kR(r)||(r=null);var o=hM(),i=new Rx,a=!1,u=o.app={_uid:gM++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:ID,get config(){return o.config},set config(e){},use:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(i.has(e));else if(e&&xR(e.install)){var o;i.add(e),e.install.apply(e,yd(o=[u]).call(o,n))}else if(xR(e)){var a;i.add(e),e.apply(void 0,yd(a=[u]).call(a,n))}return u},mixin:function(e){var t;return gp(t=o.mixins).call(t,e)||o.mixins.push(e),u},component:function(e,t){return t?(o.components[e]=t,u):o.components[e]},directive:function(e,t){return t?(o.directives[e]=t,u):o.directives[e]},mount:function(i,c,l){if(!a){var s=rD(n,r);return s.appContext=o,c&&t?t(s,i):e(s,i,l),a=!0,u._container=i,i.__vue_app__=u,CD(s.component)||s.component.proxy}},unmount:function(){a&&(e(null,u._container),delete u._container.__vue_app__)},provide:function(e,t){return o.provides[e]=t,u},runWithContext:function(e){yM=u;try{return e()}finally{yM=null}}};return u}}var yM=null;function bM(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=hD||sN;if(r||yM){var o=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:yM._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&xR(t)?t.call(r&&r.proxy):t}}function wM(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o={},i={};for(var a in HR(i,JM,1),e.propsDefaults=sb(null),AM(e,t,o,i),e.propsOptions[0])a in o||(o[a]=void 0);n?e.props=r?o:yj(o,!1,HP,sj,dj):e.type.props?e.props=o:e.props=i,e.attrs=i}function AM(e,t,n,r){var o,i=vd(e.propsOptions,2),a=i[0],u=i[1],c=!1;if(t)for(var l in t)if(!jR(l)){var s=t[l],f=void 0;a&&bR(a,f=DR(l))?u&&gp(u).call(u,f)?(o||(o={}))[f]=s:n[f]=s:lN(e.emitsOptions,l)||l in r&&s===r[l]||(r[l]=s,c=!0)}if(u)for(var v=xj(n),d=o||lR,p=0;p<u.length;p++){var h=u[p];n[h]=_M(a,v,h,d[h],e,!bR(d,h))}return c}function _M(e,t,n,r,o,i){var a=e[n];if(null!=a){var u=bR(a,"default");if(u&&void 0===r){var c=a.default;if(a.type!==Function&&!a.skipFactory&&xR(c)){var l=o.propsDefaults;n in l?r=l[n]:(yD(o),r=l[n]=c.call(null,t),bD())}else r=c}a[0]&&(i&&!u?r=!1:!a[1]||""!==r&&r!==FR(n)||(r=!0))}return r}function xM(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.propsCache,o=r.get(e);if(o)return o;var i=e.props,a={},u=[],c=!1;if(!xR(e)){var l,s,f=function(e){c=!0;var n=vd(xM(e,t,!0),2),r=n[0],o=n[1];gR(a,r),o&&u.push.apply(u,dd(o))};if(!n&&t.mixins.length)Rd(l=t.mixins).call(l,f);if(e.extends&&f(e.extends),e.mixins)Rd(s=e.mixins).call(s,f)}if(!i&&!c)return kR(e)&&r.set(e,sR),sR;if(wR(i))for(var v=0;v<i.length;v++){var d=DR(i[v]);SM(d)&&(a[d]=lR)}else if(i)for(var p in i){var h=DR(p);if(SM(h)){var g=i[p],m=a[h]=wR(g)||xR(g)?{type:g}:gR({},g);if(m){var y=EM(Boolean,m.type),b=EM(String,m.type);m[0]=y>-1,m[1]=b<0||y<b,(y>-1||bR(m,"default"))&&u.push(h)}}}var w=[a,u];return kR(e)&&r.set(e,w),w}function SM(e){return"$"!==e[0]}function CM(e){var t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function kM(e,t){return CM(e)===CM(t)}function EM(e,t){return wR(t)?xI(t).call(t,(function(t){return kM(t,e)})):xR(t)&&kM(t,e)?0:-1}var TM=function(e){return"_"===e[0]||"$stable"===e},IM=function(e){return wR(e)?lb(e).call(e,uD):[uD(e)]},OM=function(e,t,n){var r=e._ctx,o=function(){if(TM(i))return 1;var n=e[i];if(xR(n))t[i]=function(e,t,n){if(t._n)return t;var r=hN((function(){return IM(t.apply(void 0,arguments))}),n);return r._c=!1,r}(0,n,r);else if(null!=n){var o=IM(n);t[i]=function(){return o}}};for(var i in e)o()},RM=function(e,t){var n=IM(t);e.slots.default=function(){return n}},PM=function(e,t){if(32&e.vnode.shapeFlag){var n=t._;n?(e.slots=xj(t),HR(t,"_",n)):OM(t,e.slots={})}else e.slots={},t&&RM(e,t);HR(e.slots,JM,1)},jM=function(e,t,n){var r=e.vnode,o=e.slots,i=!0,a=lR;if(32&r.shapeFlag){var u=t._;u?n&&1===u?i=!1:(gR(o,t),n||1!==u||delete o._):(i=!t.$stable,OM(t,o)),a=t}else t&&(RM(e,t),a={default:1});if(i)for(var c in o)TM(c)||c in a||delete o[c]};function NM(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(wR(e))Rd(e).call(e,(function(e,i){return NM(e,t&&(wR(t)?t[i]:t),n,r,o)}));else if(!ON(r)||o){var i=4&r.shapeFlag?CD(r.component)||r.component.proxy:r.el,a=o?null:i,u=e.i,c=e.r,l=t&&t.r,s=u.refs===lR?u.refs={}:u.refs,f=u.setupState;if(null!=l&&l!==c&&(SR(l)?(s[l]=null,bR(f,l)&&(f[l]=null)):Ij(l)&&(l.value=null)),xR(c))Vj(c,u,12,[a,s]);else{var v=SR(c),d=Ij(c);if(v||d){var p=function(){if(e.f){var t=v?bR(f,c)?f[c]:s[c]:c.value;o?wR(t)&&mR(t,i):wR(t)?gp(t).call(t,i)||t.push(i):v?(s[c]=[i],bR(f,c)&&(f[c]=s[c])):(c.value=[i],e.k&&(s[e.k]=c.value))}else v?(s[c]=a,bR(f,c)&&(f[c]=a)):d&&(c.value=a,e.k&&(s[e.k]=a))};a?(p.id=-1,MM(p,n)):p()}}}}var MM=function(e,t){var n,r;t&&t.pendingBranch?wR(e)?(n=t.effects).push.apply(n,dd(e)):t.effects.push(e):(wR(r=e)?Zj.push.apply(Zj,dd(r)):Yj&&gp(Yj).call(Yj,r,r.allowRecurse?Gj+1:Gj)||Zj.push(r),tN())};function DM(e){return function(e,t){qR().__VUE__=!0;var n,r,o=e.insert,i=e.remove,a=e.patchProp,u=e.createElement,c=e.createText,l=e.createComment,s=e.setText,f=e.setElementText,v=e.parentNode,d=e.nextSibling,p=e.setScopeId,h=void 0===p?fR:p,g=e.insertStaticContent,m=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,a=arguments.length>6&&void 0!==arguments[6]&&arguments[6],u=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:!!t.dynamicChildren;if(e!==t){e&&!XM(e,t)&&(r=q(e),U(e,o,i,!0),e=null),-2===t.patchFlag&&(c=!1,t.dynamicChildren=null);var l=t.type,s=t.ref,f=t.shapeFlag;switch(l){case UM:y(e,t,n,r);break;case VM:b(e,t,n,r);break;case zM:null==e&&w(t,n,r,a);break;case LM:O(e,t,n,r,o,i,a,u,c);break;default:1&f?x(e,t,n,r,o,i,a,u,c):6&f?R(e,t,n,r,o,i,a,u,c):(64&f||128&f)&&l.process(e,t,n,r,o,i,a,u,c,K)}null!=s&&o&&NM(s,e&&e.ref,i,t||e,!t)}},y=function(e,t,n,r){if(null==e)o(t.el=c(t.children),n,r);else{var i=t.el=e.el;t.children!==e.children&&s(i,t.children)}},b=function(e,t,n,r){null==e?o(t.el=l(t.children||""),n,r):t.el=e.el},w=function(e,t,n,r){var o=vd(g(e.children,t,n,r,e.el,e.anchor),2);e.el=o[0],e.anchor=o[1]},A=function(e,t,n){for(var r,i=e.el,a=e.anchor;i&&i!==a;)r=d(i),o(i,t,n),i=r;o(a,t,n)},_=function(e){for(var t,n=e.el,r=e.anchor;n&&n!==r;)t=d(n),i(n),n=t;i(r)},x=function(e,t,n,r,o,i,a,u,c){a=a||"svg"===t.type,null==e?S(t,n,r,o,i,a,u,c):E(e,t,o,i,a,u,c)},S=function(e,t,n,r,i,c,l,s){var v,d,p=e.type,h=e.props,g=e.shapeFlag,m=e.transition,y=e.dirs;if(v=e.el=u(e.type,c,h&&h.is,h),8&g?f(v,e.children):16&g&&k(e.children,v,null,r,i,c&&"foreignObject"!==p,l,s),y&&TN(e,null,r,"created"),C(v,e,e.scopeId,l,r),h){for(var b in h)"value"===b||jR(b)||a(v,b,null,h[b],c,e.children,r,i,W);"value"in h&&a(v,"value",null,h.value),(d=h.onVnodeBeforeMount)&&sD(d,r,e)}y&&TN(e,null,r,"beforeMount");var w=(!i||i&&!i.pendingBranch)&&m&&!m.persisted;w&&m.beforeEnter(v),o(v,t,n),((d=h&&h.onVnodeMounted)||w||y)&&MM((function(){d&&sD(d,r,e),w&&m.enter(v),y&&TN(e,null,r,"mounted")}),i)},C=function e(t,n,r,o,i){if(r&&h(t,r),o)for(var a=0;a<o.length;a++)h(t,o[a]);if(i&&n===i.subTree){var u=i.vnode;e(t,u,u.scopeId,u.slotScopeIds,i.parent)}},k=function(e,t,n,r,o,i,a,u){for(var c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:0;c<e.length;c++){var l=e[c]=u?cD(e[c]):uD(e[c]);m(null,l,t,n,r,o,i,a,u)}},E=function(e,t,n,r,o,i,u){var c=t.el=e.el,l=t.patchFlag,s=t.dynamicChildren,v=t.dirs;l|=16&e.patchFlag;var d,p=e.props||lR,h=t.props||lR;n&&BM(n,!1),(d=h.onVnodeBeforeUpdate)&&sD(d,n,t,e),v&&TN(t,e,n,"beforeUpdate"),n&&BM(n,!0);var g=o&&"foreignObject"!==t.type;if(s?T(e.dynamicChildren,s,c,n,r,g,i):u||D(e,t,c,null,n,r,g,i,!1),l>0){if(16&l)I(c,t,p,h,n,r,o);else if(2&l&&p.class!==h.class&&a(c,"class",null,h.class,o),4&l&&a(c,"style",p.style,h.style,o),8&l)for(var m=t.dynamicProps,y=0;y<m.length;y++){var b=m[y],w=p[b],A=h[b];A===w&&"value"!==b||a(c,b,w,A,o,e.children,n,r,W)}1&l&&e.children!==t.children&&f(c,t.children)}else u||null!=s||I(c,t,p,h,n,r,o);((d=h.onVnodeUpdated)||v)&&MM((function(){d&&sD(d,n,t,e),v&&TN(t,e,n,"updated")}),r)},T=function(e,t,n,r,o,i,a){for(var u=0;u<t.length;u++){var c=e[u],l=t[u],s=c.el&&(c.type===LM||!XM(c,l)||70&c.shapeFlag)?v(c.el):n;m(c,l,s,null,r,o,i,a,!0)}},I=function(e,t,n,r,o,i,u){if(n!==r){if(n!==lR)for(var c in n)jR(c)||c in r||a(e,c,n[c],null,u,t.children,o,i,W);for(var l in r)if(!jR(l)){var s=r[l],f=n[l];s!==f&&"value"!==l&&a(e,l,f,s,u,t.children,o,i,W)}"value"in r&&a(e,"value",n.value,r.value)}},O=function(e,t,n,r,i,a,u,l,s){var f=t.el=e?e.el:c(""),v=t.anchor=e?e.anchor:c(""),d=t.patchFlag,p=t.dynamicChildren,h=t.slotScopeIds;h&&(l=l?yd(l).call(l,h):h),null==e?(o(f,n,r),o(v,n,r),k(t.children,n,v,i,a,u,l,s)):d>0&&64&d&&p&&e.dynamicChildren?(T(e.dynamicChildren,p,n,i,a,u,l),(null!=t.key||i&&t===i.subTree)&&FM(e,t,!0)):D(e,t,n,v,i,a,u,l,s)},R=function(e,t,n,r,o,i,a,u,c){t.slotScopeIds=u,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,a,c):P(t,n,r,o,i,a,c):j(e,t,c)},P=function(e,t,n,r,o,i,a){var u=e.component=function(e,t,n){var r=e.type,o=(t?t.appContext:e.appContext)||fD,i={uid:vD++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new cP(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:sb(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:xM(r,o),emitsOptions:cN(r,o),emit:null,emitted:null,propsDefaults:lR,inheritAttrs:r.inheritAttrs,ctx:lR,data:lR,props:lR,attrs:lR,slots:lR,refs:lR,setupState:lR,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=ik(uN).call(uN,null,i),e.ce&&e.ce(i);return i}(e,r,o);if(RN(e)&&(u.ctx.renderer=K),function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];_D=t;var n=e.vnode,r=n.props,o=n.children,i=wD(e);wM(e,r,i,t),PM(e,o);var a=i?function(e,t){var n=e.type;e.accessCache=sb(null),e.proxy=Sj(new Proxy(e.ctx,tM));var r=n.setup;if(r){var o=e.setupContext=r.length>1?function(e){var t=function(t){e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:function(t,n){return kP(e,0,"$attrs"),t[n]}}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null;yD(e),SP();var i=Vj(r,e,0,[e.props,o]);if(CP(),bD(),ER(i)){if(i.then(bD,bD),t)return i.then((function(n){xD(e,n,t)})).catch((function(t){Hj(t,e,0)}));e.asyncDep=i}else xD(e,i,t)}else SD(e,t)}(e,t):void 0;_D=!1}(u),u.asyncDep){if(o&&o.registerDep(u,N),!e.el){var c=u.subTree=rD(VM);b(null,c,t,n)}}else N(u,e,t,n,o,i,a)},j=function(e,t,n){var r,o,i=t.component=e.component;if(function(e,t,n){var r=e.props,o=e.children,i=e.component,a=t.props,u=t.children,c=t.patchFlag,l=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&c>=0))return!(!o&&!u||u&&u.$stable)||r!==a&&(r?!a||bN(r,a,l):!!a);if(1024&c)return!0;if(16&c)return r?bN(r,a,l):!!a;if(8&c)for(var s=t.dynamicProps,f=0;f<s.length;f++){var v=s[f];if(a[v]!==r[v]&&!lN(l,v))return!0}return!1}(e,t,n)){if(i.asyncDep&&!i.asyncResolved)return void M(i,t,n);i.next=t,r=i.update,(o=hk(Qj).call(Qj,r))>Kj&&JC(Qj).call(Qj,o,1),i.update()}else t.el=e.el,i.vnode=t},N=function(e,t,n,o,i,a,u){var c=function(){if(e.isMounted){var c,l=e.next,s=e.bu,f=e.u,d=e.parent,p=e.vnode,h=l;BM(e,!1),l?(l.el=p.el,M(e,l,u)):l=p,s&&zR(s),(c=l.props&&l.props.onVnodeBeforeUpdate)&&sD(c,d,l,p),BM(e,!0);var g=gN(e),y=e.subTree;e.subTree=g,m(y,g,v(y.el),q(y),e,i,a),l.el=g.el,null===h&&function(e,t){for(var n=e.vnode,r=e.parent;r&&r.subTree===n;)(n=r.vnode).el=t,r=r.parent}(e,g.el),f&&MM(f,i),(c=l.props&&l.props.onVnodeUpdated)&&MM((function(){return sD(c,d,l,p)}),i)}else{var b,w=t,A=w.el,_=w.props,x=e.bm,S=e.m,C=e.parent,k=ON(t);if(BM(e,!1),x&&zR(x),!k&&(b=_&&_.onVnodeBeforeMount)&&sD(b,C,t),BM(e,!0),A&&r){var E=function(){e.subTree=gN(e),r(A,e.subTree,e,i,null)};k?t.type.__asyncLoader().then((function(){return!e.isUnmounted&&E()})):E()}else{var T=e.subTree=gN(e);m(null,T,n,o,e,i,a),t.el=T.el}if(S&&MM(S,i),!k&&(b=_&&_.onVnodeMounted)){var I=t;MM((function(){return sD(b,C,I)}),i)}(256&t.shapeFlag||C&&ON(C.vnode)&&256&C.vnode.shapeFlag)&&e.a&&MM(e.a,i),e.isMounted=!0,t=n=o=null}},l=e.effect=new wP(c,(function(){return eN(s)}),e.scope),s=e.update=function(){return l.run()};s.id=e.uid,BM(e,!0),s()},M=function(e,t,n){t.component=e;var r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){var o=e.props,i=e.attrs,a=e.vnode.patchFlag,u=xj(o),c=vd(e.propsOptions,1)[0],l=!1;if(!(r||a>0)||16&a){var s;for(var f in AM(e,t,o,i)&&(l=!0),u)t&&(bR(t,f)||(s=FR(f))!==f&&bR(t,s))||(c?!n||void 0===n[f]&&void 0===n[s]||(o[f]=_M(c,u,f,void 0,e,!0)):delete o[f]);if(i!==u)for(var v in i)t&&bR(t,v)||(delete i[v],l=!0)}else if(8&a)for(var d=e.vnode.dynamicProps,p=0;p<d.length;p++){var h=d[p];if(!lN(e.emitsOptions,h)){var g=t[h];if(c)if(bR(i,h))g!==i[h]&&(i[h]=g,l=!0);else{var m=DR(h);o[m]=_M(c,u,m,g,e,!1)}else g!==i[h]&&(i[h]=g,l=!0)}}l&&TP(e,"set","$attrs")}(e,t.props,r,n),jM(e,t.children,n),SP(),nN(),CP()},D=function(e,t,n,r,o,i,a,u){var c=arguments.length>8&&void 0!==arguments[8]&&arguments[8],l=e&&e.children,s=e?e.shapeFlag:0,v=t.children,d=t.patchFlag,p=t.shapeFlag;if(d>0){if(128&d)return void F(l,v,n,r,o,i,a,u,c);if(256&d)return void B(l,v,n,r,o,i,a,u,c)}8&p?(16&s&&W(l,o,i),v!==l&&f(n,v)):16&s?16&p?F(l,v,n,r,o,i,a,u,c):W(l,o,i,!0):(8&s&&f(n,""),16&p&&k(v,n,r,o,i,a,u,c))},B=function(e,t,n,r,o,i,a,u,c){t=t||sR;var l,s=(e=e||sR).length,f=t.length,v=Math.min(s,f);for(l=0;l<v;l++){var d=t[l]=c?cD(t[l]):uD(t[l]);m(e[l],d,n,null,o,i,a,u,c)}s>f?W(e,o,i,!0,!1,v):k(t,n,r,o,i,a,u,c,v)},F=function(e,t,n,r,o,i,a,u,c){for(var l=0,s=t.length,f=e.length-1,v=s-1;l<=f&&l<=v;){var d=e[l],p=t[l]=c?cD(t[l]):uD(t[l]);if(!XM(d,p))break;m(d,p,n,null,o,i,a,u,c),l++}for(;l<=f&&l<=v;){var h=e[f],g=t[v]=c?cD(t[v]):uD(t[v]);if(!XM(h,g))break;m(h,g,n,null,o,i,a,u,c),f--,v--}if(l>f){if(l<=v)for(var y=v+1,b=y<s?t[y].el:r;l<=v;)m(null,t[l]=c?cD(t[l]):uD(t[l]),n,b,o,i,a,u,c),l++}else if(l>v)for(;l<=f;)U(e[l],o,i,!0),l++;else{var w,A=l,_=l,x=new DE;for(l=_;l<=v;l++){var S=t[l]=c?cD(t[l]):uD(t[l]);null!=S.key&&x.set(S.key,l)}var C=0,k=v-_+1,E=!1,T=0,I=new Array(k);for(l=0;l<k;l++)I[l]=0;for(l=A;l<=f;l++){var O=e[l];if(C>=k)U(O,o,i,!0);else{var R=void 0;if(null!=O.key)R=x.get(O.key);else for(w=_;w<=v;w++)if(0===I[w-_]&&XM(O,t[w])){R=w;break}void 0===R?U(O,o,i,!0):(I[R-_]=l+1,R>=T?T=R:E=!0,m(O,t[R],n,null,o,i,a,u,c),C++)}}var P=E?function(e){var t,n,r,o,i,a=tp(e).call(e),u=[0],c=e.length;for(t=0;t<c;t++){var l=e[t];if(0!==l){if(e[n=u[u.length-1]]<l){a[t]=n,u.push(t);continue}for(r=0,o=u.length-1;r<o;)e[u[i=r+o>>1]]<l?r=i+1:o=i;l<e[u[r]]&&(r>0&&(a[t]=u[r-1]),u[r]=t)}}r=u.length,o=u[r-1];for(;r-- >0;)u[r]=o,o=a[o];return u}(I):sR;for(w=P.length-1,l=k-1;l>=0;l--){var j=_+l,N=t[j],M=j+1<s?t[j+1].el:r;0===I[l]?m(null,N,n,M,o,i,a,u,c):E&&(w<0||l!==P[w]?L(N,n,M,2):w--)}}},L=function e(t,n,r,i){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,u=t.el,c=t.type,l=t.transition,s=t.children,f=t.shapeFlag;if(6&f)e(t.component.subTree,n,r,i);else if(128&f)t.suspense.move(n,r,i);else if(64&f)c.move(t,n,r,K);else if(c!==LM){if(c!==zM)if(2!==i&&1&f&&l)if(0===i)l.beforeEnter(u),o(u,n,r),MM((function(){return l.enter(u)}),a);else{var v=l.leave,d=l.delayLeave,p=l.afterLeave,h=function(){return o(u,n,r)},g=function(){v(u,(function(){h(),p&&p()}))};d?d(u,h,g):g()}else o(u,n,r);else A(t,n,r)}else{o(u,n,r);for(var m=0;m<s.length;m++)e(s[m],n,r,i);o(t.anchor,n,r)}},U=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=e.type,a=e.props,u=e.ref,c=e.children,l=e.dynamicChildren,s=e.shapeFlag,f=e.patchFlag,v=e.dirs;if(null!=u&&NM(u,null,n,e,!0),256&s)t.ctx.deactivate(e);else{var d,p=1&s&&v,h=!ON(e);if(h&&(d=a&&a.onVnodeBeforeUnmount)&&sD(d,t,e),6&s)H(e.component,n,r);else{if(128&s)return void e.suspense.unmount(n,r);p&&TN(e,null,t,"beforeUnmount"),64&s?e.type.remove(e,t,n,o,K,r):l&&(i!==LM||f>0&&64&f)?W(l,t,n,!1,!0):(i===LM&&384&f||!o&&16&s)&&W(c,t,n),r&&V(e)}(h&&(d=a&&a.onVnodeUnmounted)||p)&&MM((function(){d&&sD(d,t,e),p&&TN(e,null,t,"unmounted")}),n)}},V=function(e){var t=e.type,n=e.el,r=e.anchor,o=e.transition;if(t!==LM)if(t!==zM){var a=function(){i(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){var u=o.leave,c=o.delayLeave,l=function(){return u(n,a)};c?c(e.el,a,l):l()}else a()}else _(e);else z(n,r)},z=function(e,t){for(var n;e!==t;)n=d(e),i(e),e=n;i(t)},H=function(e,t,n){var r=e.bum,o=e.scope,i=e.update,a=e.subTree,u=e.um;r&&zR(r),o.stop(),i&&(i.active=!1,U(a,e,t,n)),u&&MM(u,t),MM((function(){e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},W=function(e,t,n){for(var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;i<e.length;i++)U(e[i],t,n,r,o)},q=function e(t){return 6&t.shapeFlag?e(t.component.subTree):128&t.shapeFlag?t.suspense.next():d(t.anchor||t.el)},Q=function(e,t,n){null==e?t._vnode&&U(t._vnode,null,null,!0):m(t._vnode||null,e,t,null,null,null,n),nN(),rN(),t._vnode=e},K={p:m,um:U,m:L,r:V,mt:P,mc:k,pc:D,pbc:T,n:q,o:e};if(t){var Z=vd(t(K),2);n=Z[0],r=Z[1]}return{render:Q,hydrate:n,createApp:mM(Q,n)}}(e)}function BM(e,t){var n=e.effect,r=e.update;n.allowRecurse=r.allowRecurse=t}function FM(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.children,o=t.children;if(wR(r)&&wR(o))for(var i=0;i<r.length;i++){var a=r[i],u=o[i];1&u.shapeFlag&&!u.dynamicChildren&&((u.patchFlag<=0||32===u.patchFlag)&&((u=o[i]=cD(o[i])).el=a.el),n||FM(a,u)),u.type===UM&&(u.el=a.el)}}var LM=GT("v-fgt"),UM=GT("v-txt"),VM=GT("v-cmt"),zM=GT("v-stc"),HM=[],WM=null;function qM(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];HM.push(WM=e?null:[])}var QM=1;function KM(e){QM+=e}function ZM(e){return e.dynamicChildren=QM>0?WM||sR:null,HM.pop(),WM=HM[HM.length-1]||null,QM>0&&WM&&WM.push(e),e}function YM(e,t,n,r,o,i){return ZM(nD(e,t,n,r,o,i,!0))}function GM(e,t,n,r,o){return ZM(rD(e,t,n,r,o,!0))}function $M(e){return!!e&&!0===e.__v_isVNode}function XM(e,t){return e.type===t.type&&e.key===t.key}var JM="__vInternal",eD=function(e){var t=e.key;return null!=t?t:null},tD=function(e){var t=e.ref,n=e.ref_key,r=e.ref_for;return"number"==typeof t&&(t=""+t),null!=t?SR(t)||Ij(t)||xR(t)?{i:sN,r:t,k:n,f:!!r}:t:null};function nD(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e===LM?0:1,a=arguments.length>6&&void 0!==arguments[6]&&arguments[6],u=arguments.length>7&&void 0!==arguments[7]&&arguments[7],c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&eD(t),ref:t&&tD(t),scopeId:fN,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:sN};return u?(lD(c,n),128&i&&e.normalize(c)):n&&(c.shapeFlag|=SR(n)?8:16),QM>0&&!a&&WM&&(c.patchFlag>0||6&i)&&32!==c.patchFlag&&WM.push(c),c}var rD=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];e&&e!==ZN||(e=VM);if($M(e)){var a=oD(e,t,!0);return n&&lD(a,n),QM>0&&!i&&WM&&(6&a.shapeFlag?WM[hk(WM).call(WM,e)]=a:WM.push(a)),a.patchFlag|=-2,a}u=e,xR(u)&&"__vccOpts"in u&&(e=e.__vccOpts);var u;if(t){var c=t=function(e){return e?_j(e)||JM in e?gR({},e):e:null}(t),l=c.class,s=c.style;l&&!SR(l)&&(t.class=$R(l)),kR(s)&&(_j(s)&&!wR(s)&&(s=gR({},s)),t.style=QR(s))}var f=SR(e)?1:function(e){return e.__isSuspense}(e)?128:function(e){return e.__isTeleport}(e)?64:kR(e)?4:xR(e)?2:0;return nD(e,t,n,r,o,f,i,!0)};function oD(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.props,o=e.ref,i=e.patchFlag,a=e.children,u=t?function(){for(var e={},t=0;t<arguments.length;t++){var n=t<0||arguments.length<=t?void 0:arguments[t];for(var r in n)if("class"===r)e.class!==n.class&&(e.class=$R([e.class,n.class]));else if("style"===r)e.style=QR([e.style,n.style]);else if(pR(r)){var o,i=e[r],a=n[r];if(a&&i!==a&&(!wR(i)||!gp(i).call(i,a)))e[r]=i?yd(o=[]).call(o,i,a):a}else""!==r&&(e[r]=n[r])}return e}(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&eD(u),ref:t&&t.ref?n&&o?wR(o)?yd(o).call(o,tD(t)):[o,tD(t)]:tD(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==LM?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&oD(e.ssContent),ssFallback:e.ssFallback&&oD(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function iD(){return rD(UM,null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)}function aD(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(qM(),GM(VM,null,e)):rD(VM,null,e)}function uD(e){return null==e||"boolean"==typeof e?rD(VM):wR(e)?rD(LM,null,tp(e).call(e)):"object"===Sf(e)?cD(e):rD(UM,null,String(e))}function cD(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:oD(e)}function lD(e,t){var n=0,r=e.shapeFlag;if(null==t)t=null;else if(wR(t))n=16;else if("object"===Sf(t)){if(65&r){var o=t.default;return void(o&&(o._c&&(o._d=!1),lD(e,o()),o._c&&(o._d=!0)))}n=32;var i=t._;i||JM in t?3===i&&sN&&(1===sN.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=sN}else xR(t)?(t={default:t,_ctx:sN},n=32):(t=String(t),64&r?(n=16,t=[iD(t)]):n=8);e.children=t,e.shapeFlag|=n}function sD(e,t,n){zj(e,t,7,[n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:null])}var fD=hM(),vD=0;var dD,pD,hD=null,gD=function(){return hD||sN},mD="__VUE_INSTANCE_SETTERS__";(pD=qR()[mD])||(pD=qR()[mD]=[]),pD.push((function(e){return hD=e})),dD=function(e){pD.length>1?Rd(pD).call(pD,(function(t){return t(e)})):pD[0](e)};var yD=function(e){dD(e),e.scope.on()},bD=function(){hD&&hD.scope.off(),dD(null)};function wD(e){return 4&e.vnode.shapeFlag}var AD,_D=!1;function xD(e,t,n){xR(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:kR(t)&&(e.setupState=Nj(t)),SD(e,n)}function SD(e,t,n){var r=e.type;if(!e.render){if(!t&&AD&&!r.render){var o=r.template||uM(e).template;if(o){var i=e.appContext.config,a=i.isCustomElement,u=i.compilerOptions,c=r.delimiters,l=r.compilerOptions,s=gR(gR({isCustomElement:a,delimiters:c},u),l);r.render=AD(o,s)}}e.render=r.render||fR}yD(e),SP(),oM(e),CP(),bD()}function CD(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Nj(Sj(e.exposed)),{get:function(t,n){return n in t?t[n]:n in JN?JN[n](e):void 0},has:function(e,t){return t in e||t in JN}}))}var kD=function(e,t){return function(e,t){var n,r,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=xR(e);return i?(n=e,r=fR):(n=e.get,r=e.set),new Uj(n,r,i||!r,o)}(e,t,_D)},ED=GT("v-scx"),TD=function(){return bM(ED)},ID="3.3.4",OD="undefined"!=typeof document?document:null,RD=OD&&OD.createElement("template"),PD={insert:function(e,t,n){t.insertBefore(e,n||null)},remove:function(e){var t=e.parentNode;t&&t.removeChild(e)},createElement:function(e,t,n,r){var o=t?OD.createElementNS("http://www.w3.org/2000/svg",e):OD.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:function(e){return OD.createTextNode(e)},createComment:function(e){return OD.createComment(e)},setText:function(e,t){e.nodeValue=t},setElementText:function(e,t){e.textContent=t},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},querySelector:function(e){return OD.querySelector(e)},setScopeId:function(e,t){e.setAttribute(t,"")},insertStaticContent:function(e,t,n,r,o,i){var a=n?n.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==i&&(o=o.nextSibling););else{RD.innerHTML=r?"<svg>".concat(e,"</svg>"):e;var u=RD.content;if(r){for(var c=u.firstChild;c.firstChild;)u.appendChild(c.firstChild);u.removeChild(c)}t.insertBefore(u,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};var jD=/\s*!important$/;function ND(e,t,n){if(wR(n))Rd(n).call(n,(function(n){return ND(e,t,n)}));else if(null==n&&(n=""),ep(t).call(t,"--"))e.setProperty(t,n);else{var r=function(e,t){var n=DD[t];if(n)return n;var r=DR(t);if("filter"!==r&&r in e)return DD[t]=r;r=LR(r);for(var o=0;o<MD.length;o++){var i=MD[o]+r;if(i in e)return DD[t]=i}return t}(e,t);jD.test(n)?e.setProperty(FR(r),n.replace(jD,""),"important"):e[r]=n}}var MD=["Webkit","Moz","ms"],DD={};var BD="http://www.w3.org/1999/xlink";function FD(e,t,n,r){e.addEventListener(t,n,r)}function LD(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=e._vei||(e._vei={}),a=i[t];if(r&&a)a.value=r;else{var u=function(e){var t;if(UD.test(e)){var n;for(t={};n=e.match(UD);)e=tp(e).call(e,0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}var r=":"===e[2]?tp(e).call(e,3):FR(tp(e).call(e,2));return[r,t]}(t),c=vd(u,2),l=c[0],s=c[1];if(r){var f=i[t]=function(e,t){var n=function e(n){if(n._vts){if(n._vts<=e.attached)return}else n._vts=rb();zj(function(e,t){if(wR(t)){var n=e.stopImmediatePropagation;return e.stopImmediatePropagation=function(){n.call(e),e._stopped=!0},lb(t).call(t,(function(e){return function(t){return!t._stopped&&e&&e(t)}}))}return t}(n,e.value),t,5,[n])};return n.value=e,n.attached=HD(),n}(r,o);FD(e,l,f,s)}else a&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,l,a,s),i[t]=void 0)}}var UD=/(?:Once|Passive|Capture)$/;var VD=0,zD=Jy.resolve(),HD=function(){return VD||(zD.then((function(){return VD=0})),VD=rb())};var WD=/^on[a-z]/;function qD(e){var t=gD();if(t){var n=t.ut=function(){var n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e(t.proxy);Rd(n=hb(document.querySelectorAll('[data-v-owner="'.concat(t.uid,'"]')))).call(n,(function(e){return KD(e,r)}))},r=function(){var r=e(t.proxy);QD(t.subTree,r),n(r)};xN(r,null,{flush:"post"}),LN((function(){var e=new MutationObserver(r);e.observe(t.subTree.el.parentNode,{childList:!0}),HN((function(){return e.disconnect()}))}))}}function QD(e,t){if(128&e.shapeFlag){var n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((function(){QD(n.activeBranch,t)}))}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)KD(e.el,t);else if(e.type===LM){var r;Rd(r=e.children).call(r,(function(e){return QD(e,t)}))}else if(e.type===zM)for(var o=e,i=o.el,a=o.anchor;i&&(KD(i,t),i!==a);)i=i.nextSibling}function KD(e,t){if(1===e.nodeType){var n=e.style;for(var r in t)n.setProperty("--".concat(r),t[r])}}var ZD=function(e){var t=e.props["onUpdate:modelValue"]||!1;return wR(t)?function(e){return zR(t,e)}:t};function YD(e){e.target.composing=!0}function GD(e){var t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}var $D={created:function(e,t,n){var r=t.modifiers,o=r.lazy,i=PC(r),a=r.number;e._assign=ZD(n);var u=a||n.props&&"number"===n.props.type;FD(e,o?"change":"input",(function(t){if(!t.target.composing){var n=e.value;i&&(n=PC(n).call(n)),u&&(n=WR(n)),e._assign(n)}})),i&&FD(e,"change",(function(){var t;e.value=PC(t=e.value).call(t)})),o||(FD(e,"compositionstart",YD),FD(e,"compositionend",GD),FD(e,"change",GD))},mounted:function(e,t){var n=t.value;e.value=null==n?"":n},beforeUpdate:function(e,t,n){var r=t.value,o=t.modifiers,i=o.lazy,a=PC(o),u=o.number;if(e._assign=ZD(n),!e.composing){if(document.activeElement===e&&"range"!==e.type){var c;if(i)return;if(a&&PC(c=e.value).call(c)===r)return;if((u||"number"===e.type)&&WR(e.value)===r)return}var l=null==r?"":r;e.value!==l&&(e.value=l)}}},XD=["ctrl","shift","alt","meta"],JD={stop:function(e){return e.stopPropagation()},prevent:function(e){return e.preventDefault()},self:function(e){return e.target!==e.currentTarget},ctrl:function(e){return!e.ctrlKey},shift:function(e){return!e.shiftKey},alt:function(e){return!e.altKey},meta:function(e){return!e.metaKey},left:function(e){return"button"in e&&0!==e.button},middle:function(e){return"button"in e&&1!==e.button},right:function(e){return"button"in e&&2!==e.button},exact:function(e,t){return Ab(XD).call(XD,(function(n){return e["".concat(n,"Key")]&&!gp(t).call(t,n)}))}},eB=function(e,t){return function(n){for(var r,o=0;o<t.length;o++){var i=JD[t[o]];if(i&&i(n,t))return}for(var a=arguments.length,u=new Array(a>1?a-1:0),c=1;c<a;c++)u[c-1]=arguments[c];return e.apply(void 0,yd(r=[n]).call(r,u))}},tB={beforeMount:function(e,t,n){var r=t.value,o=n.transition;e._vod="none"===e.style.display?"":e.style.display,o&&r?o.beforeEnter(e):nB(e,r)},mounted:function(e,t,n){var r=t.value,o=n.transition;o&&r&&o.enter(e)},updated:function(e,t,n){var r=t.value,o=t.oldValue,i=n.transition;!r!=!o&&(i?r?(i.beforeEnter(e),nB(e,!0),i.enter(e)):i.leave(e,(function(){nB(e,!1)})):nB(e,r))},beforeUnmount:function(e,t){nB(e,t.value)}};function nB(e,t){e.style.display=t?e._vod:"none"}var rB,oB=gR({patchProp:function(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=arguments.length>5?arguments[5]:void 0,a=arguments.length>6?arguments[6]:void 0,u=arguments.length>7?arguments[7]:void 0,c=arguments.length>8?arguments[8]:void 0;"class"===t?function(e,t,n){var r,o=e._vtc;o&&(t=(t?yd(r=[t]).call(r,dd(o)):dd(o)).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,o):"style"===t?function(e,t,n){var r=e.style,o=SR(n);if(n&&!o){if(t&&!SR(t))for(var i in t)null==n[i]&&ND(r,i,"");for(var a in n)ND(r,a,n[a])}else{var u=r.display;o?t!==n&&(r.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=u)}}(e,n,r):pR(t)?hR(t)||LD(e,t,n,r,a):("."===t[0]?(t=tp(t).call(t,1),1):"^"===t[0]?(t=tp(t).call(t,1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&WD.test(t)&&xR(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(WD.test(t)&&SR(n))return!1;return t in e}(e,t,r,o))?function(e,t,n,r,o,i,a){if("innerHTML"===t||"textContent"===t)return r&&a(r,o,i),void(e[t]=null==n?"":n);var u=e.tagName;if("value"===t&&"PROGRESS"!==u&&!gp(u).call(u,"-")){e._value=n;var c=null==n?"":n;return("OPTION"===u?e.getAttribute("value"):e.value)!==c&&(e.value=c),void(null==n&&e.removeAttribute(t))}var l=!1;if(""===n||null==n){var s=Sf(e[t]);"boolean"===s?n=JR(n):null==n&&"string"===s?(n="",l=!0):"number"===s&&(n=0,l=!0)}try{e[t]=n}catch(f){}l&&e.removeAttribute(t)}(e,t,r,i,a,u,c):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),function(e,t,n,r,o){if(r&&ep(t).call(t,"xlink:"))null==n?e.removeAttributeNS(BD,tp(t).call(t,6,t.length)):e.setAttributeNS(BD,t,n);else{var i=XR(t);null==n||i&&!JR(n)?e.removeAttribute(t):e.setAttribute(t,i?"":n)}}(e,t,r,o))}},PD);var iB=function(){var e,t=(e=rB||(rB=DM(oB))).createApp.apply(e,arguments),n=t.mount;return t.mount=function(e){var r=function(e){if(SR(e)){return document.querySelector(e)}return e}(e);if(r){var o=t._component;xR(o)||o.render||o.template||(o.template=r.innerHTML),r.innerHTML="";var i=n(r,!1,r instanceof SVGElement);return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i}},t};const aB=n(te.setInterval);var uB=function(e){return e[e.top=0]="top",e[e.bottom=1]="bottom",e}(uB||{});const cB=n(te.Object.getOwnPropertySymbols);var lB={exports:{}},sB=In,fB=i,vB=$,dB=T.f,pB=I;sB({target:"Object",stat:!0,forced:!pB||fB((function(){dB(1)})),sham:!pB},{getOwnPropertyDescriptor:function(e,t){return dB(vB(e),t)}});var hB=te.Object,gB=lB.exports=function(e,t){return hB.getOwnPropertyDescriptor(e,t)};hB.getOwnPropertyDescriptor.sham&&(gB.sham=!0);const mB=n(lB.exports);var yB=_p,bB=$,wB=T,AB=Wo;In({target:"Object",stat:!0,sham:!I},{getOwnPropertyDescriptors:function(e){for(var t,n,r=bB(e),o=wB.f,i=yB(r),a={},u=0;i.length>u;)void 0!==(n=o(r,t=i[u++]))&&AB(a,t,n);return a}});const _B=n(te.Object.getOwnPropertyDescriptors);var xB={exports:{}},SB=In,CB=I,kB=br.f;SB({target:"Object",stat:!0,forced:Object.defineProperties!==kB,sham:!CB},{defineProperties:kB});var EB=te.Object,TB=xB.exports=function(e,t){return EB.defineProperties(e,t)};EB.defineProperties.sham&&(TB.sham=!0);const IB=n(xB.exports);function OB(e){return!!lP()&&(function(e){rP&&rP.cleanups.push(e)}(e),!0)}function RB(e){return"function"==typeof e?e():Pj(e)}const PB="undefined"!=typeof window&&"undefined"!=typeof document,jB=Object.prototype.toString,NB=e=>"[object Object]"===jB.call(e),MB=()=>{},DB=BB();function BB(){var e;return PB&&(null==(e=null==window?void 0:window.navigator)?void 0:e.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent)}function FB(e,t={}){if(!Ij(e))return Dj(e);const n=Array.isArray(e.value)?Array.from({length:e.value.length}):{};for(const r in e.value)n[r]=new Mj((()=>({get:()=>e.value[r],set(n){var o;if(null==(o=RB(t.replaceRef))||o)if(Array.isArray(e.value)){const t=[...e.value];t[r]=n,e.value=t}else{const t={...e.value,[r]:n};Object.setPrototypeOf(t,Object.getPrototypeOf(e.value)),e.value=t}else e.value[r]=n}})));return n}function LB(e,t=!0){gD()?LN(e):t?e():Jj(e)}function UB(e){var t;const n=RB(e);return null!=(t=null==n?void 0:n.$el)?t:n}const VB=PB?window:void 0,zB=PB?window.document:void 0;function HB(...e){let t,n,r,o;if("string"==typeof e[0]||Array.isArray(e[0])?([n,r,o]=e,t=VB):[t,n,r,o]=e,!t)return MB;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const i=[],a=()=>{i.forEach((e=>e())),i.length=0},u=_N((()=>[UB(t),RB(o)]),(([e,t])=>{if(a(),!e)return;const o=NB(t)?{...t}:t;i.push(...n.flatMap((t=>r.map((n=>((e,t,n,r)=>(e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)))(e,t,n,o))))))}),{immediate:!0,flush:"post"}),c=()=>{u(),a()};return OB(c),c}let WB=!1;function qB(e,t,n={}){const{window:r=VB,ignore:o=[],capture:i=!0,detectIframe:a=!1}=n;if(!r)return;DB&&!WB&&(WB=!0,Array.from(r.document.body.children).forEach((e=>e.addEventListener("click",MB))),r.document.documentElement.addEventListener("click",MB));let u=!0;const c=e=>o.some((t=>{if("string"==typeof t)return Array.from(r.document.querySelectorAll(t)).some((t=>t===e.target||e.composedPath().includes(t)));{const n=UB(t);return n&&(e.target===n||e.composedPath().includes(n))}})),l=[HB(r,"click",(n=>{const r=UB(e);r&&r!==n.target&&!n.composedPath().includes(r)&&(0===n.detail&&(u=!c(n)),u?t(n):u=!0)}),{passive:!0,capture:i}),HB(r,"pointerdown",(t=>{const n=UB(e);n&&(u=!t.composedPath().includes(n)&&!c(t))}),{passive:!0}),a&&HB(r,"blur",(n=>{setTimeout((()=>{var o;const i=UB(e);"IFRAME"!==(null==(o=r.document.activeElement)?void 0:o.tagName)||(null==i?void 0:i.contains(r.document.activeElement))||t(n)}),0)}))].filter(Boolean);return()=>l.forEach((e=>e()))}function QB(e){const t=function(){const e=Oj(!1);return gD()&&LN((()=>{e.value=!0})),e}();return kD((()=>(t.value,Boolean(e()))))}function KB(e,t={}){var n,r;const{pointerTypes:o,preventDefault:i,stopPropagation:a,exact:u,onMove:c,onEnd:l,onStart:s,initialValue:f,axis:v="both",draggingElement:d=VB,containerElement:p,handle:h=e}=t,g=Oj(null!=(n=RB(f))?n:{x:0,y:0}),m=Oj(),y=e=>!o||o.includes(e.pointerType),b=e=>{RB(i)&&e.preventDefault(),RB(a)&&e.stopPropagation()},w=t=>{var n;if(!y(t))return;if(RB(u)&&t.target!==RB(e))return;const r=(null!=(n=RB(p))?n:RB(e)).getBoundingClientRect(),o={x:t.clientX-r.left,y:t.clientY-r.top};!1!==(null==s?void 0:s(o,t))&&(m.value=o,b(t))},A=e=>{if(!y(e))return;if(!m.value)return;let{x:t,y:n}=g.value;"x"!==v&&"both"!==v||(t=e.clientX-m.value.x),"y"!==v&&"both"!==v||(n=e.clientY-m.value.y),g.value={x:t,y:n},null==c||c(g.value,e),b(e)},_=e=>{y(e)&&m.value&&(m.value=void 0,null==l||l(g.value,e),b(e))};if(PB){const e={capture:null==(r=t.capture)||r};HB(h,"pointerdown",w,e),HB(d,"pointermove",A,e),HB(d,"pointerup",_,e)}return{...FB(g),position:g,isDragging:kD((()=>!!m.value)),style:kD((()=>`left:${g.value.x}px;top:${g.value.y}px;`))}}function ZB(e,t,n={}){const{window:r=VB,...o}=n;let i;const a=QB((()=>r&&"ResizeObserver"in r)),u=()=>{i&&(i.disconnect(),i=void 0)},c=_N(kD((()=>Array.isArray(e)?e.map((e=>UB(e))):[UB(e)])),(e=>{if(u(),a.value&&r){i=new ResizeObserver(t);for(const t of e)t&&i.observe(t,o)}}),{immediate:!0,flush:"post",deep:!0}),l=()=>{u(),c()};return OB(l),{isSupported:a,stop:l}}function YB(e,t={}){const{reset:n=!0,windowResize:r=!0,windowScroll:o=!0,immediate:i=!0}=t,a=Oj(0),u=Oj(0),c=Oj(0),l=Oj(0),s=Oj(0),f=Oj(0),v=Oj(0),d=Oj(0);function p(){const t=UB(e);if(!t)return void(n&&(a.value=0,u.value=0,c.value=0,l.value=0,s.value=0,f.value=0,v.value=0,d.value=0));const r=t.getBoundingClientRect();a.value=r.height,u.value=r.bottom,c.value=r.left,l.value=r.right,s.value=r.top,f.value=r.width,v.value=r.x,d.value=r.y}return ZB(e,p),_N((()=>UB(e)),(e=>!e&&p())),o&&HB("scroll",p,{capture:!0,passive:!0}),r&&HB("resize",p,{passive:!0}),LB((()=>{i&&p()})),{height:a,bottom:u,left:c,right:l,top:s,width:f,x:v,y:d,update:p}}function GB(e,t={width:0,height:0},n={}){const{window:r=VB,box:o="content-box"}=n,i=kD((()=>{var t,n;return null==(n=null==(t=UB(e))?void 0:t.namespaceURI)?void 0:n.includes("svg")})),a=Oj(t.width),u=Oj(t.height);return ZB(e,(([t])=>{const n="border-box"===o?t.borderBoxSize:"content-box"===o?t.contentBoxSize:t.devicePixelContentBoxSize;if(r&&i.value){const t=UB(e);if(t){const e=r.getComputedStyle(t);a.value=Number.parseFloat(e.width),u.value=Number.parseFloat(e.height)}}else if(n){const e=Array.isArray(n)?n:[n];a.value=e.reduce(((e,{inlineSize:t})=>e+t),0),u.value=e.reduce(((e,{blockSize:t})=>e+t),0)}else a.value=t.contentRect.width,u.value=t.contentRect.height}),n),_N((()=>UB(e)),(e=>{a.value=e?t.width:0,u.value=e?t.height:0})),{width:a,height:u}}function $B(e,t=MB,n={}){const{immediate:r=!0,manual:o=!1,type:i="text/javascript",async:a=!0,crossOrigin:u,referrerPolicy:c,noModule:l,defer:s,document:f=zB,attrs:v={}}=n,d=Oj(null);let p=null;const h=(n=!0)=>(p||(p=(n=>new Promise(((r,o)=>{const p=e=>(d.value=e,r(e),e);if(!f)return void r(!1);let h=!1,g=f.querySelector(`script[src="${RB(e)}"]`);g?g.hasAttribute("data-loaded")&&p(g):(g=f.createElement("script"),g.type=i,g.async=a,g.src=RB(e),s&&(g.defer=s),u&&(g.crossOrigin=u),l&&(g.noModule=l),c&&(g.referrerPolicy=c),Object.entries(v).forEach((([e,t])=>null==g?void 0:g.setAttribute(e,t))),h=!0),g.addEventListener("error",(e=>o(e))),g.addEventListener("abort",(e=>o(e))),g.addEventListener("load",(()=>{g.setAttribute("data-loaded","true"),t(g),p(g)})),h&&(g=f.head.appendChild(g)),n||p(g)})))(n)),p),g=()=>{if(!f)return;p=null,d.value&&(d.value=null);const t=f.querySelector(`script[src="${RB(e)}"]`);t&&f.head.removeChild(t)};var m;return r&&!o&&LB(h),o||(m=g,gD()&&HN(m)),{scriptTag:d,load:h,unload:g}}function XB(e={}){const{window:t=VB,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:r=Number.POSITIVE_INFINITY,listenOrientation:o=!0,includeScrollbar:i=!0}=e,a=Oj(n),u=Oj(r),c=()=>{t&&(i?(a.value=t.innerWidth,u.value=t.innerHeight):(a.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight))};if(c(),LB(c),HB("resize",c,{passive:!0}),o){const e=function(e,t={}){const{window:n=VB}=t,r=QB((()=>n&&"matchMedia"in n&&"function"==typeof n.matchMedia));let o;const i=Oj(!1),a=e=>{i.value=e.matches},u=()=>{o&&("removeEventListener"in o?o.removeEventListener("change",a):o.removeListener(a))},c=wN((()=>{r.value&&(u(),o=n.matchMedia(RB(e)),"addEventListener"in o?o.addEventListener("change",a):o.addListener(a),i.value=o.matches)}));return OB((()=>{c(),u(),o=void 0})),i}("(orientation: portrait)");_N(e,(()=>c()))}return{width:a,height:u}}function JB(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function eF(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=JB(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=JB(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var tF=function(e){let t,n=!1;const r=new cP(!0);return(...o)=>(n||(t=r.run((()=>e(...o))),n=!0),t)}((function(){var e=Oj({likeCrm:"1",filterAdvertisement:"1",crossDomain:!0,platform:0,server:3,fromType:1,ttype:1,reasons:["vstreq:u","vstsys:u","vstwtr:u","vsturl:u","vstwtr:f","vstreq:f","vstsys:f","vstsys:i"],isSessionConnected:!1,webSocket:!1,usingWs:!1,wsUrl:"/",key:"",unreadNum:0}),t=Oj({});function n(e){t.value=eF(eF({},t.value),e)}var r=Oj({});function o(t){r.value=eF(eF(eF({},e.value),r.value),t)}return{styleConfig:t,siteConfig:r,updateStyleConfig:n,updateSiteConfig:o,updateImConfig:function(e){var t=e.siteConfig,r=void 0===t?{}:t,i=e.styleConfig;n(void 0===i?{}:i),o(r)},getAuth:function(){return{anonym:r.value.bid?0:1,key:r.value.key||"",id:r.value.bid||"",from:r.value.authType,token:r.value.authToken||""}}}}));var nF,rF={all:nF=nF||new Map,on:function(e,t){var n=nF.get(e);n?n.push(t):nF.set(e,[t])},off:function(e,t){var n=nF.get(e);n&&(t?n.splice(n.indexOf(t)>>>0,1):nF.set(e,[]))},emit:function(e,t){var n=nF.get(e);n&&n.slice().map((function(e){e(t)})),(n=nF.get("*"))&&n.slice().map((function(n){n(e,t)}))}},oF=function(e){return e&&(rF.on(e.name,e.callback),zN((function(){rF.off(e.name)}))),{emitter:rF}},iF={BIZ_EVENT_ICON_CLICK:"biz_event_icon_click",BIZ_EVENT_INVITE_CHAT_CLICK:"biz_event_invite_chat_click",BIZ_EVENT_GROUP_ICON_CLICK:"biz_event_group_icon_click",BIZ_EVENT_ICON_HOTISSUE_CLICK:"biz_event_icon_hotissue_click",BIZ_EVENT_INVITE_CLICK:"biz_event_invite_click",BIZ_EVENT_INVITE_LOOP_SHOW:"biz_event_invite_loop_show",BIZ_EVENT_INVITE_SHOW:"biz_event_invite_show",BIZ_EVENT_INVITE_ACCEPT:"biz_event_invite_accept",BIZ_EVENT_MESSAGE_SHOW_CENTER:"biz_event_message_show_center",BIZ_EVENT_CHAT_IFRAME_LOADED:"biz_event_chat_iframe_loaded",BIZ_EVENT_CLEAR_AUTO_CHAT:"biz_event_clear_auto_chat",BIZ_EVENT_M_CHAT_SHOW:"biz_event_m_chat_show",BIZ_EVENT_M_NEW_MSG:"biz_event_m_new_msg",BIZ_EVENT_M_CHAT_CONTINUE:"biz_event_m_chat_continue",BIZ_EVENT_M_HIDE_ISSUES:"biz_event_m_hide_issues",BIZ_EVENT_ENTER_PASS:"biz_event_enter_pass"},aF={class:"embed-hot-issue"},uF=["onClick"],cF={class:"embed-hot-issue-text"};const lF=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},sF=lF(IN({__name:"HotIssue",props:{issueAlign:{},rollDir:{}},setup:function(e,t){var n=t.expose,r=e,o=oF().emitter,i=tF().styleConfig.value.seekIcon,a=i.hotIssues,u=Oj(0);aB((function(){u.value===a.length-1?u.value=0:u.value++}),3e3);var c=kD((function(){return function(e){var t=["embed-hot-issue-item","issue-item-".concat(i.barrageColor)];return e===u.value&&t.push("fade-".concat(Number(!uB[r.rollDir]))),u.value?e===u.value-1&&t.push("fade-".concat(uB[r.rollDir])):e===a.length-1&&t.push("fade-".concat(uB[r.rollDir])),t.join(" ")}})),l=Oj([]);return n({getOffsetWidth:function(){var e,t=[];return Rd(e=l.value).call(e,(function(e){t.push(e.clientWidth)})),Math.max.apply(Math,t)}}),function(e,t){return qM(),YM("div",aF,[(qM(!0),YM(LM,null,YN(Pj(a),(function(t,n){return qM(),YM("p",{key:n,ref_for:!0,ref_key:"issueItem",ref:l,style:QR("".concat(e.issueAlign,": 0;")),class:$R(c.value(n)),onClick:function(e){return Pj(o).emit(Pj(iF).BIZ_EVENT_ICON_HOTISSUE_CLICK,t)}},[nD("span",{class:"embed-hot-issue-icon",style:QR({"background-color":Pj(i).barrageIconColor})},null,4),nD("span",cF,oP(t.question),1)],14,uF)})),128))])}}}),[["__scopeId","data-v-d2162698"]]);var fF=function(e){return dN("data-v-3d95fcfa"),e=e(),pN(),e},vF={key:0,class:"embed-group-icon__default-icon"},dF=[fF((function(){return nD("span",{class:"embed-group-icon-img"},null,-1)})),fF((function(){return nD("span",{class:"embed-group-icon-title"},"在线咨询",-1)}))],pF=["onClick"];const hF=lF(IN({__name:"GroupIcon",props:{rate:{}},setup:function(e){var t=e,n=oF().emitter,r=tF().styleConfig.value.seekIcon,o=r.customerStyle.backImg,i=kD((function(){return Math.ceil((r.groupWidth+16)/(r.customerStyle.rate||t.rate))}));return function(e,t){return qM(),YM(LM,null,[Pj(r).isCustomerStyle?(qM(),YM("div",{key:0,class:"embed-group-icon__custom-icon",style:QR({backgroundImage:"url(".concat(Pj(o),")"),height:"".concat(i.value,"px")})},null,4)):aD("",!0),nD("div",{class:"embed-group-icon",style:QR({width:"".concat(Pj(r).groupWidth,"px"),"background-color":Pj(r).pcGroupiconColor})},[Pj(r).isCustomerStyle?aD("",!0):(qM(),YM("div",vF,dF)),(qM(!0),YM(LM,null,YN(Pj(r).groups,(function(e,t){return qM(),YM("div",{key:t,class:$R(["embed-group-icon__item",{"embed-group-icon-disabled":!e.isSelected}]),onClick:function(t){return Pj(n).emit(Pj(iF).BIZ_EVENT_GROUP_ICON_CLICK,e)}},oP(e.groupName),11,pF)})),128))],4)],64)}}}),[["__scopeId","data-v-3d95fcfa"]]);var gF={"left-top":"background-position: left center","left-center":"background-position: left center","left-bottom":"background-position: left center","right-top":"background-position: right center","right-center":"background-position: right center","right-bottom ":"background-position: right center"};function mF(e,t,n,r,o,i,a){try{var u=e[i](a),c=u.value}catch(kK){return void n(kK)}u.done?t(c):Xy.resolve(c).then(r,o)}function yF(e){return function(){var t=this,n=arguments;return new Xy((function(r,o){var i=e.apply(t,n);function a(e){mF(i,r,o,a,u,"next",e)}function u(e){mF(i,r,o,a,u,"throw",e)}a(void 0)}))}}var bF={exports:{}},wF={exports:{}};!function(e){var t=Ms,n=_f;function r(o){return e.exports=r="function"==typeof t&&"symbol"==typeof n?function(e){return typeof e}:function(e){return e&&"function"==typeof t&&e.constructor===t&&e!==t.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,r(o)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(wF);var AF=wF.exports,_F=In,xF=Fo,SF=g([].reverse),CF=[1,2];_F({target:"Array",proto:!0,forced:String(CF)===String(CF.reverse())},{reverse:function(){return xF(this)&&(this.length=this.length),SF(this)}});var kF=Df("Array","reverse"),EF=ue,TF=kF,IF=Array.prototype,OF=function(e){var t=e.reverse;return e===IF||EF(IF,e)&&t===IF.reverse?TF:t};!function(e){var t=AF.default,n=wf,r=Ms,o=Rf,i=qf,a=Od,u=iv,c=Pf,l=$y,s=OF,f=Cv;function v(){e.exports=v=function(){return p},e.exports.__esModule=!0,e.exports.default=e.exports;var d,p={},h=Object.prototype,g=h.hasOwnProperty,m=n||function(e,t,n){e[t]=n.value},y="function"==typeof r?r:{},b=y.iterator||"@@iterator",w=y.asyncIterator||"@@asyncIterator",A=y.toStringTag||"@@toStringTag";function _(e,t,r){return n(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{_({},"")}catch(W){_=function(e,t,n){return e[t]=n}}function x(e,t,n,r){var i=t&&t.prototype instanceof O?t:O,a=o(i.prototype),u=new z(r||[]);return m(a,"_invoke",{value:F(e,n,u)}),a}function S(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}p.wrap=x;var C="suspendedStart",k="suspendedYield",E="executing",T="completed",I={};function O(){}function R(){}function P(){}var j={};_(j,b,(function(){return this}));var N=i&&i(i(H([])));N&&N!==h&&g.call(N,b)&&(j=N);var M=P.prototype=O.prototype=o(j);function D(e){var t;a(t=["next","throw","return"]).call(t,(function(t){_(e,t,(function(e){return this._invoke(t,e)}))}))}function B(e,n){function r(o,i,a,u){var c=S(e[o],e,i);if("throw"!==c.type){var l=c.arg,s=l.value;return s&&"object"==t(s)&&g.call(s,"__await")?n.resolve(s.__await).then((function(e){r("next",e,a,u)}),(function(e){r("throw",e,a,u)})):n.resolve(s).then((function(e){l.value=e,a(l)}),(function(e){return r("throw",e,a,u)}))}u(c.arg)}var o;m(this,"_invoke",{value:function(e,t){function i(){return new n((function(n,o){r(e,t,n,o)}))}return o=o?o.then(i,i):i()}})}function F(e,t,n){var r=C;return function(o,i){if(r===E)throw new Error("Generator is already running");if(r===T){if("throw"===o)throw i;return{value:d,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var u=L(a,n);if(u){if(u===I)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===C)throw r=T,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=E;var c=S(e,t,n);if("normal"===c.type){if(r=n.done?T:k,c.arg===I)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=T,n.method="throw",n.arg=c.arg)}}}function L(e,t){var n=t.method,r=e.iterator[n];if(r===d)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=d,L(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),I;var o=S(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,I;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=d),t.delegate=null,I):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,I)}function U(e){var t,n={tryLoc:e[0]};1 in e&&(n.catchLoc=e[1]),2 in e&&(n.finallyLoc=e[2],n.afterLoc=e[3]),u(t=this.tryEntries).call(t,n)}function V(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function z(e){this.tryEntries=[{tryLoc:"root"}],a(e).call(e,U,this),this.reset(!0)}function H(e){if(e||""===e){var n=e[b];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(g.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=d,t.done=!0,t};return o.next=o}}throw new TypeError(t(e)+" is not iterable")}return R.prototype=P,m(M,"constructor",{value:P,configurable:!0}),m(P,"constructor",{value:R,configurable:!0}),R.displayName=_(P,A,"GeneratorFunction"),p.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},p.mark=function(e){return c?c(e,P):(e.__proto__=P,_(e,A,"GeneratorFunction")),e.prototype=o(M),e},p.awrap=function(e){return{__await:e}},D(B.prototype),_(B.prototype,w,(function(){return this})),p.AsyncIterator=B,p.async=function(e,t,n,r,o){void 0===o&&(o=l);var i=new B(x(e,t,n,r),o);return p.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},D(M),_(M,A,"Generator"),_(M,b,(function(){return this})),_(M,"toString",(function(){return"[object Generator]"})),p.keys=function(e){var t=Object(e),n=[];for(var r in t)u(n).call(n,r);return s(n).call(n),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},p.values=H,z.prototype={constructor:z,reset:function(e){var t;if(this.prev=0,this.next=0,this.sent=this._sent=d,this.done=!1,this.delegate=null,this.method="next",this.arg=d,a(t=this.tryEntries).call(t,V),!e)for(var n in this)"t"===n.charAt(0)&&g.call(this,n)&&!isNaN(+f(n).call(n,1))&&(this[n]=d)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=d),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],i=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var a=g.call(o,"catchLoc"),u=g.call(o,"finallyLoc");if(a&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&g.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,I):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),I},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),V(n),I}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;V(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:H(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=d),I}},p}e.exports=v,e.exports.__esModule=!0,e.exports.default=e.exports}(bF);var RF=(0,bF.exports)(),PF=RF;try{regeneratorRuntime=RF}catch(EK){"object"==typeof globalThis?globalThis.regeneratorRuntime=RF:Function("r","regeneratorRuntime = r")(RF)}const jF=n(PF);function NF(){return(NF=yF(jF.mark((function e(t){return jF.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Jy((function(e,n){var r=new Image;r.src=t,r.onload=function(){return e(r)},r.onerror=n})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function MF(e){var t=Oj(0),n=Oj(0);return function(e){return NF.apply(this,arguments)}(e).then((function(e){t.value=e.width,n.value=e.height})).catch((function(e){})),{width:t,height:n}}const DF=lF(IN({__name:"index",setup:function(e){var t,n,r=tF(),o=r.styleConfig,i=r.siteConfig,a=oF().emitter,u=(o.value||{}).seekIcon,c=kD((function(){var e;return((null===(e=o.value.seekIcon)||void 0===e?void 0:e.hotIssues)||[]).length>0})),l=kD((function(){var e,t,n=o.value.components||{},r=o.value.noteBoard||{};return!(pC(e=n.comps||[]).call(e,(function(e){return e.activate})).length>0&&0===(null===(t=o.value.seekIcon)||void 0===t?void 0:t.iconType))&&(!("false"===i.value.online&&!r.webim.webimOffline&&!r.board.boardOffline)&&!!u.autoConsult)})),s=kD((function(){return i.value.unreadNum})),f={"left-top":"top:2px;left:2px;","left-center":"top:50%;left:2px;transform: translateY(-50%);","left-bottom":"bottom:2px;left:2px;","right-top":"top:2px;right:2px;","right-center":"top:50%;right:2px;transform: translateY(-50%);","right-bottom":"bottom:2px;right:2px;"},v=Oj(function(){var e="";if(u.isFixedPosition)e+="".concat(f[u.position],";");else{var t,n,r=XB(),o=r.width,i=r.height,a=u.marginLeft,c=u.marginTop,l=u.horizontalPosition||"left",s=u.portraitPosition||"top";if(a>o.value)e+="".concat("left"===l?"right":"left",":0;");else e+=yd(t="".concat(l,": ")).call(t,u.marginLeft,"px;");if(c>i.value)e+="".concat("top"===s?"bottom":"top",":0;");else e+=yd(n="".concat(s,": ")).call(n,u.marginTop,"px;")}return e}()),d=Oj(1);if(u.isCustomerStyle&&null!==(t=u.customerStyle)&&void 0!==t&&t.backImg&&(null===(n=u.customerStyle)||void 0===n||!n.iconHeight)){var p=MF(u.customerStyle.backImg),h=p.width,g=p.height;wN((function(){d.value=(h.value||1)/(g.value||1)}))}var m=kD((function(){var e="";if(u.isCustomerStyle){var t=u.customerStyle,n=t.backImg,r=t.iconWidth,o=t.iconHeight;o||(o=Math.ceil(r/d.value)),e+="background: url(".concat(n,") no-repeat;"),e+="background-size: 100%;",e+="width:".concat(r,"px;"),e+="height:".concat(o,"px;")}else{var i={width:44,height:98};+u.skinIndex>2?(e+="width:".concat(u.iconWidth||i.height,"px;"),e+="height:".concat(u.iconHeight||i.width,"px;")):(e+="width:".concat(u.iconWidth||i.width,"px;"),e+="height:".concat(u.iconHeight||i.height,"px;"))}return e+=gF[u.position]})),y=Oj(null),b=Oj(null),w=Oj(""),A=Oj("left"),_=Oj("top");return LN((function(){!function(){var e,t,n=document.body.offsetWidth,r=null===(e=y.value)||void 0===e?void 0:e.getOffsetWidth();w.value+="width:".concat(r,"px;");var o=(null===(t=b.value)||void 0===t?void 0:t.getBoundingClientRect())||{},i=o.top,a=o.left,u=o.width;i>40?w.value+="top:0;":(w.value+="bottom:-38px;",_.value="bottom"),a+u/2>n/2?(w.value+="right:0;",A.value="right"):w.value+="left:0;"}()})),function(e,t){return l.value?(qM(),YM("div",{key:0,ref_key:"icon",ref:b,class:"embed-icon",style:QR(v.value)},[EN(nD("span",{class:"embed-icon-unread-num"},oP(s.value),513),[[tB,s.value]]),Pj(u).iconType?(qM(),GM(hF,{key:0,rate:d.value},null,8,["rate"])):(qM(),YM("div",{key:1,class:$R(["embed-icon-default",!Pj(u).isCustomerStyle&&"embed-icon-pcIcon".concat(Pj(u).skinIndex)]),style:QR(m.value),onClick:t[0]||(t[0]=function(e){return Pj(a).emit(Pj(iF).BIZ_EVENT_ICON_CLICK)})},null,6)),c.value&&Pj(u).barrageSwitch?(qM(),GM(sF,{key:2,ref_key:"hotIssue",ref:y,"roll-dir":_.value,"issue-align":A.value,style:QR(w.value)},null,8,["roll-dir","issue-align","style"])):aD("",!0)],4)):aD("",!0)}}}),[["__scopeId","data-v-06873b41"]]);var BF=In,FF=ja.find,LF="find",UF=!0;LF in[]&&Array(1)[LF]((function(){UF=!1})),BF({target:"Array",proto:!0,forced:UF},{find:function(e){return FF(this,e,arguments.length>1?arguments[1]:void 0)}});var VF=Df("Array","find"),zF=ue,HF=VF,WF=Array.prototype;const qF=n((function(e){var t=e.find;return e===WF||zF(WF,e)&&t===WF.find?HF:t}));var QF=I,KF=i,ZF=g,YF=il,GF=Yr,$F=$,XF=ZF(j.f),JF=ZF([].push),eL=QF&&KF((function(){var e=Object.create(null);return e[2]=2,!XF(e,2)})),tL=function(e){return function(t){for(var n,r=$F(t),o=GF(r),i=eL&&null===YF(r),a=o.length,u=0,c=[];a>u;)n=o[u++],QF&&!(i?n in r:XF(r,n))||JF(c,e?[n,r[n]]:r[n]);return c}},nL={entries:tL(!0),values:tL(!1)},rL=nL.values;In({target:"Object",stat:!0},{values:function(e){return rL(e)}});const oL=n(te.Object.values);function iL(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}var aL=function(e){return dN("data-v-3175e1d3"),e=e(),pN(),e},uL=["src"],cL={class:"embed-image-viewer__btn embed-image-viewer__actions"},lL={class:"embed-image-viewer__actions__inner"},sL=["src"],fL=["src"],vL=aL((function(){return nD("i",{class:"embed-image-viewer__actions__divider"},null,-1)})),dL=["src"],pL=aL((function(){return nD("i",{class:"embed-image-viewer__actions__divider"},null,-1)})),hL=["src"],gL=["src"],mL={class:"embed-image-viewer__canvas"},yL=["src"];const bL=IN({__name:"ImgViewer",props:{urlList:{default:function(){return[]}},onClose:{type:Function,default:function(){}}},setup:function(e){var t=e,n={CONTAIN:{name:"contain",icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAAJ1BMVEUAAAD///////////////////////////////////////////////+uPUo5AAAADXRSTlMAIEC/3++vYBBvUDB/BHB7KgAAAExJREFUGNNjIBZwGhsbMDCwGRsfAHJElZQ2MDAwKSkFAjkiMCWOxHO6YBwFBioAdhijghznQPwDdAi3klYEyDRj4wQGBmZjswl4rAQAHooKLviD0QAAAAAASUVORK5CYII="},ORIGINAL:{name:"original",icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAAKlBMVEUAAAD///////////////////////////////////////////////////+Gu8ovAAAADnRSTlMAIL9A32Cgn5Aw708Qf0v1BUsAAABfSURBVBjTYyAaTDYGA0MQm3eREhioXAByTglAFDCuARIiMOWOmByODQwM3A1QDlsA5wTWAjiHEZnDwICLw7oBuwyjAND+CdgshYKFQMzUDPGChQPItU0QL2gcYCAHAADbzRc1GDywygAAAABJRU5ErkJggg=="}},r=Oj(null),o=Oj(0),i=Oj(!1),a=Oj(n.CONTAIN),u=Oj({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),c=kD((function(){return t.urlList[o.value]})),l=kD((function(){var e,t=u.value,n=t.scale,r=t.deg,o=t.offsetX,i=t.offsetY,c=t.enableTransition,l={transform:yd(e="scale(".concat(n,") rotate(")).call(e,r,"deg)"),transition:c?"transform .3s":"",marginLeft:"".concat(o,"px"),marginTop:"".concat(i,"px")};return"contain"===a.value.name&&(l.maxWidth=l.maxHeight="100%"),l})),s=null,f=null,v=/firefox/i.exec(window.navigator.userAgent)?"DOMMouseScroll":"wheel",d=function(){document.removeEventListener("keydown",s),document.removeEventListener(v,f),t.onClose&&t.onClose()},p=function(){if(!i.value){var e=db(n),t=oL(n),r=(xI(t).call(t,(function(e){return e.name===a.value.name}))+1)%e.length;a.value=n[e[r]],u.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}},h=function(e,t){if(!i.value){var n=function(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=iL(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=iL(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}({zoomRate:.2,rotateDeg:90,enableTransition:!0},t),r=n.zoomRate,o=n.rotateDeg,a=n.enableTransition;switch(e){case"zoomOut":u.value.scale>.2&&(u.value.scale=qO((u.value.scale-r).toFixed(3)));break;case"zoomIn":u.value.scale=qO((u.value.scale+r).toFixed(3));break;case"clocelise":u.value.deg+=o;break;case"anticlocelise":u.value.deg-=o}u.value.enableTransition=a}},g=function(){i.value=!1},m=function(e){i.value=!1,e.target.alt="加载失败"},y=function(e){if(!i.value&&0===e.button){var t,n,r,o=u.value,a=o.offsetX,c=o.offsetY,l=e.pageX,s=e.pageY,f=(t=function(e){u.value.offsetX=a+e.pageX-l,u.value.offsetY=c+e.pageY-s},n=!1,r=null,function(e){n?r=e:(n=!0,window.requestAnimationFrame((function(){t(r),n=!1,r=null})))}),v=function(e){return f(e)};document.addEventListener("mousemove",v),document.addEventListener("mouseup",(function e(){document.removeEventListener("mousemove",v),document.removeEventListener("mouseup",e)})),e.preventDefault()}};return _N(c,yF(jF.mark((function e(){var t,n;return jF.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Jj();case 2:(n=null===(t=r.value)||void 0===t?void 0:t.querySelector("img"))&&!n.complete&&(i.value=!0);case 4:case"end":return e.stop()}}),e)})))),LN((function(){var e;s=function(e){switch(e.keyCode){case 27:d();break;case 32:p();break;case 38:h("zoomIn");break;case 40:h("zoomOut")}},f=function(e){var t=e.wheelDelta?e.wheelDelta:-e.detail;h(t>0?"zoomIn":"zoomOut",{zoomRate:.015,enableTransition:!1})},document.addEventListener("keydown",s,!1),document.addEventListener(v,f,!1),null===(e=r.value)||void 0===e||e.focus()})),function(e,t){return qM(),YM("div",{ref_key:"wrapperRef",ref:r,tabindex:"-1",class:"embed-image-viewer__wrapper"},[nD("div",{class:"embed-image-viewer__mask",onClick:d}),nD("span",{class:"embed-image-viewer__btn embed-image-viewer__close",onClick:d},[nD("img",{class:"embed-icon-circle-close",src:Pj("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAANlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC3dmhyAAAAEXRSTlMAIN9w7xCvgEC/z6Bff5Awvt40kxUAAAEcSURBVDjLzZTdloMgDIQbCQHEn877v+yy6mZ6XGmPd82NiX7EDCE8vsZiqKMCOtokb7BcAJrlDiYN0xLW2BLnYL/oZdag0BQZSxqg4T83A8uOEa3AfMFdrA4k+WZiRJtO60UZnzPoqyJD7W3ZgsIgY5BuCxTcz4LU70JiSgHEW3h4kiJTRpdmXL4XIQPVFXfNvQY08ni4cFc67uWSdG6zFePhHUWQ3Djqhh4esD1IkuP3G+CNX49Y34nJLqZ+2h5zr/hLbnjiiZlYhPcr/bUwxFODPx6KGcZymZLGqjOjgqUHVtiNUbgxXG6pN66USbLKaVwW505XyjDLCzYr9LJyMQAWcmzQGooCz95s5obSnvnRN5lsv0hra+G32A9/yQ3onZCJeAAAAABJRU5ErkJggg==")},null,8,uL)]),nD("div",cL,[nD("div",lL,[nD("img",{class:"embed-icon-zoom-out",src:Pj("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAAOVBMVEUAAAD///////////////////////////////////////////////////////////////////////8KOjVvAAAAEnRSTlMA3yAwoO+/gG8QYF/PkEBQr09OIYjZAAAAl0lEQVQoz52QSRYDIQhEnVBxTu5/2HSbRsRl/4X4wAIs9R4Tq6vZnOnmocYUBzhZipAfXdB2ywfN7+xWMXrXN4109Vn0dWEJlMACTXZKQlNqnLoJzl7pOihi0TeeEqQQ+M8/pnMGbY+A0p4iFmfK+hZC2/25BOxP5/z3Hs0VZx77fRf+YtBluAE+L39Z1VM3h/MnNqsX/ADYQQNi3HeoYQAAAABJRU5ErkJggg=="),onClick:t[0]||(t[0]=function(e){return h("zoomOut")})},null,8,sL),nD("img",{class:"embed-icon-zoom-in",src:Pj("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAANlBMVEUAAAD////////////////////////////////////////////////////////////////////xY8b8AAAAEXRSTlMA3yAwX6AQ77+AT0DPkK9vcAvvNsgAAACaSURBVCjPnVBJEsMgDMPG7Fvy/892CkpZeosvYiQkg9T74VZMET7p5F1p0qozu9ScwBdIL3ygeU8vCoNHKtnn6JEDwoSfYUftIFxmF9SzpVyd5kTMtmf1aKDNRDeRBwHHEeXjQPnbwXilswMrPpIH4uFz8ncFLGntZxjQT5z8jTMUw6jfx61fGyhXU50X9Lu6okQ+mj9Hi3oxHxn2A26lyzZMAAAAAElFTkSuQmCC"),onClick:t[1]||(t[1]=function(e){return h("zoomIn")})},null,8,fL),vL,nD("img",{src:a.value.icon,onClick:p},null,8,dL),pL,nD("img",{class:"embed-icon-refresh-left",src:Pj("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAANlBMVEUAAAD////////////////////////////////////////////////////////////////////xY8b8AAAAEXRSTlMA32AQoIAgQL9QMO/PkK9vfz1eEAcAAACFSURBVCjPtZBLDsMgDETxF/NP73/ZqguIicSumdV4npEGhzdEGVN+ZAlDNqjcK1zqgcgoQj+nDNkDqLpeeyIf4zSHCLSAImJcU5NTvXIqXvMBME4TTwB3YHNx8A4KzRpAPkdbVprLFfRuDtftq2xbrPMgsv+2FU4xDSsYHlLs1luk8H99AVjdAtDJrl2YAAAAAElFTkSuQmCC"),onClick:t[2]||(t[2]=function(e){return h("anticlocelise")})},null,8,hL),nD("img",{class:"embed-icon-refresh-right",src:Pj("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAAPFBMVEUAAAD////////////////////////////////////////////////////////////////////////////YSWgTAAAAE3RSTlMA3yAQT4Bgv5/vz6+QQDCgcG+PKpquPAAAAIpJREFUKM+1kUsKwzAMRCPr62+S9v53LZTEslsCXTRazcwTxoOWOwabUPuOm0KynIDDYjjke+S3DSs0CJ5z6oYgOhBfKqZPB0Bdkoh0U/Xio4UvQKYfAZ9+lRloPcRmM4hndQQcc1F/9DHkAVrXCOw68bRl4SibynyMEo0q7Rq3zzJBsuZScfn/vABufwL7nlmSnAAAAABJRU5ErkJggg=="),onClick:t[3]||(t[3]=function(e){return h("clocelise")})},null,8,gL)])]),nD("div",mL,[(qM(!0),YM(LM,null,YN(e.urlList,(function(e){return qM(),YM("img",{key:e,class:"embed-image-viewer__img",src:c.value,style:QR(l.value),onLoad:g,onError:m,onMousedown:y},null,44,yL)})),128))])],512)}}}),wL=lF(bL,[["__scopeId","data-v-3175e1d3"]]);var AL={class:"embed-chat-tip-header"},_L={class:"embed-chat-tip-message"};const xL=lF(IN({__name:"MessageTips",props:{massageInfo:{},chatShow:{type:Boolean}},emits:["closeTip","show"],setup:function(e,t){var n=t.emit,r=e,o=tF(),i=o.siteConfig,a=o.styleConfig,u=kD((function(){return i.value.unreadNum})),c=kD((function(){var e;return"right-bottom"===((null===(e=a.value.webimConfig)||void 0===e?void 0:e.position)||"right-bottom")?{right:"20px",bottom:r.chatShow?"76px":"20px"}:{left:"20px",bottom:r.chatShow?"76px":"20px"}}));return function(e,t){return qM(),YM("div",{class:"embed-chat-tip",style:QR(c.value),onClick:t[1]||(t[1]=function(e){return n("show")})},[nD("div",AL,[nD("span",null,[iD(oP(r.massageInfo.nickName)+" ",1),EN(nD("span",{class:"embed-chat-tip-unread-num"},oP(u.value),513),[[tB,u.value]])]),nD("span",{class:"embed-chat-tip-close-btn",onClick:t[0]||(t[0]=eB((function(e){return n("closeTip")}),["stop"]))})]),nD("div",_L,oP(r.massageInfo.message),1)],4)}}}),[["__scopeId","data-v-2d74f0c2"]]);var SL;function CL(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function kL(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=CL(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=CL(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var EL=function(e){var t=function(e){var t=[];for(var n in e)if(e.hasOwnProperty(n)){var r,o=e[n];e[n]instanceof Object&&(o=ok(o)),"string"==typeof o&&(o=encodeURIComponent(o)),t.push(yd(r="".concat(n,"=")).call(r,o))}return t.push("stamp=".concat(Math.floor(1e4*Math.random()+500))),t.join("&")}(e.data||{}),n=e.cbKey||"cb",r=e.cbDataKey||"body";return new Jy((function(o,i){var a,u,c,l="jsonp_callback_".concat(Math.round(1e5*Math.random())),s=window;s[l]=function(e){delete s[l];var t=document.getElementById(l);t&&t.parentNode&&t.parentNode.removeChild(t),0===Number(e.status)||0===Number(e.code)?o(e[r]):i(e)};var f=document.createElement("script");f.id=l,f.src=yd(a=yd(u=yd(c="".concat(e.url,"?")).call(c,t,"&")).call(u,n,"=")).call(a,l),f.onerror=i,document.body.appendChild(f)}))},TL=(null===(SL=window.affImConfig)||void 0===SL||null===(SL=SL.siteConfig)||void 0===SL?void 0:SL.webRoot)||"";FI(TL).call(TL,"/")&&(TL=tp(TL).call(TL,0,-1));var IL={cps:TL,crm:"https://aifanfan.baidu.com/crm"},OL={cps:"cb",crm:"callback"},RL={cps:"body",crm:"data"};function PL(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cps",r=e.url;return r=yd(t="".concat(IL[n])).call(t,e.url),EL(kL(kL({},e),{},{url:r,cbKey:OL[n]||"cb",cbDataKey:RL[n]||"body"}))}var jL={class:"embed-digital-man"},NL={class:"embed-digital-man-img-wrapper"},ML=["src"],DL=["src"],BL=["onClick"],FL={class:"embed-digital-man-text"};const LL=lF(IN({__name:"DigitalMan",setup:function(e){var t=tF(),n=t.siteConfig,r=t.styleConfig,o=Oj(0),i=Oj(0),a=Oj(""),u=Oj(null),c=null,l={},s=r.value.digitalHumanSetting;_N(a,(function(e){"number"==typeof c&&(clearTimeout(c),c=null),e&&(c=Gx((function(){a.value="",c=null}),3e3))})),oF({name:iF.BIZ_EVENT_CHAT_IFRAME_LOADED,callback:function(){a.value="很高兴为您服务"}});var f=kD((function(){var e,t,n=s.data;return{0:(null==n||null===(e=n.welcome)||void 0===e?void 0:e.gif)||"",1:(null==n||null===(t=n.contact)||void 0===t?void 0:t.gif)||""}}));function v(){0===o.value?(o.value=1,a.value="我可以跟您说话啦～"):(o.value=0,i.value=0,a.value="我会保持安静哒～",u.value&&(u.value.pause(),u.value.src="",u.value.load())),window.sensors&&window.sensors.track("im_digitalman_audio_click",{digitalManStatus:o.value,ctype:0});var e=new Audio("https://aifanfan.baidu.com/chat/static/voice/msg.wav");e.muted=!0,e.play()}return window.addEventListener("message",(function(e){var t=e.data;if("chatBox"===t.target&&"sendMessage"===t.event)try{var r=t.msg;!function(e){var t=e.content,r=e.reason||"";t&&1===o.value&&1!==e.msgOwner&&"offlineChatFormText"!==r&&"offlineChatForm"!==r&&"onlineFormAuto"!==r&&"file"!==t.type&&"voice"!==t.type&&11!==e.msgType&&(l=e,PL({url:"/chat/GenSpeechSynthesisAuth",data:{bid:n.value.bid,siteId:n.value.siteId,org_id:n.value.eid,user_id:n.value.userId,per:s.perid||"6567",session_id:e.sessionid||e.sid||"",msg_id:e.msgid||""}}).then((function(e){l.audioUrl=(null==e?void 0:e.url)||"",u.value&&(u.value.src=l.audioUrl,u.value.load(),u.value.play().catch((function(e){u.value&&u.value.play()}))),i.value=1})).catch((function(e){i.value=0})))}(JSON.parse(r).message)}catch(kK){}"chatBox"===t.target&&"leaveContactWay"===t.event&&(a.value="谢谢您的留联，祝您生活愉快哦~")})),function(e,t){return qM(),YM("div",jL,[nD("div",NL,[EN(nD("img",{src:f.value[0],class:"embed-digital-man-img"},null,8,ML),[[tB,0===i.value]]),EN(nD("img",{src:f.value[1],class:"embed-digital-man-img"},null,8,DL),[[tB,1===i.value]])]),nD("div",{class:$R(["embed-digital-man-btn",{"embed-digital-man-btn-play":1===o.value,"embed-digital-man-btn-stop":0===o.value}]),onClick:eB(v,["stop"])},null,10,BL),nD("div",FL,oP(a.value),1),nD("audio",{ref_key:"digitalManMsgPlayer",ref:u,style:{display:"none"},class:"embed-audio",onEnded:t[0]||(t[0]=function(e){return i.value=0})},null,544)])}}}),[["__scopeId","data-v-c97e24c7"]]);var UL="im_sdk_event_force_talk",VL="im_sdk_event_invite",zL="im_sdk_event_visiter_statu_change",HL="im_sdk_event_block",WL={exports:{}};WL.exports=function(){function e(){var e;if(document.currentScript)e=document.currentScript;else{var t=document.getElementsByTagName("script"),n=t[t.length-1];if(n.getAttribute("data-bdms-faccdee21b68")||n.getAttribute("data-app"))e=n;else for(var r=0;r<t.length;r++){var o=t[r].getAttribute("src");if(o&&-1<hk(o).call(o,"xaf.js")){e=t[r];break}}}var i,a,u="",c="";return e&&e.getAttribute&&(u=(i=e,a={dataApp:"",dataAppProp:""},i.getAttribute("data-bdms-faccdee21b68")?(a.dataApp=i.getAttribute("data-bdms-faccdee21b68"),a.dataAppProp="data-bdms-faccdee21b68"):i.getAttribute("data-app")?(a.dataApp=i.getAttribute("data-app"),a.dataAppProp="data-app"):i.getAttribute("app")&&(a.dataApp=i.getAttribute("app"),a.dataAppProp="app"),n=a).dataApp,c=n.dataAppProp),{dataApp:u,dataAppProp:c}}function t(e){for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n),o=n%32;t+=r<=122&&41<=r?122<r+o?String.fromCharCode(40+r-122+o):String.fromCharCode(r+o):String.fromCharCode(e.charCodeAt(n))}return t}function n(e){if(void 0!==e){if(e<0)throw new Error("timeout最小值为0");if(6e5<e)throw new Error("timeout最大值为600000")}}var r=function(){try{if(!window.localStorage)return!1;try{return window.localStorage.setItem("test","testValue"),window.localStorage.removeItem("test"),!0}catch(e){return!1}}catch(e){return!1}}(),o={keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t,n,r,i,a,u,c="",l=0;for(e=o.utf8Encode(e);l<e.length;)r=(u=e.charCodeAt(l++))>>2,i=(3&u)<<4|(t=e.charCodeAt(l++))>>4,a=(15&t)<<2|(n=e.charCodeAt(l++))>>6,u=63&n,isNaN(t)?a=u=64:isNaN(n)&&(u=64),c=c+this.keyStr.charAt(r)+this.keyStr.charAt(i)+this.keyStr.charAt(a)+this.keyStr.charAt(u);return c},decode:function(e){var t,n,r,i,a,u,c="",l=0;for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");l<e.length;){var s,f,v,d;r=hk(s=this.keyStr).call(s,e.charAt(l++)),t=(15&(i=hk(f=this.keyStr).call(f,e.charAt(l++))))<<4|(a=hk(v=this.keyStr).call(v,e.charAt(l++)))>>2,n=(3&a)<<6|(u=hk(d=this.keyStr).call(d,e.charAt(l++))),c+=String.fromCharCode(r<<2|i>>4),64!==a&&(c+=String.fromCharCode(t)),64!==u&&(c+=String.fromCharCode(n))}return o.utf8Decode(c)},utf8Encode:function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):(127<r&&r<2048?t+=String.fromCharCode(r>>6|192):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128)),t+=String.fromCharCode(63&r|128))}return t},utf8Decode:function(e){for(var t,n,r="",o=0,i=0;o<e.length;)(t=e.charCodeAt(o))<128?(r+=String.fromCharCode(t),o++):191<t&&t<224?(i=e.charCodeAt(o+1),r+=String.fromCharCode((31&t)<<6|63&i),o+=2):(i=e.charCodeAt(o+1),n=e.charCodeAt(o+2),r+=String.fromCharCode((15&t)<<12|(63&i)<<6|63&n),o+=3);return r}},i="https://sofire.baidu.com/h5/r",a={status:""},u=null,c=null,l=!1,s=!1,f=null,v=null,d={dataApp:"",dataAppProp:""},p=(new Date).getTime(),h="",g="https://safe.cdn.bcebos.com/",m=g+"js/dfxaf3.js",y=g,b=[],w=[],A=[],_={report:function(e){n((e=e||{}).timeout),"loading"===a.status?A.push(e):"error"===a.status?I(e):window.xaf.coreDfXaf?window.xaf.report(e):I(e,"dfxaf3.js error")},hgzAs:function(e,t){u=e,c=t,"success"===a.status&&window.xaf.coreDfXaf?window.xaf.hgzAs(e,t):function(e,t){if(n=e,r=f&&!f.aid&&!n.aid,n=!f&&!n.aid,r||n)throw"aid缺失";var n,r;"loading"===a.status?s=!0:t&&"function"==typeof t&&t("")}(e,t)},reportV3:function(e){n((e=e||{}).timeout);try{return window.xaf.coreDfXaf?window.xaf.reportV3(e):new Jy((function(t){"loading"===a.status?O(t,"dfxaf3.js loading",e):"error"===a.status?O(t,null,e):O(t,"dfxaf3.js error",e)}))}catch(t){}},hgzAsV3:function(e){try{return window.xaf.coreDfXaf?window.xaf.hgzAsV3(e):new Jy((function(e){e("")}))}catch(t){}},getData:function(){return window.xaf.coreDfXaf?window.xaf.getData():{Token:"",view:""}},init:function(e,t){window.xaf.coreDfXaf?window.xaf.init(e,t):"loading"===a.status&&(l=!0,f=e,v=t)},onReady:function(e){"function"==typeof e&&b.push(e)},onReadyFail:function(e){"function"==typeof e&&w.push(e)}};function x(e){var t,n,o,i=(t="dfxafjs",(t=document.cookie.match("(^|;) ?"+t+"=([^;]*)(;|$)"))?t[2]:null),g=i?y+i:m;h=i||"",r&&localStorage.setItem("__g",h),e=(g={url:g,addScriptCallback:e,isGray:h}).url,n=g.isGray,(g=document.createElement("script")).src=e,d.dataAppProp&&g.setAttribute(d.dataAppProp,d.dataApp),g.onerror=function(e){S({r:"fi",time:(new Date).getTime()-p,fr:e+""||"Faild to load"}),a={status:"error"},s&&"function"==typeof c&&c(""),Rd(A).call(A,(function(e){I(e)})),Rd(w).call(w,(function(t){t(e)}))},g.onload=function(){n&&S({r:"lo",time:(new Date).getTime()-p,fr:""}),a={status:"success"},_=window.xaf,l&&window.xaf.init(f,v),s&&window.xaf.hgzAs(u,c),Rd(A).call(A,(function(e){window.xaf.report(e)})),Rd(b).call(b,(function(e){e(window.xaf)}))},g=e=g,(e=document.getElementsByTagName("script")[0])&&e.parentNode?e.parentNode.insertBefore(g,e):(e=document.body||document.head)&&e.appendChild(g),a={status:"loading"},o!==m&&(document.cookie="dfxafjs="+o+";expires="+function(e){var t=new Date;return t.setTime(e),t.toUTCString()}((new Date).getTime()))}function S(e){var t=e.r,n=e.time,r=e.fr,o=e.e,a=new Image,u=encodeURIComponent;e=navigator.connection?navigator.connection.effectiveType||navigator.connection.type||"":"wifi",a.src=i+"/1234"+"?n=".concat(u(e))+"&u=".concat(u(location.href))+"&ts=".concat(p)+"&tt=".concat((new Date).getTime())+"&ti=".concat(n||0)+"&v=".concat(h)+"&r=".concat(t||"")+"&fr=".concat(u(r||""))+"&em=".concat(u(o&&o.message||""))+"&ef=".concat(u(o&&o.filename||""))+"&el=".concat(o&&o.lineno||"")+"&ec=".concat(o&&o.colno||"")}function C(){return"0x1x"+Math.floor((Math.random()+Math.floor(9*Math.random()+1))*Math.pow(10,9))}var k="31$";function E(e,n){var i,a,u=yd(i=yd(a="".concat((r?localStorage.getItem("__bid_n"):"")||C(),"|")).call(a,location.href,"|")).call(i,(new Date).getTime());return u+="|"+e,u={i:"0",tn:e=(new Date).getTime()+"",tj:e,tp:p+"",to:n.timeout+"",v:"s3.4.9",j:u},u=t(ok(u)),k+"CODED--v30"+o.encode(u)}function T(e){if(!r)return null;var n=e.timeout,i=VT(r?localStorage.getItem("__tc")||"1":"0");if(5<=i)return null;var a=VT(r&&localStorage.getItem("__tu")||"0");return 288e5<(new Date).getTime()-a?null:(e=i+1+"",localStorage.setItem("__tc",e),i=localStorage.getItem("__t"),i={i:e,tn:(new Date).getTime()+"",tj:a+"",tp:p+"",to:n+"",v:"s3.4.9",j:i},o.encode(t(ok(i))))}function I(e,t){var n=e.complete,r=(e=T({timeout:r="number"==typeof e.timeout?e.timeout:5e3}))?k+e:E(t||"dfxaf3 failed to load",{timeout:r});n&&n({code:0,msg:"",jt:r})}function O(e,t,n){var r=n.timeout||5e3;e({code:0,msg:"",jt:(n=T({timeout:r}))?k+n:E(t||"dfxaf3 failed to load",{timeout:r})})}return g=e(),d=g,x(),window.xaf=_}();const qL=n(WL.exports);const QL=n(Pf);const KL=n(qf);!function(){function e(){var t;return e=jO?ik(t=jO).call(t):function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.apply(this,arguments)}function t(e,t){e.prototype=sb(t.prototype),e.prototype.constructor=e,r(e,t)}function n(e){var t;return(n=QL?ik(t=KL).call(t):function(e){return e.__proto__||KL(e)})(e)}function r(e,t){var n;return(r=QL?ik(n=QL).call(n):function(e,t){return e.__proto__=t,e})(e,t)}function o(e,t,n){var i;return(o=function(){if("undefined"==typeof Reflect||!Do)return!1;if(Do.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Do(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?ik(i=Do).call(i):function(e,t,n){var o=[null];o.push.apply(o,t);var i=new(ik(Function).apply(e,o));return n&&r(i,n.prototype),i}).apply(null,arguments)}function i(e){var t="function"==typeof DE?new DE:void 0;return i=function(e){var i,a;if(null===e||(a=e,-1===hk(i=Function.toString.call(a)).call(i,"[native code]")))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,u)}function u(){return o(e,arguments,n(this).constructor)}return u.prototype=sb(e.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),r(u,e)},i(e)}function a(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var u=function(e){function n(t,n){var r;return(r=e.call(this,n)||this).code=t,r}return t(n,e),n.create=function(e,t){return t instanceof n?(t.code=e,t):t instanceof Error?new n(e,t.message):new n(e,t)},n}(i(Error));function c(e,t,n){return void 0===e[t]?n:e[t]}function l(e,t){return function(n,r){var o=!1;function i(e,t){o||(o=!0,r(e,t))}Gx((function(){var e=new Error("timeout.");t&&(e=u.create(t,e)),i(e)}),e),n(i)}}function s(){return(new Date).getTime()}function f(e){var t=[],n=!1;return function(){var r=this,o=tp(Array.prototype).apply(arguments),i=o.length,a=o[i-1];o[i-1]=function(){a.apply(this,arguments),n=!1,function(){if(t.length){var e=t.shift();n=!0,e()}}()},n?t.push((function(){return e.apply(r,o)})):(n=!0,e.apply(this,o))}}var v="auto-report",d="active-report",p="acs-get-sign",h=[551,552,553],g="3.1.4",m=!1;var y=function(){function e(e,t){this.sid=e,this.group=t}var t=e.prototype;return t.log=function(e,t){(m||t)&&function(e){var t=e.url,n=e.data,r=e.success,o=e.error,i=e.headers,a=void 0===i?{}:i,u=e.withCredentials,c=window,l=c.XDomainRequest,s=c.XMLHttpRequest;if(void 0===u&&(u=!0),s){var f=new s;for(var v in f.open("POST",t,!0),a)f.setRequestHeader(v,a[v]);u&&(f.withCredentials=!0),f.onreadystatechange=function(e){if(4===f.readyState&&200===f.status){var t=f.responseText;r&&r(t,f)}},f.onerror=function(e){o&&o(e)},f.send(n)}else{var d=new l;d.open("post",t,!0),u&&(d.withCredentials=!0),d.onerror=function(e){o&&o(e)},d.onload=function(){var e=d.responseText;r&&r(e)},d.send(n)}}({url:"https://miao.baidu.com/sdk_log",data:ok(e)})},t.error=function(e,t,n){var r=this.sid,o=this.group,i=n.code||600,a=hk(h).call(h,i)>-1,u={sid:r,group:o,type:e,total:t,status:1,error:n.message+"\n"+n.stack,extra:{errorCode:i,version:g}};this.log(u,a)},t.success=function(e,t){var n={sid:this.sid,group:this.group,type:e,total:t,status:0,error:"",extra:{version:g}};this.log(n)},e}(),b=function(){function e(e,t,n){this.loader=null,this.monitor=null,this.target=null,this.loader=n,this.monitor=new y(e,t)}return e.prototype.getTarget=function(e){var t=this,n=this.loader;if(this.target)return e(null,this.target);n.getTarget((function(n,r){r&&(t.target=r),e(n,r)}))},e}();function w(e){(void 0===e&&(e=""),e)&&(e=""+e+(hk(e).call(e,"?")>0?"&":"?")+"_o="+encodeURIComponent(location.protocol+"//"+location.host));return e}var A=function(e){function n(t,n){var r;return(r=e.call(this,t,"abclite",n)||this).state=1,r.evalData=null,r.autoResponse="",r.autoError=null,r.reportCnt=0,r.autoResponseQueue=[],r.error=null,r}t(n,e);var r=n.prototype;return r.handleTargetError=function(e){this.state=2,this.autoError=e,this.evalAutoQueue(e)},r.autoReportInit=function(){var e=this,t=this.loader.opts;this.getTarget((function(n,r){if(n)e.handleTargetError(n);else{var o=r;e.state=3;try{o.initData(t,(function(t){e.evalData=t,e.autoReport()}))}catch(i){e.state=4,i=u.create(551,i),e.autoError=i,e.evalAutoQueue(i)}}}))},r.autoReport=function(e){var t=this,n=this.loader.opts,r=n.reportTimeout,o=n.mainReportUrl;this.getTarget((function(n,i){if(n)t.handleTargetError(n);else{t.reportCnt++;var a=i;t.state=5,l(r,561)((function(n){e=e||w(o),a.report({url:e,data:t.evalData,success:function(e){n(null,e)},error:function(e){e=u.create(571,e),n(e)}})}),(function(e,n){if(e)return t.state=6,t.autoError=e,void t.evalAutoQueue(e,null);t.state=7,t.autoResponse=n,t.autoError=null,t.evalAutoQueue(null,n)}))}}))},r.pushAutoQueue=function(e){this.autoResponseQueue.push(e)},r.evalAutoQueue=function(){for(;this.autoResponseQueue.length;){if(2===this.autoResponseQueue.shift().apply(this,arguments))break}},r.getAutoResponse=function(e){var t=this,n=this.state,r=this.loader,o=this.monitor,i=s(),a=r.opts,u=a.mainReportRetryCnt,c=a.secondReportRetryCnt,l=a.secondReportUrl,f=function n(r,a){if(6===t.state){if(t.reportCnt-1<u)return t.autoReport(),t.pushAutoQueue(n),2;if(t.reportCnt-1-u<c+1)return t.autoReport(w(l)),t.pushAutoQueue(n),2}try{!function(t,n){var r=s()-i;t?o.error(v,r,t):o.success(v,r);try{e(t,n)}catch(a){}}(r,a)}finally{return 1}};1===n||3===n||5===n?this.pushAutoQueue(f):f(this.error,this.autoResponse)},r.activeReport=function(e,t,n,r){var o=this;void 0===n&&(n=0);var i=this.loader.opts,a=i.mainReportRetryCnt,c=i.reportTimeout,s=i.mainReportUrl,f=i.secondReportRetryCnt;n++,this.getTarget((function(v,d){if(v)return t&&t(v);var p=d;l(c,562)((function(t){r=r||w(s),p.report({url:r,data:e,success:function(e){t(null,e)},error:function(e){e=u.create(570,e),t(e)}})}),(function(r,u){if(r)return n-1<a?void o.activeReport(e,t,n):n-1-a<f+1?void o.activeReport(e,t,n,w(i.secondReportUrl)):t&&t(r);t&&t(null,u)}))}))},r.getActiveResponse=function(e,t){var n=this,r=this.state,o=this.monitor,i=e.timeout,a=s(),c=!1;function l(e,n){if(!c){c=!0;var r=s()-a;e?o.error(d,r,e):o.success(d,r);try{t(e,n)}catch(i){}}}"number"==typeof i&&i>0&&Gx((function(){l(u.create(563,"active timeout"))}),i);var f=function(){n.getTarget((function(t,r){if(t)return l(t);try{r.initActiveData(e,(function(e){n.activeReport(e,l)}))}catch(o){l(o=u.create(552,o))}}))};1===r?this.pushAutoQueue(f):f()},n}(b);function _(e,t){e.onload=function(){this.onerror=this.onload=null,t(null,e)},e.onerror=function(){this.onerror=this.onload=null,t(new Error("Failed to load "+this.src),e)}}function x(e,t){e.onreadystatechange=function(){"complete"!=this.readyState&&"loaded"!=this.readyState||(this.onreadystatechange=null,t(null,e))}}var S=function(){function e(e){var t=this;this.state=0,this.queue=[],this.error=null,this.opts={},this.loadCnt=0,this.opts=e;var n=e.preload;n&&"function"==typeof n?n((function(e){e?t.doLoad():(t.state=2,t.evalQueue())})):this.doLoad()}var t=e.prototype;return t.doLoad=function(){var e=this;this.error=null,this.loadCnt++,this.state=1;var t=this.opts,n=t.timeout,r=t.src;l(n)((function(t){!function(e,t,n){var r,o,i,a,u=!1,l=c(t,"timeout",5e3),s=c(t,"clientCacheTTL",120),f=e;function v(e,t){u||(u=!0,r&&clearTimeout(r),n(e,t))}if(!e)return v(new Error("Load script miss src"));s&&(o=f,i=s,a="_="+VT(+new Date/(6e4*i),10),f=hk(o).call(o,"?")>0?o+"&"+a:o+"?"+a),l&&(r=Gx((function(){v(new Error("Load "+e+" timeout"),null)}),l)),function(e,t,n){var r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("script");"function"==typeof t&&(n=t,t={}),t=t||{},n=n||function(){},o.type=t.type||"text/javascript",o.charset=t.charset||"utf8",o.async=!("async"in t)||!!t.async,o.src=e,t.attrs&&function(e,t){for(var n in t)e.setAttribute(n,t[n])}(o,t.attrs),t.text&&(o.text=""+t.text),("onload"in o?_:x)(o,n),o.onload||_(o,n),r.appendChild(o)}(f,v)}(r,e.opts,t)}),(function(t){t?(e.error=t,e.state=3):e.state=2,e.evalQueue(t)}))},t.pushQueue=function(e){this.queue.push(e)},t.evalQueue=function(){for(;this.queue.length;){if(2===this.queue.shift().apply(this,arguments))break}},t.getTarget=function(e){var t=this,n=this.state,r=this.opts,o=this.targetKey,i=function n(i){if(i){var a=r.retryCnt;if(t.loadCnt-1<a)return t.doLoad(),t.pushQueue(n),2}try{var c;if("function"==typeof o)try{c=o.call(window)}catch(l){}else c=window[o];if(c)return e(null,c);i=u.create(400,"targetKey: "+o+" not found"),e(i,c)}catch(l){}return 1};0===n||1===n?this.pushQueue(i):i(this.error)},e}(),C="__abbaidu_",k=function(e){function n(t){var n;(n=e.call(this,t)||this).targetKey=null,n.instance=null;var r=t.sid;return n.targetKey="BCat_"+r,window[""+C+r+"_advanced"]=!0,window[""+C+r+"_paris"]=!0,n.instance=new A(r,a(n)),n.init(),n}return t(n,e),n.prototype.init=function(){this.instance.autoReportInit()},n}(S),E=function(e){function n(t,n){var r;return(r=e.call(this,t,"acs",n)||this).state=1,r}t(n,e);var r=n.prototype;return r.getSign=function(e,t){void 0===t&&(t="");var n=this.monitor,r=s(),o=function(t,o){var i=s()-r;t?n.error(p,i,t):n.success(p,i),e(t,o)};this.getTarget((function(e,n){if(e)return o(e);try{n.gs((function(e,t){o(t,e)}),t)}catch(r){r=u.create(553,r),o(r)}}))},r.getApiObfuscator=function(e){this.getTarget((function(t,n){if(t)return e(t,null,null);e(null,n.gst(),n.dpt)}))},r.configDkp=function(e,t){this.getTarget((function(n,r){return n?t(n):r.configDkp?void t(null,r.configDkp(e)):t(new Error("请通知管理员开启configDkp功能"))}))},n}(b),T=function(e){function n(t){var n;(n=e.call(this,t)||this).targetKey=null,n.instance=null;var r=t.sid;return n.targetKey="$BSB_"+r,n.instance=new E(r,a(n)),n}return t(n,e),n}(S),I=function(e){function n(t,n){var r;return(r=e.call(this,t,"banti",n)||this).autoResponse="",r.autoError=null,r.initSending=!1,r.initSendFinished=!1,r.autoResponseQueue=[],r}t(n,e);var r=n.prototype;return r.handleTargetError=function(e){this.autoError=e,this.evalAutoQueue(e,null)},r.sendReportInit=function(){var e=this;this.getTarget((function(t,n){if(t)e.handleTargetError(t);else{var r=n;e.initSending=!0;try{r.initSend((function(t,n){if(e.initSendFinished=!0,t)return e.handleTargetError(t);e.autoResponse=n,e.evalAutoQueue(null,n)}))}catch(o){e.handleTargetError(o)}}}))},r.pushAutoQueue=function(e){this.autoResponseQueue.push(e)},r.evalAutoQueue=function(){for(;this.autoResponseQueue.length;){this.autoResponseQueue.shift().apply(this,arguments)}},r.getInitResponse=function(e){this.initSendFinished?e(this.autoError,this.autoResponse):this.pushAutoQueue(e)},r.send=function(e,t){this.getTarget((function(n,r){if(n)return t&&t(n);r.send(e,(function(e,n){e?t(e,null):t(null,n)}))}))},r.hgzAs=function(e){this.getTarget((function(t,n){if(t)return e(t);var r=n;try{r.hgzAs((function(t,n){e(n,t)}))}catch(o){e(o,"")}}))},n}(b),O=function(n){function r(t){var r;(r=n.call(this,t)||this).targetKey=null,r.instance=null;var o=t.sak,i=t.bantiOptions,u=void 0===i?{}:i;return r.targetKey=function(){if(!window.Banti)return null;var t=e({sak:o},u,{autoInit:!1});return window.Banti.create(t)},r.instance=new I(o,a(r)),!1!==u.autoInit&&r.init(),r}return t(r,n),r.prototype.init=function(){this.instance.sendReportInit()},r}(S),R=function(t){var n,r=!1,o=[];function i(){for(;o.length;)o.shift().call(null,n)}return function(a,u,l,s){var f=e({},l,{src:a,retryCnt:c(s,"mainCDNRetryCnt",1),mainReportRetryCnt:c(s,"mainReportRetryCnt",1),secondReportRetryCnt:c(s,"secondReportRetryCnt",0)});return n=new t(f),u?n.getTarget((function(o){if(r=!0,o){var a=e({},l,{src:u,retryCnt:c(s,"secondCDNRetryCnt",0),mainReportRetryCnt:c(s,"mainReportRetryCnt",1),secondReportRetryCnt:c(s,"secondReportRetryCnt",0)});n=new t(a)}i()})):(r=!0,i()),function(e){var t;r?e(n):(t=e,o.push(t))}}},P=R(k),j=R(T),N=R(O);function M(e){var t=e.disasterConfig,n=void 0===t?{}:t;return{sak:c(e,"sak",null),bantiOptions:c(e,"bantiOptions",{}),sid:c(e,"sid",null),timeout:c(e,"timeout",5e3),clientCacheTTL:c(e,"clientCacheTTL",120),mainReportUrl:c(n,"mainReportUrl"),secondReportUrl:c(n,"secondReportUrl"),reportTimeout:c(n,"reportTimeout",5e3)}}function D(e,t){return function(n,r){1===arguments.length&&(r=n,n={isWait:!1});var o=c(n,"isWait",!1),i=!1,a=!1,u=!1,l=null,s=function(){if(!i&&a)return o?void(u&&(i=!0,r(l))):(i=!0,void r(l))};try{e(n,(function(e,t){u=!0,s()}))}catch(f){if(null==f||!f.combinedInvokeError)throw f;u||(u=!0,s())}t.getAcsToken((function(e){a=!0,l=e,s()}))}}function B(t){var n,r=t.sid,o=t.sak;if(!o&&!r)throw new Error("Missing param `sid` or `sak`");n=c(t,"monitoring",!1),m=n;var i={sid:r,sak:o};return function(t){var n;t.initBanti=function(r){var o=r.bantiUrl,i=r.disasterConfig,a=void 0===i?{}:i,u=r.bantiOptions,l=void 0===u?{}:u,s=r.sak,f=r.preloadBantiUrl;if(o&&s){t._bantiInited=!0;var v=M(r),d=c(a,"bantiUrl");v.preload=f,n=N(o,d,e({},v,l),a)}},t.bantiInitSend=function(e){if(e=e||function(){},!n){var t=new Error("Missing param `bantiUrl` during initialization");throw t.combinedInvokeError=!0,t}n((function(t){t.getTarget((function(n){return n?e(n):t.instance.initSending?e(null):(t.instance.sendReportInit(),void e(null))}))}))},t.getBantiInstance=function(e){if(!n){var t=new Error("Missing param `bantiUrl` during initialization");throw t.combinedInvokeError=!0,t}n((function(t){t.getTarget((function(n){if(n)return e(n,null);e(null,t.instance)}))}))},t.getBantiInitResponse=function(e){t.getBantiInstance((function(t,n){t?e(t,n):n.getInitResponse(e)}))};var r=null,o="";t.sendBantiReport=function(e,n){1===arguments.length&&(n=e),t.getBantiInstance((function(t,i){if(t)return r=t,void n(t,i);i.send(e,(function(e,t){r=e,o=t,n(e,t)}))}))},t.hgzAs=function(e){t.getBantiInstance((function(t,n){if(t)return e(t,"");n.hgzAs(e)}))},t.getPrevSendBantiReportResponse=function(){return o},t.getPrevSendBantiReportError=function(){return r}}(i),function(t){var n;t.initAbclite=function(r){var o=r.abcliteUrl,i=r.disasterConfig,a=void 0===i?{}:i,u=r.abcliteFields,l=void 0===u?{}:u;if(o){t._abcliteInited=!0;var s=M(r),f=c(a,"abcliteUrl");n=P(o,f,e({},s,{subid:c(l,"subid"),extraData:c(l,"extraData")}),a)}},t.getAbcliteInstance=function(e){if(!n){var t=new Error("Missing param `abcliteUrl` during initialization");throw t.combinedInvokeError=!0,t}n((function(t){t.getTarget((function(n){if(n)return e(n,null);e(null,t.instance)}))}))},t.getAbcliteAutoResponse=function(e){t.getAbcliteInstance((function(t,n){t?e(t,n):n.getAutoResponse((function(t,n){e(t,n)}))}))};var r=null,o="";t.getAbcliteActiveResponse=function(e,n){1===arguments.length&&(n=e),t.getAbcliteInstance((function(t,i){if(t)return r=t,void n(t,i);i.getActiveResponse(e,(function(e,t){r=e,o=t,n(e,t)}))}))},t.getPrevAbcliteActiveResponse=function(){return o},t.getPrevAbcliteActiveError=function(){return r}}(i),function(e){var t;e.initAcs=function(n){var r=n.disasterConfig,o=void 0===r?{}:r,i=n.acsUrl,a=n.preloadAcsUrl;if(i){e._acsInited=!0;var u=M(n),l=c(o,"acsUrl");u.preload=a,t=j(i,l,u,o)}},e.getAcsInstance=function(e){if(!t)throw new Error("Missing param `acsUrl` during initialization");t((function(t){t.getTarget((function(n){if(n)return e(n,null);var r=t.instance;e(null,r)}))}))};var n=null;e.getAcsToken=function(t,r){1===arguments.length&&(r=t,t=""),e.getAcsInstance((function(e,o){if(e)return n=e,void r(e.code||600);o.getSign((function(e,t){if(e)return n=e,void r(e.code||600);n=null,r(t)}),t)}))},e.getApiObfuscator=function(t){e.getAcsInstance((function(e,n){e?t(e,null,null):n.getApiObfuscator(t)}))},e.getPrevAcsTokenError=function(){return n}}(i),function(e){e.getAcsTokenWithAbcliteActiveReport=f(D((function(t,n){e.getAbcliteActiveResponse(t,n)}),e)),e.getAcsTokenWithBantiSend=f(D((function(t,n){e.sendBantiReport(t,n)}),e))}(i),i.initBanti(t),i.initAbclite(t),i.initAcs(t),i}var F="3.1.4",L={create:function(e){var t,n=e.sid,r=e.abcliteUrl,o=e.acsUrl,i=e.sak,a=e.bantiUrl,u=(window.PARIS_INSTANCE_CACHE||(window.PARIS_INSTANCE_CACHE={}),window.PARIS_INSTANCE_CACHE);return u[i]||u[n]?(t=u[n],n&&r&&!t._abcliteInited&&(t.sid=n,t.initAbclite(e)),n&&o&&!t._acsInited&&(t.sid=n,t.initAcs(e)),i&&a&&!t._bantiInited&&(t.sak=i,t.initBanti(e))):t=B(e),n&&(window["paris_"+n]=u[n]=t),i&&(window["paris_"+i]=u[i]=t),t},VERSION:F,VERSION_FLAG:function(e){for(var t=e.split("."),n=1,r=0,o=t.length-1;o>=0;o--,n*=100){var i=t[o];r+=(i=Number(i.split("-")[0]))*n}return r}(F)};"undefined"!=typeof window&&function(){if(window.ParisFactory){var e=window.ParisFactory.VERSION_FLAG;L.VERSION_FLAG>e&&(window.ParisFactory=L)}else window.ParisFactory=L}()}();var ZL="undefined"!=typeof window?window.ParisFactory:null,YL={exports:{}};!function(e,t){e.exports=function(){function e(e,t){for(var n in t)e[n]=t[n];return e}function t(e){var t=!1;if(e&&e.setItem){t=!0;var n="__"+Math.round(1e7*Math.random());try{e.setItem(n,n),e.removeItem(n)}catch(r){t=!1}}return t}function n(e){return"string"===Sf(e)&&window[e]instanceof Storage?window[e]:e}function r(e){return"[object Date]"===Object.prototype.toString.call(e)&&!isNaN(e.getTime())}function o(e,t){if(t=t||new Date,"number"==typeof e?e=e===1/0?f:new Date(t.getTime()+1e3*e):"string"==typeof e&&(e=new Date(e)),e&&!r(e))throw new Error("`expires` parameter cannot be converted to a valid Date instance");return e}function i(e){var t=!1;if(e)if(e.code)switch(e.code){case 22:t=!0;break;case 1014:"NS_ERROR_DOM_QUOTA_REACHED"===e.name&&(t=!0)}else-2147024882===e.number&&(t=!0);return t}function a(e,t){this.c=(new Date).getTime();var n=o(t=t||v);this.e=n.getTime(),this.v=e}function u(e){return"object"==Sf(e)&&!!(e&&"c"in e&&"e"in e&&"v"in e)}function c(e){return(new Date).getTime()<e.e}function l(e){return"string"!=typeof e&&(e=String(e)),e}function s(o){var i=e({storage:"localStorage",exp:1/0},o),a=i.exp;if(a&&"number"!=typeof a&&!r(a))throw new Error("Constructor `exp` parameter cannot be converted to a valid Date instance");v=a;var u=n(i.storage),c=t(u);this.isSupported=function(){return c},c?(this.storage=u,this.quotaExceedHandler=function(e,t,n){if(n&&!0===n.force){this.deleteAllExpires();try{n.force=!1,this.set(e,t,n)}catch(r){}}}):e(this,p)}var f=new Date("Fri, 31 Dec 9999 23:59:59 UTC"),v=f,d={serialize:function(e){return ok(e)},deserialize:function(e){return e&&JSON.parse(e)}},p={set:function(){},get:function(){},delete:function(){},deleteAllExpires:function(){},clear:function(){},add:function(){},replace:function(){},touch:function(){}},h={set:function(t,n,r){if(t=l(t),"number"==typeof r&&(r={exp:r}),r=e({force:!0},r),void 0===n)return this.delete(t);var o=d.serialize(n),u=new a(o,r.exp);try{this.storage.setItem(t,d.serialize(u))}catch(c){i(c)&&this.quotaExceedHandler(t,o,r,c)}return n},get:function(e){e=l(e);var t=null;try{t=d.deserialize(this.storage.getItem(e))}catch(r){return null}if(u(t)){if(c(t)){var n=t.v;return d.deserialize(n)}this.delete(e)}return null},delete:function(e){return e=l(e),this.storage.removeItem(e),e},deleteAllExpires:function(){for(var e=this.storage.length,t=[],n=this,r=0;e>r;r++){var o=this.storage.key(r),i=null;try{i=d.deserialize(this.storage.getItem(o))}catch(a){}null!==i&&void 0!==i.e&&(new Date).getTime()>=i.e&&t.push(o)}return Rd(t).call(t,(function(e){n.delete(e)})),t},clear:function(){this.storage.clear()},add:function(t,n,r){t=l(t),"number"==typeof r&&(r={exp:r}),r=e({force:!0},r);try{var o=d.deserialize(this.storage.getItem(t));if(!u(o)||!c(o))return this.set(t,n,r),!0}catch(i){return this.set(t,n,r),!0}return!1},replace:function(e,t,n){e=l(e);var r=null;try{r=d.deserialize(this.storage.getItem(e))}catch(o){return!1}if(u(r)){if(c(r))return this.set(e,t,n),!0;this.delete(e)}return!1},touch:function(e,t){e=l(e);var n=null;try{n=d.deserialize(this.storage.getItem(e))}catch(r){return!1}if(u(n)){if(c(n))return this.set(e,this.get(e),{exp:t}),!0;this.delete(e)}return!1}};return s.prototype=h,s}()}(YL);const GL=n(YL.exports);var $L=function(){return{webCache:new GL({storage:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage"})}},XL=i,JL=vt("iterator"),eU=!XL((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n=new URLSearchParams("a=1&a=2&b=3"),r="";return e.pathname="c%20d",t.forEach((function(e,n){t.delete("b"),r+=n+e})),n.delete("a",2),n.delete("b",void 0),!e.toJSON||!n.has("a",1)||n.has("a",2)||!n.has("a",void 0)||n.has("b")||!t.size&&true||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[JL]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==r||"x"!==new URL("http://x",void 0).host})),tU=In,nU=o,rU=P,oU=g,iU=I,aU=eU,uU=Di,cU=Fi,lU=fw,sU=ca,fU=_l,vU=Ca,dU=Oh,pU=E,hU=Xe,gU=Gt,mU=Gn,yU=nn,bU=ee,wU=yi,AU=Ao,_U=L,xU=Hv,SU=uf,CU=Yl,kU=Bh,EU=zE,TU=vt("iterator"),IU="URLSearchParams",OU=IU+"Iterator",RU=vU.set,PU=vU.getterFor(IU),jU=vU.getterFor(OU),NU=Object.getOwnPropertyDescriptor,MU=function(e){if(!iU)return nU[e];var t=NU(nU,e);return t&&t.value},DU=MU("fetch"),BU=MU("Request"),FU=MU("Headers"),LU=BU&&BU.prototype,UU=FU&&FU.prototype,VU=nU.RegExp,zU=nU.TypeError,HU=nU.decodeURIComponent,WU=nU.encodeURIComponent,qU=oU("".charAt),QU=oU([].join),KU=oU([].push),ZU=oU("".replace),YU=oU([].shift),GU=oU([].splice),$U=oU("".split),XU=oU("".slice),JU=/\+/g,eV=Array(4),tV=function(e){return eV[e-1]||(eV[e-1]=VU("((?:%[\\da-f]{2}){"+e+"})","gi"))},nV=function(e){try{return HU(e)}catch(kK){return e}},rV=function(e){var t=ZU(e,JU," "),n=4;try{return HU(t)}catch(kK){for(;n;)t=ZU(t,tV(n--),nV);return t}},oV=/[!'()~]|%20/g,iV={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},aV=function(e){return iV[e]},uV=function(e){return ZU(WU(e),oV,aV)},cV=fU((function(e,t){RU(this,{type:OU,target:PU(e).entries,index:0,kind:t})}),IU,(function(){var e=jU(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=void 0,CU(void 0,!0);var r=t[n];switch(e.kind){case"keys":return CU(r.key,!1);case"values":return CU(r.value,!1)}return CU([r.key,r.value],!1)}),!0),lV=function(e){this.entries=[],this.url=null,void 0!==e&&(bU(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===qU(e,0)?XU(e,1):e:wU(e)))};lV.prototype={type:IU,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,o,i,a,u,c=this.entries,l=SU(e);if(l)for(n=(t=xU(e,l)).next;!(r=rU(n,t)).done;){if(i=(o=xU(yU(r.value))).next,(a=rU(i,o)).done||(u=rU(i,o)).done||!rU(i,o).done)throw new zU("Expected sequence with length 2");KU(c,{key:wU(a.value),value:wU(u.value)})}else for(var s in e)hU(e,s)&&KU(c,{key:s,value:wU(e[s])})},parseQuery:function(e){if(e)for(var t,n,r=this.entries,o=$U(e,"&"),i=0;i<o.length;)(t=o[i++]).length&&(n=$U(t,"="),KU(r,{key:rV(YU(n)),value:rV(QU(n,"="))}))},serialize:function(){for(var e,t=this.entries,n=[],r=0;r<t.length;)e=t[r++],KU(n,uV(e.key)+"="+uV(e.value));return QU(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var sV=function(){dU(this,fV);var e=RU(this,new lV(arguments.length>0?arguments[0]:void 0));iU||(this.size=e.entries.length)},fV=sV.prototype;if(lU(fV,{append:function(e,t){var n=PU(this);kU(arguments.length,2),KU(n.entries,{key:wU(e),value:wU(t)}),iU||this.length++,n.updateURL()},delete:function(e){for(var t=PU(this),n=kU(arguments.length,1),r=t.entries,o=wU(e),i=n<2?void 0:arguments[1],a=void 0===i?i:wU(i),u=0;u<r.length;){var c=r[u];if(c.key!==o||void 0!==a&&c.value!==a)u++;else if(GU(r,u,1),void 0!==a)break}iU||(this.size=r.length),t.updateURL()},get:function(e){var t=PU(this).entries;kU(arguments.length,1);for(var n=wU(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){var t=PU(this).entries;kU(arguments.length,1);for(var n=wU(e),r=[],o=0;o<t.length;o++)t[o].key===n&&KU(r,t[o].value);return r},has:function(e){for(var t=PU(this).entries,n=kU(arguments.length,1),r=wU(e),o=n<2?void 0:arguments[1],i=void 0===o?o:wU(o),a=0;a<t.length;){var u=t[a++];if(u.key===r&&(void 0===i||u.value===i))return!0}return!1},set:function(e,t){var n=PU(this);kU(arguments.length,1);for(var r,o=n.entries,i=!1,a=wU(e),u=wU(t),c=0;c<o.length;c++)(r=o[c]).key===a&&(i?GU(o,c--,1):(i=!0,r.value=u));i||KU(o,{key:a,value:u}),iU||(this.size=o.length),n.updateURL()},sort:function(){var e=PU(this);EU(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){for(var t,n=PU(this).entries,r=gU(e,arguments.length>1?arguments[1]:void 0),o=0;o<n.length;)r((t=n[o++]).value,t.key,this)},keys:function(){return new cV(this,"keys")},values:function(){return new cV(this,"values")},entries:function(){return new cV(this,"entries")}},{enumerable:!0}),uU(fV,TU,fV.entries,{name:"entries"}),uU(fV,"toString",(function(){return PU(this).serialize()}),{enumerable:!0}),iU&&cU(fV,"size",{get:function(){return PU(this).entries.length},configurable:!0,enumerable:!0}),sU(sV,IU),tU({global:!0,constructor:!0,forced:!aU},{URLSearchParams:sV}),!aU&&pU(FU)){var vV=oU(UU.has),dV=oU(UU.set),pV=function(e){if(bU(e)){var t,n=e.body;if(mU(n)===IU)return t=e.headers?new FU(e.headers):new FU,vV(t,"content-type")||dV(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),AU(e,{body:_U(0,wU(n)),headers:_U(0,t)})}return e};if(pU(DU)&&tU({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return DU(e,arguments.length>1?pV(arguments[1]):{})}}),pU(BU)){var hV=function(e){return dU(this,LU),new BU(e,arguments.length>1?pV(arguments[1]):{})};LU.constructor=hV,hV.prototype=LU,tU({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:hV})}}var gV,mV={URLSearchParams:sV,getState:PU},yV=g,bV=2147483647,wV=/[^\0-\u007E]/,AV=/[.\u3002\uFF0E\uFF61]/g,_V="Overflow: input needs wider integers to process",xV=RangeError,SV=yV(AV.exec),CV=Math.floor,kV=String.fromCharCode,EV=yV("".charCodeAt),TV=yV([].join),IV=yV([].push),OV=yV("".replace),RV=yV("".split),PV=yV("".toLowerCase),jV=function(e){return e+22+75*(e<26)},NV=function(e,t,n){var r=0;for(e=n?CV(e/700):e>>1,e+=CV(e/t);e>455;)e=CV(e/35),r+=36;return CV(r+36*e/(e+38))},MV=function(e){var t=[];e=function(e){for(var t=[],n=0,r=e.length;n<r;){var o=EV(e,n++);if(o>=55296&&o<=56319&&n<r){var i=EV(e,n++);56320==(64512&i)?IV(t,((1023&o)<<10)+(1023&i)+65536):(IV(t,o),n--)}else IV(t,o)}return t}(e);var n,r,o=e.length,i=128,a=0,u=72;for(n=0;n<e.length;n++)(r=e[n])<128&&IV(t,kV(r));var c=t.length,l=c;for(c&&IV(t,"-");l<o;){var s=bV;for(n=0;n<e.length;n++)(r=e[n])>=i&&r<s&&(s=r);var f=l+1;if(s-i>CV((bV-a)/f))throw new xV(_V);for(a+=(s-i)*f,i=s,n=0;n<e.length;n++){if((r=e[n])<i&&++a>bV)throw new xV(_V);if(r===i){for(var v=a,d=36;;){var p=d<=u?1:d>=u+26?26:d-u;if(v<p)break;var h=v-p,g=36-p;IV(t,kV(jV(p+h%g))),v=CV(h/g),d+=36}IV(t,kV(jV(v))),u=NV(a,f,l===c),a=0,l++}}a++,i++}return TV(t,"")},DV=In,BV=I,FV=eU,LV=o,UV=Gt,VV=g,zV=Di,HV=Fi,WV=Oh,qV=Xe,QV=RO,KV=td,ZV=Ti,YV=Qs.codeAt,GV=function(e){var t,n,r=[],o=RV(OV(PV(e),AV,"."),".");for(t=0;t<o.length;t++)n=o[t],IV(r,SV(wV,n)?"xn--"+MV(n):n);return TV(r,".")},$V=yi,XV=ca,JV=Bh,ez=mV,tz=Ca,nz=tz.set,rz=tz.getterFor("URL"),oz=ez.URLSearchParams,iz=ez.getState,az=LV.URL,uz=LV.TypeError,cz=LV.parseInt,lz=Math.floor,sz=Math.pow,fz=VV("".charAt),vz=VV(/./.exec),dz=VV([].join),pz=VV(1..toString),hz=VV([].pop),gz=VV([].push),mz=VV("".replace),yz=VV([].shift),bz=VV("".split),wz=VV("".slice),Az=VV("".toLowerCase),_z=VV([].unshift),xz="Invalid scheme",Sz="Invalid host",Cz="Invalid port",kz=/[a-z]/i,Ez=/[\d+-.a-z]/i,Tz=/\d/,Iz=/^0x/i,Oz=/^[0-7]+$/,Rz=/^\d+$/,Pz=/^[\da-f]+$/i,jz=/[\0\t\n\r #%/:<>?@[\\\]^|]/,Nz=/[\0\t\n\r #/:<>?@[\\\]^|]/,Mz=/^[\u0000-\u0020]+/,Dz=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,Bz=/[\t\n\r]/g,Fz=function(e){var t,n,r,o;if("number"==typeof e){for(t=[],n=0;n<4;n++)_z(t,e%256),e=lz(e/256);return dz(t,".")}if("object"==typeof e){for(t="",r=function(e){for(var t=null,n=1,r=null,o=0,i=0;i<8;i++)0!==e[i]?(o>n&&(t=r,n=o),r=null,o=0):(null===r&&(r=i),++o);return o>n&&(t=r,n=o),t}(e),n=0;n<8;n++)o&&0===e[n]||(o&&(o=!1),r===n?(t+=n?":":"::",o=!0):(t+=pz(e[n],16),n<7&&(t+=":")));return"["+t+"]"}return e},Lz={},Uz=QV({},Lz,{" ":1,'"':1,"<":1,">":1,"`":1}),Vz=QV({},Uz,{"#":1,"?":1,"{":1,"}":1}),zz=QV({},Vz,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Hz=function(e,t){var n=YV(e,0);return n>32&&n<127&&!qV(t,e)?e:encodeURIComponent(e)},Wz={ftp:21,file:null,http:80,https:443,ws:80,wss:443},qz=function(e,t){var n;return 2===e.length&&vz(kz,fz(e,0))&&(":"===(n=fz(e,1))||!t&&"|"===n)},Qz=function(e){var t;return e.length>1&&qz(wz(e,0,2))&&(2===e.length||"/"===(t=fz(e,2))||"\\"===t||"?"===t||"#"===t)},Kz=function(e){return"."===e||"%2e"===Az(e)},Zz={},Yz={},Gz={},$z={},Xz={},Jz={},eH={},tH={},nH={},rH={},oH={},iH={},aH={},uH={},cH={},lH={},sH={},fH={},vH={},dH={},pH={},hH=function(e,t,n){var r,o,i,a=$V(e);if(t){if(o=this.parse(a))throw new uz(o);this.searchParams=null}else{if(void 0!==n&&(r=new hH(n,!0)),o=this.parse(a,null,r))throw new uz(o);(i=iz(new oz)).bindURL(this),this.searchParams=i}};hH.prototype={type:"URL",parse:function(e,t,n){var r,o,i,a,u,c=this,l=t||Zz,s=0,f="",v=!1,d=!1,p=!1;for(e=$V(e),t||(c.scheme="",c.username="",c.password="",c.host=null,c.port=null,c.path=[],c.query=null,c.fragment=null,c.cannotBeABaseURL=!1,e=mz(e,Mz,""),e=mz(e,Dz,"$1")),e=mz(e,Bz,""),r=KV(e);s<=r.length;){switch(o=r[s],l){case Zz:if(!o||!vz(kz,o)){if(t)return xz;l=Gz;continue}f+=Az(o),l=Yz;break;case Yz:if(o&&(vz(Ez,o)||"+"===o||"-"===o||"."===o))f+=Az(o);else{if(":"!==o){if(t)return xz;f="",l=Gz,s=0;continue}if(t&&(c.isSpecial()!==qV(Wz,f)||"file"===f&&(c.includesCredentials()||null!==c.port)||"file"===c.scheme&&!c.host))return;if(c.scheme=f,t)return void(c.isSpecial()&&Wz[c.scheme]===c.port&&(c.port=null));f="","file"===c.scheme?l=uH:c.isSpecial()&&n&&n.scheme===c.scheme?l=$z:c.isSpecial()?l=tH:"/"===r[s+1]?(l=Xz,s++):(c.cannotBeABaseURL=!0,gz(c.path,""),l=vH)}break;case Gz:if(!n||n.cannotBeABaseURL&&"#"!==o)return xz;if(n.cannotBeABaseURL&&"#"===o){c.scheme=n.scheme,c.path=ZV(n.path),c.query=n.query,c.fragment="",c.cannotBeABaseURL=!0,l=pH;break}l="file"===n.scheme?uH:Jz;continue;case $z:if("/"!==o||"/"!==r[s+1]){l=Jz;continue}l=nH,s++;break;case Xz:if("/"===o){l=rH;break}l=fH;continue;case Jz:if(c.scheme=n.scheme,o===gV)c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=ZV(n.path),c.query=n.query;else if("/"===o||"\\"===o&&c.isSpecial())l=eH;else if("?"===o)c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=ZV(n.path),c.query="",l=dH;else{if("#"!==o){c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=ZV(n.path),c.path.length--,l=fH;continue}c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=ZV(n.path),c.query=n.query,c.fragment="",l=pH}break;case eH:if(!c.isSpecial()||"/"!==o&&"\\"!==o){if("/"!==o){c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,l=fH;continue}l=rH}else l=nH;break;case tH:if(l=nH,"/"!==o||"/"!==fz(f,s+1))continue;s++;break;case nH:if("/"!==o&&"\\"!==o){l=rH;continue}break;case rH:if("@"===o){v&&(f="%40"+f),v=!0,i=KV(f);for(var h=0;h<i.length;h++){var g=i[h];if(":"!==g||p){var m=Hz(g,zz);p?c.password+=m:c.username+=m}else p=!0}f=""}else if(o===gV||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()){if(v&&""===f)return"Invalid authority";s-=KV(f).length+1,f="",l=oH}else f+=o;break;case oH:case iH:if(t&&"file"===c.scheme){l=lH;continue}if(":"!==o||d){if(o===gV||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()){if(c.isSpecial()&&""===f)return Sz;if(t&&""===f&&(c.includesCredentials()||null!==c.port))return;if(a=c.parseHost(f))return a;if(f="",l=sH,t)return;continue}"["===o?d=!0:"]"===o&&(d=!1),f+=o}else{if(""===f)return Sz;if(a=c.parseHost(f))return a;if(f="",l=aH,t===iH)return}break;case aH:if(!vz(Tz,o)){if(o===gV||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()||t){if(""!==f){var y=cz(f,10);if(y>65535)return Cz;c.port=c.isSpecial()&&y===Wz[c.scheme]?null:y,f=""}if(t)return;l=sH;continue}return Cz}f+=o;break;case uH:if(c.scheme="file","/"===o||"\\"===o)l=cH;else{if(!n||"file"!==n.scheme){l=fH;continue}switch(o){case gV:c.host=n.host,c.path=ZV(n.path),c.query=n.query;break;case"?":c.host=n.host,c.path=ZV(n.path),c.query="",l=dH;break;case"#":c.host=n.host,c.path=ZV(n.path),c.query=n.query,c.fragment="",l=pH;break;default:Qz(dz(ZV(r,s),""))||(c.host=n.host,c.path=ZV(n.path),c.shortenPath()),l=fH;continue}}break;case cH:if("/"===o||"\\"===o){l=lH;break}n&&"file"===n.scheme&&!Qz(dz(ZV(r,s),""))&&(qz(n.path[0],!0)?gz(c.path,n.path[0]):c.host=n.host),l=fH;continue;case lH:if(o===gV||"/"===o||"\\"===o||"?"===o||"#"===o){if(!t&&qz(f))l=fH;else if(""===f){if(c.host="",t)return;l=sH}else{if(a=c.parseHost(f))return a;if("localhost"===c.host&&(c.host=""),t)return;f="",l=sH}continue}f+=o;break;case sH:if(c.isSpecial()){if(l=fH,"/"!==o&&"\\"!==o)continue}else if(t||"?"!==o)if(t||"#"!==o){if(o!==gV&&(l=fH,"/"!==o))continue}else c.fragment="",l=pH;else c.query="",l=dH;break;case fH:if(o===gV||"/"===o||"\\"===o&&c.isSpecial()||!t&&("?"===o||"#"===o)){if(".."===(u=Az(u=f))||"%2e."===u||".%2e"===u||"%2e%2e"===u?(c.shortenPath(),"/"===o||"\\"===o&&c.isSpecial()||gz(c.path,"")):Kz(f)?"/"===o||"\\"===o&&c.isSpecial()||gz(c.path,""):("file"===c.scheme&&!c.path.length&&qz(f)&&(c.host&&(c.host=""),f=fz(f,0)+":"),gz(c.path,f)),f="","file"===c.scheme&&(o===gV||"?"===o||"#"===o))for(;c.path.length>1&&""===c.path[0];)yz(c.path);"?"===o?(c.query="",l=dH):"#"===o&&(c.fragment="",l=pH)}else f+=Hz(o,Vz);break;case vH:"?"===o?(c.query="",l=dH):"#"===o?(c.fragment="",l=pH):o!==gV&&(c.path[0]+=Hz(o,Lz));break;case dH:t||"#"!==o?o!==gV&&("'"===o&&c.isSpecial()?c.query+="%27":c.query+="#"===o?"%23":Hz(o,Lz)):(c.fragment="",l=pH);break;case pH:o!==gV&&(c.fragment+=Hz(o,Uz))}s++}},parseHost:function(e){var t,n,r;if("["===fz(e,0)){if("]"!==fz(e,e.length-1))return Sz;if(t=function(e){var t,n,r,o,i,a,u,c=[0,0,0,0,0,0,0,0],l=0,s=null,f=0,v=function(){return fz(e,f)};if(":"===v()){if(":"!==fz(e,1))return;f+=2,s=++l}for(;v();){if(8===l)return;if(":"!==v()){for(t=n=0;n<4&&vz(Pz,v());)t=16*t+cz(v(),16),f++,n++;if("."===v()){if(0===n)return;if(f-=n,l>6)return;for(r=0;v();){if(o=null,r>0){if(!("."===v()&&r<4))return;f++}if(!vz(Tz,v()))return;for(;vz(Tz,v());){if(i=cz(v(),10),null===o)o=i;else{if(0===o)return;o=10*o+i}if(o>255)return;f++}c[l]=256*c[l]+o,2!=++r&&4!==r||l++}if(4!==r)return;break}if(":"===v()){if(f++,!v())return}else if(v())return;c[l++]=t}else{if(null!==s)return;f++,s=++l}}if(null!==s)for(a=l-s,l=7;0!==l&&a>0;)u=c[l],c[l--]=c[s+a-1],c[s+--a]=u;else if(8!==l)return;return c}(wz(e,1,-1)),!t)return Sz;this.host=t}else if(this.isSpecial()){if(e=GV(e),vz(jz,e))return Sz;if(t=function(e){var t,n,r,o,i,a,u,c=bz(e,".");if(c.length&&""===c[c.length-1]&&c.length--,(t=c.length)>4)return e;for(n=[],r=0;r<t;r++){if(""===(o=c[r]))return e;if(i=10,o.length>1&&"0"===fz(o,0)&&(i=vz(Iz,o)?16:8,o=wz(o,8===i?1:2)),""===o)a=0;else{if(!vz(10===i?Rz:8===i?Oz:Pz,o))return e;a=cz(o,i)}gz(n,a)}for(r=0;r<t;r++)if(a=n[r],r===t-1){if(a>=sz(256,5-t))return null}else if(a>255)return null;for(u=hz(n),r=0;r<n.length;r++)u+=n[r]*sz(256,3-r);return u}(e),null===t)return Sz;this.host=t}else{if(vz(Nz,e))return Sz;for(t="",n=KV(e),r=0;r<n.length;r++)t+=Hz(n[r],Lz);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return qV(Wz,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"===this.scheme&&1===t&&qz(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,o=e.host,i=e.port,a=e.path,u=e.query,c=e.fragment,l=t+":";return null!==o?(l+="//",e.includesCredentials()&&(l+=n+(r?":"+r:"")+"@"),l+=Fz(o),null!==i&&(l+=":"+i)):"file"===t&&(l+="//"),l+=e.cannotBeABaseURL?a[0]:a.length?"/"+dz(a,"/"):"",null!==u&&(l+="?"+u),null!==c&&(l+="#"+c),l},setHref:function(e){var t=this.parse(e);if(t)throw new uz(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"===e)try{return new gH(e.path[0]).origin}catch(kK){return"null"}return"file"!==e&&this.isSpecial()?e+"://"+Fz(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse($V(e)+":",Zz)},getUsername:function(){return this.username},setUsername:function(e){var t=KV($V(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=Hz(t[n],zz)}},getPassword:function(){return this.password},setPassword:function(e){var t=KV($V(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=Hz(t[n],zz)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?Fz(e):Fz(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,oH)},getHostname:function(){var e=this.host;return null===e?"":Fz(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,iH)},getPort:function(){var e=this.port;return null===e?"":$V(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""===(e=$V(e))?this.port=null:this.parse(e,aH))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+dz(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,sH))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""===(e=$V(e))?this.query=null:("?"===fz(e,0)&&(e=wz(e,1)),this.query="",this.parse(e,dH)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!==(e=$V(e))?("#"===fz(e,0)&&(e=wz(e,1)),this.fragment="",this.parse(e,pH)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var gH=function(e){var t=WV(this,mH),n=JV(arguments.length,1)>1?arguments[1]:void 0,r=nz(t,new hH(e,!1,n));BV||(t.href=r.serialize(),t.origin=r.getOrigin(),t.protocol=r.getProtocol(),t.username=r.getUsername(),t.password=r.getPassword(),t.host=r.getHost(),t.hostname=r.getHostname(),t.port=r.getPort(),t.pathname=r.getPathname(),t.search=r.getSearch(),t.searchParams=r.getSearchParams(),t.hash=r.getHash())},mH=gH.prototype,yH=function(e,t){return{get:function(){return rz(this)[e]()},set:t&&function(e){return rz(this)[t](e)},configurable:!0,enumerable:!0}};if(BV&&(HV(mH,"href",yH("serialize","setHref")),HV(mH,"origin",yH("getOrigin")),HV(mH,"protocol",yH("getProtocol","setProtocol")),HV(mH,"username",yH("getUsername","setUsername")),HV(mH,"password",yH("getPassword","setPassword")),HV(mH,"host",yH("getHost","setHost")),HV(mH,"hostname",yH("getHostname","setHostname")),HV(mH,"port",yH("getPort","setPort")),HV(mH,"pathname",yH("getPathname","setPathname")),HV(mH,"search",yH("getSearch","setSearch")),HV(mH,"searchParams",yH("getSearchParams")),HV(mH,"hash",yH("getHash","setHash"))),zV(mH,"toJSON",(function(){return rz(this).serialize()}),{enumerable:!0}),zV(mH,"toString",(function(){return rz(this).serialize()}),{enumerable:!0}),az){var bH=az.createObjectURL,wH=az.revokeObjectURL;bH&&zV(gH,"createObjectURL",UV(bH,az)),wH&&zV(gH,"revokeObjectURL",UV(wH,az))}XV(gH,"URL"),DV({global:!0,constructor:!0,forced:!FV,sham:!BV},{URL:gH});var AH=In,_H=i,xH=Bh,SH=yi,CH=eU,kH=ae("URL");AH({target:"URL",stat:!0,forced:!(CH&&_H((function(){kH.canParse()})))},{canParse:function(e){var t=xH(arguments.length,1),n=SH(e),r=t<2||void 0===arguments[1]?void 0:SH(arguments[1]);try{return!!new kH(n,r)}catch(kK){return!1}}});const EH=n(te.URL);var TH=nL.entries;In({target:"Object",stat:!0},{entries:function(e){return TH(e)}});const IH=n(te.Object.entries);var OH=tF().siteConfig;const RH={log:function(e){try{var t,n,r;e=e||{};var o=OH.value.webRoot+"report/log.gif";if(!e.pstage||!e.stage||!e.logType)return;e.loginid=OH.value.eid||"",e.t=e.t||rb(),e.s=OH.value.siteId||"",e.v=OH.value.bid||"",e.sid=OH.value.sid||"",e.dev=OH.value.platform;var i=yd(t=yd(n=yd(r="".concat(e.loginid,"-")).call(r,e.pstage,"-")).call(n,e.stage,"-")).call(t,e.v);if("START"===e.logType)sessionStorage.setItem(i,ok(e));else if("END"===e.logType){var a={},u=sessionStorage.getItem(i)||"";if(u&&(a=JSON.parse(u)),!a||"START"!==a.logType)return void sessionStorage.removeItem(i);var c=e.t-(a.t||0);if(c<=0||c>=1e4)return void sessionStorage.removeItem(i);var l=BH();a.lid=e.lid=l,e.st=a.t,e.et=e.t,this.send(o,e),sessionStorage.removeItem(i)}else if("IMMEDIATELY"===e.logType){var s=BH();e.lid=s,this.send(o,e)}}catch(kK){}},send:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};rb();for(var r=new Image,o="",i=0,a=IH(t);i<a.length;i++){var u=vd(a[i],2),c=u[0],l=u[1];if(null!=l)o+="&"+c+"="+encodeURIComponent(l)}r.src=e+"?"+tp(o).call(o,1),r.onload=r.onerror=r.onabort=function(){"function"==typeof n&&n&&n()}},logStatic:function(e){this.log({pstage:1,stage:2,logType:e.logType||"START",fnName:"logStatic",t:e.t})},logVisiterEnter:function(e){this.log({pstage:1,stage:3,logType:e.logType||"START",fnName:"logVisiterEnter",t:e.t})},logVisiterEnterHTJ:function(e){this.log({pstage:1,stage:5,logType:e.logType||"START",fnName:"logVisiterEnterHTJ",t:e.t})},logVisiterEnterAust:function(e){this.log({pstage:1,stage:6,logType:e.logType||"START",fnName:"logVisiterEnterAust",t:e.t})},logVisiterEnterHTJStatic:function(e){this.log({pstage:1,stage:7,logType:e.logType||"START",fnName:"logVisiterEnterHTJStatic",t:e.t})},logVisiterEnterHTJServer:function(e){this.log({pstage:1,stage:8,logType:e.logType||"START",fnName:"logVisiterEnterHTJServer",t:e.t})},logVisiterOpenBcpAiAgent:function(e){this.log({pstage:11,stage:1,logType:"IMMEDIATELY",fnName:"logVisiterOpenBcpAiAgent",t:e.t})}};var PH=/^[\s\uFEFF\xA0]*$/;function jH(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"string"!=typeof e?e:e=(e=(e=(e=e.replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/\"/g,"&quot;")).replace(/\'/g,"&apos;")}function NH(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return PH.test(e)}function MH(e,t){var n=(t=t||"").substr(hk(t).call(t,"?")),r=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),o=n.substr(1).match(r);return o&&o.length>2?decodeURIComponent(o[2]):""}function DH(e){return"number"==typeof e&&!isNaN(e)}function BH(){for(var e=[],t="0123456789ABCDEF",n=0;n<32;n++)e[n]=t.substr(Math.floor(16*Math.random()),1);e[12]="4",e[16]=t.substr(3&Number(e[16])|8,1);var r=e.join("");return r=(r=r.toLowerCase()).replace(/^(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})$/,"$1-$2-$3-$4-$5")}function FH(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'""';try{return JSON.parse(e)}catch(kK){return""}}function LH(e,t){var n,r,o,i=vd(e.split("?"),2),a=i[0],u=i[1],c=u?lb(n=u.split("&")).call(n,(function(e){return e.split("=")})):[],l={};for(var s in Rd(c).call(c,(function(e){var t=vd(e,2),n=t[0],r=t[1];l[n]=decodeURIComponent(r)})),t)t.hasOwnProperty(s)&&void 0!==t[s]&&null!==t[s]&&String(t[s])&&(l[s]=t[s]);var f=lb(r=IH(l)).call(r,(function(e){var t,n=vd(e,2),r=n[0],o=n[1];return yd(t="".concat(encodeURIComponent(r),"=")).call(t,encodeURIComponent(o))})).join("&");return f?yd(o="".concat(a,"?")).call(o,f):a}var UH=tF().siteConfig,VH=$L().webCache,zH="QIAO_LS_",HH="NEWBRIDGE_BID_",WH=function(e){var t,n=UH.value.bid||"";if(t=UH.value.session,"[object Object]"===Object.prototype.toString.call(t)&&void 0!==n&&""!==n)return n;var r=HH+e;return n||(n=VH.get(r)||function(e){var t=zH+e+"_BID",n=VH.get(t);return n?n=VT(n.substr(0,13),16).toString():n}(e)||function(){var e=(+new Date).toString(),t=Math.floor(1e5*Math.random()).toString(),n=5-t.length,r=0;if(n>0)for(r=0;r<n;r++)t+="0";return e+t}()),VH.set(r,n),n};function qH(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function QH(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=qH(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=qH(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var KH=tF(),ZH=KH.siteConfig,YH=KH.updateSiteConfig,GH=KH.styleConfig,$H=oF().emitter,XH={handler1:function(e){"invite"===e.reason?(YH({sid:e.sid,tid:e.uid,fromType:2,ttype:0}),$H.emit(VL,e)):"chat"===e.reason&&(YH({sid:e.sid,tid:e.uid,fromType:2,ttype:0,invited:1,sinfo:e.sinfo&&JSON.parse(e.sinfo)||{}}),$H.emit(UL,e))},handler104:function(e){YH({status:e.status}),$H.emit(zL,e)}},JH=function e(){vW().then((function(t){Rd(t).call(t,(function(t){YH({sid:t.msg.sid,uid:t.msg.uid});var n="handler".concat(t.type);XH.hasOwnProperty(n)&&t.msg&&(XH[n](t.msg),Gx(e,10))}))})).catch((function(t){Gx(e,5e3)}))},eW=function(e){var t;RH.logVisiterEnter({logType:"END"});var n=e[0]||{},r=n.v||(null===(t=n.msg)||void 0===t?void 0:t.v);YH({key:n.key||""}),r&&YH({bid:r}),Rd(e).call(e,(function(e){var t=e.msg||{};if(2===e.type)return $H.emit(HL,e),void(ZH.value.blocked=1);100===e.type&&(YH(QH(QH({nbAustData:e},t),{},{enterStatus:t.status})),"complete"===document.readyState?JH():window.addEventListener("load",JH),3===t.status&&($H.emit(iF.BIZ_EVENT_INVITE_SHOW,!0),YH({fromType:2,ttype:0})),4===t.status&&$H.emit(iF.BIZ_EVENT_M_CHAT_CONTINUE),$H.emit(iF.BIZ_EVENT_INVITE_LOOP_SHOW,t))}))},tW=function(){var e=GH.value.blackWhiteList||null,t=!1;if(e&&e.type>-1){for(var n,r,o=location.href,i=o,a=o.length-1;a>0&&"/"===o[a];a--)i=o.substr(0,a);if(1==+e.type)Rd(n=e.siteBlackWhiteList).call(n,(function(e){if(FI(e).call(e,"*"))ep(i).call(i,e.substr(0,e.length-1))&&(t=!0);else if(i===e)t=!0;else if(e.length>i.length&&ep(e).call(e,i)){var n=e.substr(i.length,e.length-1);/^[/]+$/.test(n)&&(t=!0)}}));else t=!0,Rd(r=e.siteBlackWhiteList).call(r,(function(e){if(FI(e).call(e,"*"))ep(i).call(i,e.substr(0,e.length-1))&&(t=!1);else if(i===e)t=!1;else if(e.length>i.length){var n=e.substr(i.length,e.length-1);/^[/]+$/.test(n)&&(t=!1)}}))}return t},nW=function(){var e=yF(jF.mark((function e(){var t,n,r,o,i,a;return jF.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(YH({bid:WH(String(ZH.value.siteId))}),RH.logVisiterEnter({logType:"START"}),RH.logVisiterEnterHTJ({logType:"START"}),n="",!ZH.value.ymgWhitelist){e.next=11;break}return e.next=7,sW();case 7:o=e.sent,r=o.htj.jt,e.next=17;break;case 11:return e.next=13,lW();case 13:i=e.sent,r=i.jt,t=qL.getData().Token,n=qL.getData().view;case 17:if(RH.logVisiterEnterHTJ({logType:"END"}),!r){e.next=29;break}return YH({AFDto:t||"",AFDvw:n||"",AFDjt:r}),RH.logVisiterEnterAust({logType:"START"}),e.next=23,fW();case 23:a=e.sent,RH.logVisiterEnterAust({logType:"END"}),eW(a),$H.emit(iF.BIZ_EVENT_ENTER_PASS),e.next=31;break;case 29:return e.abrupt("return");case 31:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),rW=function(){var e=yF(jF.mark((function e(){var t,n;return jF.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return YH({bid:WH(String(ZH.value.siteId))}),RH.logVisiterEnter({logType:"START"}),RH.logVisiterEnterHTJ({logType:"START"}),e.next=5,lW();case 5:if(t=e.sent,RH.logVisiterEnterHTJ({logType:"END"}),!t.jt){e.next=15;break}return YH({AFDto:qL.getData().Token||"",AFDvw:qL.getData().view||"",AFDjt:t.jt}),RH.logVisiterEnterAust({logType:"START"}),e.next=12,fW();case 12:return n=e.sent,RH.logVisiterEnterAust({logType:"END"}),e.abrupt("return",n);case 15:return e.abrupt("return");case 17:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();function oW(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function iW(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=oW(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=oW(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var aW=tF(),uW=aW.siteConfig,cW=aW.getAuth,lW=function(){return qL.init({aid:"5480",app:"universe",ver:"1.0.0.0",timeout:500}),new Jy((function(e,t){var n={aid:"5480",timeout:500,biz:{ev:"page_enter",customer:uW.value.eid||"",bid:uW.value.bid||"",length:0},complete:function(t){e(t)}};try{qL.report(n)}catch(kK){t(kK)}}))},sW=function(){var e="4984ec8f17",t={sak:e,timeout:5e3,bantiUrl:"https://banti-static.cdn.bcebos.com/o/static/banti_".concat(e,".js"),bantiOptions:{subid:function(){return""}}},n=ZL.create(t);return new Jy((function(e,t){n.getBantiInitResponse((function(n,r){n?t(n):e(r)}))}))},fW=function(){var e,t,n,r,o,i,a,u,c;return PL({url:"/site/aust",data:{op:0,s_info:(u=window.navigator,c=window.screen,{lang:u.language||"",cbit:c.colorDepth,rsl:yd(e="".concat(c.width,"*")).call(e,c.height),tz:(r=(new Date).getTimezoneOffset(),o=VT(String(r/60),10),i=r%60,a="-",(o<0||i<0)&&(a="+",o=-o,i<0&&(i=-i)),yd(t=yd(n="UTC".concat(a)).call(n,o,":")).call(t,i)),xst:encodeURIComponent(pW()),bd_bxst:encodeURIComponent(hW()),bd_vid:encodeURIComponent(gW()),referrer:encodeURIComponent(document.referrer),xstlink:encodeURIComponent(location.href),isAiAgent:ZH.value.isAiAgent}),url:location.href,siteToken:uW.value.siteToken,dev:uW.value.platform,ser:uW.value.server,v:uW.value.bid,s:uW.value.siteId,e:uW.value.eid,isAFF:1,filterAdvertisement:uW.value.filterAdvertisement,auth:cW(),AFDbiz:{ev:"page_enter",customer:uW.value.eid||"",bid:uW.value.bid||"",length:0},AFDto:uW.value.AFDto,AFDvw:uW.value.AFDvw,AFDjt:uW.value.AFDjt}})},vW=function(){return PL({url:"/site/poll",data:{l:1,sign:"",v:uW.value.bid,s:uW.value.siteId,e:uW.value.eid,isAFF:1,filterAdvertisement:uW.value.filterAdvertisement,dev:uW.value.platform,auth:cW()}})},dW=function(e,t){var n="";if(!t)return n;if(!function(e){var t=/https?:\/\/([^\/]+)/i.exec(e=e||"");return null!=t&&t.length>1?t[1]:""}(t))return n;if(!(n=MH(e,t))){var r=MH("reqParam",t)||"";try{r=JSON.parse(r)}catch(o){r={}}"object"===Sf(r)&&r.hasOwnProperty(e)&&r[e]&&(n=r[e])}return n},pW=function(){var e=dW("xst",location.href);return!e&&MH("referer",location.href)&&(e=dW("xst",MH("referer",location.href))),!e&&document.referrer&&(e=dW("xst",document.referrer)),e},hW=function(){return dW("bd_bxst",location.href)},gW=function(){return dW("bd_vid",location.href)},mW=function(e,t){var n,r=uW.value,o=r.biz_data,i=r.bcpAiAgentUrl,a=o?JSON.parse(o):{},u=yd(n="".concat(i,"&ch=90&sch=")).call(n,a.fromEngineCode);e&&(u+="&showpageinpc=1"),t&&(u+="&customQuery=".concat(encodeURIComponent(t)));var c=pW();c&&(u+="&xst=".concat(c));var l=hW();l&&(u+="&bd_bxst=".concat(l));var s=gW();return s&&(u+="&bd_vid=".concat(s)),RH.logVisiterOpenBcpAiAgent({logType:"IMMEDIATELY"}),u};function yW(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function bW(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=yW(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=yW(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var wW={class:"embed-chat-header-content"},AW={key:1,class:"embed-chat-title"},_W={class:"embed-chat-toolbar"},xW=["onClick"],SW=["onClick"],CW=["onClick"],kW={class:"embed-chat-content"},EW=["src"],TW=["muted"];const IW=IN({__name:"index",setup:function(e){var t,n=$L().webCache,r=tF(),o=r.siteConfig,i=r.styleConfig,a=r.updateSiteConfig,u=i.value.webimConfig,c=i.value.seekIcon,l=Oj(!1),s=Oj(!1),f=Oj(!1),v=kD((function(){return o.value.unreadNum})),d=null,p=gj({nickName:"",message:""}),h=oF().emitter,g=(null===(t=i.value)||void 0===t||null===(t=t.digitalHumanSetting)||void 0===t?void 0:t.data)||!1,m=Oj(!0);var y=Oj(!1),b=Oj(null),w=Oj(null),A=Oj(!1),_=Oj("auto"),x=KB(b,{handle:w,stopPropagation:!0,preventDefault:!0,onStart:function(){_.value="none"},onEnd:function(){_.value="auto"}}),S=x.x,C=x.y,k=XB(),E=k.width,T=k.height,I=452,O=471,R=u.width||I,P=Oj(u.height||O),j=!1,N=kD((function(){var e,t,n,r=yd(e="width:".concat(R||I,"px;\n                height:")).call(e,A.value?P.value||O:P.value,"px;");if(0===S.value&&0===C.value)return"left-bottom"===u.position?"".concat(r,"left:20px;bottom:10px;"):"right-bottom"===u.position?"".concat(r,"right:20px;bottom:10px;"):"".concat(r,"left:20px;bottom:10px;");var o=S.value,i=C.value;if(o<=0&&(o=0),i<=0&&(i=0),o+R>=E.value&&(o=E.value-R),i+P.value>=T.value)i=T.value-P.value;else if(P.value<=46){if(!A.value)return"width:0px;height:0px;left:".concat(o,"px;bottom:10px;");j&&(i+(u.height-46)<=T.value?i+=u.height-46:i=T.value-P.value,j=!1)}return yd(t=yd(n="".concat(r,"left:")).call(n,o,"px;top:")).call(t,i,"px;")}));wN((function(){A.value?y.value?(P.value=46,j=!0):(P.value=u.height||O,o.value.unreadNum=0,f.value=!1,d&&clearTimeout(d)):P.value=0}));var M=Oj(null),D=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];m.value&&(A.value=!0,n.delete("closed-im-chat".concat(o.value.siteId))),y.value=e,e||(n.delete("mini-im-chat".concat(o.value.siteId)),M.value&&M.value.contentWindow&&M.value.contentWindow.postMessage({target:"chatBox",status:!0},"*"),M.value&&M.value.contentWindow&&M.value.contentWindow.postMessage({target:"BackBottom",status:!0},"*")),m.value=!0},B=function(){A.value=!1,n.set("closed-im-chat".concat(o.value.siteId),!0),M.value&&M.value.contentWindow&&M.value.contentWindow.postMessage({target:"chatBox",status:!1},"*")};function F(){y.value?n.delete("mini-im-chat".concat(o.value.siteId)):n.set("mini-im-chat".concat(o.value.siteId),!0),y.value=!y.value,M.value&&M.value.contentWindow&&M.value.contentWindow.postMessage({target:"chatBox",status:!y.value},"*")}function L(){h.emit(iF.BIZ_EVENT_CLEAR_AUTO_CHAT),l.value?D():l.value=!0}HB(M,"load",(function(){if(1!=+o.value.bcpAiAgentEnable||!o.value.bcpAiAgentUrl){var e=!u.isOpenKeepCom&&n.get("mini-im-chat".concat(o.value.siteId));D(e),Gx((function(){h.emit(iF.BIZ_EVENT_CHAT_IFRAME_LOADED)}))}}));var U=Oj(""),V=Oj(null),z=Oj(n.get("".concat(o.value.siteId,"-muted")));function H(){z.value?n.delete("".concat(o.value.siteId,"-muted")):n.set("".concat(o.value.siteId,"-muted"),!0),z.value=!z.value}var W="",q="",Q=null;function K(e){var t,n,r,i,a,u=o.value,c=u.webRoot,l=u.siteId,s=u.userId,f=u.siteToken,v=u.groupId,d=u.bid,p=u.pageId,h=u.eid,g=u.ttype,m=u.uid,y=u.sid,b=u.bcpAiAgentEnable,w=u.bcpAiAgentUrl,A={from:0,sid:"-100",tid:v||"-1",ttype:1,siteId:l,userId:s,pageId:p};e&&(A={from:2,sessionid:y,siteId:l,tid:m||"-1",userId:s,ttype:g});var _=pW();_&&(A.xst=_);var x,S,C=yd(t=yd(n=yd(r=yd(i=yd(a="".concat(c,"chatIndex?\n                siteToken=")).call(a,f,"\n                &bid=")).call(i,d,"\n                &eid=")).call(r,h,"&reqParam=")).call(n,ok(A),"\n                &clickTime=")).call(t,rb());W&&(C=yd(x=yd(S="".concat(C,"&hotIssueid=")).call(S,encodeURIComponent(W),"&hotIssue=")).call(x,encodeURIComponent(q)));1==+b&&w&&(C=mW(!0,q)),W&&(W="",q=""),Q&&!Q.closed&&Q.window?Q.focus():Q=window.open(C.replace(/[\r\n\s+]/g,""),"_blank")}var Z=function(){var e,t,n,r,i,a,u,c,l,s,f,v,d,p=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,h=o.value,g=h.webRoot,m=h.siteId,y=h.userId,b=h.siteToken,w=h.groupId,A=h.bid,_=h.forcetalkReason,x=void 0===_?"":_,S=h.nbAustData,C=h.uid,k=h.sid,E=w||-1,T=yd(e=yd(t=yd(n=yd(r=yd(i=yd(a=yd(u="".concat(g,"chat?\n        ctype=0\n        &siteId=")).call(u,m,"\n        &userId=")).call(a,y,"\n        &siteToken=")).call(i,b,"\n        &bid=")).call(r,A,"\n        &fromType=")).call(n,p,"\n        &forcetalkReason=")).call(t,x,"\n        &clientBeginTime=")).call(e,rb());1===p&&(E=C,T=yd(l="".concat(T,"&sid=")).call(l,k));2===p&&(T=yd(s=yd(f="".concat(T,"&austData=")).call(f,encodeURIComponent(ok(S)),"&sid=")).call(s,k),E=C);W&&(T=yd(v=yd(d="".concat(T,"&hotIssueid=")).call(d,encodeURIComponent(W),"&hotIssue=")).call(v,encodeURIComponent(q)),W="",q="");return(T=yd(c="".concat(T,"&tid=")).call(c,E)).replace(/[\r\n\s+]/g,"")};oF({name:UL,callback:function(e){var t=e;a({nbAustData:bW(bW({},o.value.nbAustData),{},{invite:1,uid:t.uid}),forcetalkReason:t.reason||"",sid:t.sid}),U.value=Z(2),L()}}),oF({name:zL,callback:function(e){var t=e;4===t.status&&"waiterDirectChat"===t.reason&&(l.value||(U.value=Z(2)),L())}});var Y=o.value,G=Y.online,$=Y.isOpenOfflineChat,X=Y.bcpAiAgentEnable,J=Y.bcpAiAgentUrl;oF({name:iF.BIZ_EVENT_ENTER_PASS,callback:function(){if(u.isOpenAutoDirectCom&&1==+X&&J){var e=mW(!0);Gx((function(){window.open(e)}),1e3*u.autoDuration)}else if(4===o.value.enterStatus){if(o.value.isWebim)return m.value=!1,U.value=Z(-1),void(l.value=!0);if(0===o.value.isWebim&&!u.isOpenKeepCom)return n.get("closed-im-chat".concat(o.value.siteId))&&(m.value=!1),U.value=Z(-1),void(l.value=!0);if(0===o.value.isWebim&&u.isOpenKeepCom)return m.value=!0,U.value=Z(-1),void(l.value=!0)}else 4!==o.value.status&&!c.iconType&&u.isOpenAutoDirectCom&&o.value.isCsOnline&&!o.value.isWebim&&Gx((function(){l.value||(U.value=Z(3),L())}),1e3*u.autoDuration)}}),oF({name:iF.BIZ_EVENT_ICON_CLICK,callback:function(){if(1==+X&&J){var e=mW(!0);window.open(e)}else("true"===G||$)&&(o.value.isWebim?K(0):(l.value||(U.value=Z(0)),L()))}}),oF({name:iF.BIZ_EVENT_GROUP_ICON_CLICK,callback:function(e){e.isSelected||$?(a({groupId:e.groupId}),o.value.isWebim?K(0):(l.value||(U.value=Z(0)),L())):l.value&&D()}}),oF({name:iF.BIZ_EVENT_ICON_HOTISSUE_CLICK,callback:function(e){if(W=e.id,q=e.question,1==+X&&J){var t=mW(!0,q);window.open(t)}else if(c.iconType){var n,r=qF(n=c.groups).call(n,(function(e){return e.isSelected}));(r||$)&&(r&&a({groupId:r.groupId}),o.value.isWebim?K(0):(l.value||(U.value=Z(0)),L()))}else("true"===G||$)&&(o.value.isWebim?K(0):(l.value||(U.value=Z(0)),L()))}}),oF({name:iF.BIZ_EVENT_INVITE_ACCEPT,callback:function(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&!A.value)return U.value=Z(3),void L();o.value.isWebim?K(2===o.value.fromType?1:0):(l.value||(U.value=Z(2===o.value.fromType?1:-1)),a({invited:o.value.tid&&"-1"!==o.value.tid?1:0}),L())}});var ee=[];function te(){s.value=!1}return window.addEventListener("message",(function(e){var t=e.data,n=(i.value.components||{}).comps;(void 0===n?[]:n).length?"chatBox"===t.target&&("sendMessage"===t.event||"openFrameChat"===t.event&&"changeTab"===t.type)&&D():"chatBox"===t.target&&"sendMessage"===t.event?(z.value||!V.value||0==+t.fromType||g||V.value.play(),u.autoPopupMsg?D():Q&&!Q.closed&&Q.window||!y.value&&A.value||function(e){var t,n=e?JSON.parse(e):"";if(n){var r=("string"==typeof n.msgBody.msg.content?n.msgBody.msg.content.replace(/<.*?>/g,"").replace(/&nbsp;/g,""):"")||"文件";gp(t=["offlineChatForm","onlineFormAuto"]).call(t,n.message.reason)&&(r="留言表单"),p.nickName=n.msgBody.nickName,p.message=r,f.value=!0,o.value.unreadNum++,d&&clearTimeout(d),d=Gx((function(){f.value=!1}),5e3)}}(t.msg)):"imgViewer"===t.target&&t.src&&(s.value=!0,ee.length=0,ee.push(t.src))})),function(e,t){return qM(),YM(LM,null,[l.value?(qM(),YM("div",{key:0,class:"embed-chat-container",style:QR("".concat(N.value))},[EN(nD("div",{ref_key:"chat",ref:b,class:"embed-chat"},[nD("div",{ref_key:"chatHeader",ref:w,class:"embed-chat-header",style:QR({backgroundColor:Pj(u).customerColor||"#9861E6"})},[nD("div",wW,[Pj(g)?(qM(),GM(LL,{key:0})):(qM(),YM("div",AW,[iD(" 在线咨询 "),EN(nD("span",{class:"embed-chat-unread-num"},oP(Pj(v)),513),[[tB,Pj(v)]])])),nD("div",_W,[Pj(g)?aD("",!0):(qM(),YM("span",{key:0,class:$R(["embed-chat-audio-icon",{"embed-chat-is-mute":z.value}]),onClick:eB(H,["stop"])},null,10,xW)),nD("span",{class:$R(["embed-chat-size-icon",y.value?"embed-chat-is-max":"embed-chat-is-mini"]),onClick:eB(F,["stop"])},null,10,SW),Pj(u).isShowCloseBtn?(qM(),YM("span",{key:1,class:"embed-chat-close-icon",onClick:eB(B,["stop"])},null,8,CW)):aD("",!0)])])],4),EN(nD("div",kW,[nD("iframe",{ref_key:"chatIframe",ref:M,class:"embed-chat-iframe",src:U.value,style:QR({"pointer-events":_.value})},"\n                ",12,EW)],512),[[tB,!y.value]])],512),[[tB,A.value]]),EN(rD(xL,{"massage-info":p,"chat-show":A.value,onShow:t[0]||(t[0]=function(e){return D()}),onCloseTip:t[1]||(t[1]=function(e){return f.value=!1})},null,8,["massage-info","chat-show"]),[[tB,f.value]])],4)):aD("",!0),nD("audio",{ref_key:"audio",ref:V,muted:z.value,preload:"auto","cross-origin":"anonymous",src:"https://aifanfan.baidu.com/chat/static/voice/msg.wav"},null,8,TW),s.value?(qM(),GM(wL,{key:1,"on-close":te,"url-list":ee})):aD("",!0)],64)}}}),OW=lF(IW,[["__scopeId","data-v-e2f257f5"]]);var RW=function(e){return dN("data-v-c877883f"),e=e(),pN(),e},PW={class:"embed-messageboard-form-select-input"},jW=RW((function(){return nD("label",{class:"embed-messageboard-form-icon-ext"},null,-1)})),NW={class:"embed-messageboard-form-select-value"},MW=["onClick"];const DW=lF(IN({__name:"FormSelect",props:{modelValue:{default:""},options:{default:function(){return[]}},placeholder:{default:"请选择"}},emits:["update:modelValue"],setup:function(e,t){var n=t.emit,r=e,o=Oj(!1),i=Oj(null),a=Oj(null),u=GB(a).height,c=YB(i),l=c.top,s=c.bottom,f=c.update,v=XB().height,d=kD((function(){return l.value<u.value?"left:0px;top:auto;":v.value-s.value<u.value?"left:0px;top:0px;transform:translateY(-100%);":"left:0px;top:auto;"}));function p(){f(),o.value=!o.value}return LN((function(){r.modelValue||n("update:modelValue",r.options[0]||"")})),function(e,t){return qM(),YM("div",{ref_key:"selectEl",ref:i,class:"embed-messageboard-form-select",onClick:p},[nD("div",PW,[jW,nD("div",NW,oP(e.modelValue||e.placeholder),1)]),EN(nD("div",{ref_key:"optionsEl",ref:a,class:"embed-messageboard-form-select-options",style:QR(d.value)},[(qM(!0),YM(LM,null,YN(e.options,(function(e,t){return qM(),YM("div",{key:t,class:"embed-messageboard-form-select-option",onClick:eB((function(t){return function(e){n("update:modelValue",e),o.value=!1}(e)}),["stop"])},oP(e),9,MW)})),128))],4),[[tB,o.value]])],512)}}}),[["__scopeId","data-v-c877883f"]]);var BW=g,FW=Rr,LW=yi,UW=function(e){var t=YO(GO(this)),n="",r=ZO(e);if(r<0||r===1/0)throw new $O("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},VW=Z,zW=BW(UW),HW=BW("".slice),WW=Math.ceil,qW=function(e){return function(t,n,r){var o,i,a=LW(VW(t)),u=FW(n),c=a.length,l=void 0===r?" ":LW(r);return u<=c||""===l?a:((i=zW(l,WW((o=u-c)/l.length))).length>o&&(i=HW(i,0,o)),e?a+i:i+a)}},QW={start:qW(!1),end:qW(!0)},KW=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(ce),ZW=QW.start;In({target:"String",proto:!0,forced:KW},{padStart:function(e){return ZW(this,e,arguments.length>1?arguments[1]:void 0)}});var YW=Df("String","padStart"),GW=ue,$W=YW,XW=String.prototype;const JW=n((function(e){var t=e.padStart;return"string"==typeof e||e===XW||GW(XW,e)&&t===XW.padStart?$W:t}));var eq=/^1\d{10}$|^0\d{10,11}$|^[4]00\d{7}$|^[2356789]\d{6,7}$|^4[123456789]\d{5,6}$|^4\d{1}[123456789]\d{4,5}$/,tq=/^(?!.{50,})(\w+([-.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$)/,nq=function(e){return e[e.Success=1]="Success",e[e.Fail=2]="Fail",e}(nq||{}),rq=ek(ek({},1,{title:"感谢留言",msg:"我们会尽快与您联系",btn:"关闭"}),2,{title:"提交失败",msg:"提交失败请刷新重试!",btn:"返回"});var oq={content:"留言内容",visitorName:"姓名",visitorPhone:"电话",visitorEmail:"邮箱",visitorAddress:"地址"},iq={content:[],visitorName:[],visitorPhone:[{validator:function(e){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return eq.test(e)}(e)},message:"请输入正确的电话号码"}],visitorEmail:[{validator:function(e){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return tq.test(e)}(e)},message:"邮箱格式输入有误"}],visitorAddress:[]};function aq(e,t){var n=void 0!==Bs&&sf(e)||e["@@iterator"];if(!n){if(df(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return uq(e,t);var r=tp(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return hb(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uq(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw i}}}}function uq(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var cq=function(e){return dN("data-v-39c4214d"),e=e(),pN(),e},lq={class:"embed-messageboard-form"},sq={key:0,class:"embed-messageboard-form-content"},fq=["placeholder","onFocus"],vq={key:1,class:"embed-messageboard-form-item"},dq=cq((function(){return nD("label",{class:"embed-messageboard-form-label embed-messageboard-form-icon-name"},null,-1)})),pq=["placeholder","onFocus"],hq={key:2,class:"embed-messageboard-form-item"},gq=cq((function(){return nD("label",{class:"embed-messageboard-form-label embed-messageboard-form-icon-phone"},null,-1)})),mq=["placeholder","onFocus"],yq={key:3,class:"embed-messageboard-form-item"},bq=cq((function(){return nD("label",{class:"embed-messageboard-form-label embed-messageboard-form-icon-email"},null,-1)})),wq=["placeholder","onFocus"],Aq={key:4,class:"embed-messageboard-form-item"},_q=cq((function(){return nD("label",{class:"embed-messageboard-form-label embed-messageboard-form-icon-address"},null,-1)})),xq=["placeholder","onFocus"],Sq={key:5,class:"embed-messageboard-form-error-tip"},Cq={key:0,class:"embed-messageboard-form-item"},kq=cq((function(){return nD("label",{class:"embed-messageboard-form-label embed-messageboard-form-icon-ext"},null,-1)})),Eq=["onUpdate:modelValue","placeholder","onFocus"],Tq={key:1},Iq={class:"embed-messageboard-select-title"},Oq=cq((function(){return nD("label",{class:"embed-messageboard-form-label embed-messageboard-form-icon-ext"},null,-1)})),Rq={key:2,class:"embed-messageboard-form-error-tip"},Pq={class:"embed-messageboard-send"};const jq=lF(IN({__name:"BoardForm",props:{loading:{type:Boolean,default:!1}},emits:["submit"],setup:function(e,t){var n=t.expose,r=t.emit;qD((function(e){return{"488531de":c.value}}));var o=tF().styleConfig,i=kD((function(){return o.value.noteBoard})),a=kD((function(){return i.value.items})),u=kD((function(){return i.value.itemsExt})),c=kD((function(){return i.value.btnBgColor||"#9861E6"})),l=Oj({}),s=Oj({});function f(e){s.value[e]=""}function v(){(function(){var e,t,n=!0,r={},o=aq(yd(e=a.value).call(e,u.value));try{for(o.s();!(t=o.n()).done;){var i=t.value;if(i.isShow){var c=NH(l.value[i.name]||"");if(i.required&&c)r[i.name]="".concat(oq[i.name]||i.question,"不能为空"),n=!1;else{var f=iq[i.name]||[];if(f.length>0&&(!c||i.required)){var v,d=aq(f);try{for(d.s();!(v=d.n()).done;){var p=v.value;if(!p.validator(l.value[i.name]||"")){r[i.name]=p.message,n=!1;break}}}catch(h){d.e(h)}finally{d.f()}}}}}}catch(h){o.e(h)}finally{o.f()}return s.value=r,n})()&&r("submit",l.value)}return n({resetForm:function(){l.value={}}}),function(e,t){return qM(),YM("div",lq,[(qM(!0),YM(LM,null,YN(a.value,(function(e,n){var r,o,i,a;return qM(),YM(LM,null,[1===e.isShow?(qM(),YM("div",{key:n,class:$R(s.value[e.name]&&"embed-messageboard-error")},["content"===e.name?(qM(),YM("div",sq,[EN(nD("textarea",{"onUpdate:modelValue":t[0]||(t[0]=function(e){return l.value.content=e}),placeholder:"请在此输入留言内容，我们会尽快与您联系。".concat(e.required?"（必填）":""),onFocus:function(t){return f(e.name)}},null,40,fq),[[$D,l.value.content]])])):aD("",!0),"visitorName"===e.name?(qM(),YM("div",vq,[dq,EN(nD("input",{"onUpdate:modelValue":t[1]||(t[1]=function(e){return l.value.visitorName=e}),class:"embed-messageboard-form-input",placeholder:yd(r="".concat(Pj(oq)[e.name])).call(r,e.required?"（必填）":""),maxlength:"30",onFocus:function(t){return f(e.name)}},null,40,pq),[[$D,l.value.visitorName]])])):aD("",!0),"visitorPhone"===e.name?(qM(),YM("div",hq,[gq,EN(nD("input",{"onUpdate:modelValue":t[2]||(t[2]=function(e){return l.value.visitorPhone=e}),class:"embed-messageboard-form-input",placeholder:yd(o="".concat(Pj(oq)[e.name])).call(o,e.required?"（必填）":""),maxlength:"30",onFocus:function(t){return f(e.name)}},null,40,mq),[[$D,l.value.visitorPhone]])])):aD("",!0),"visitorEmail"===e.name?(qM(),YM("div",yq,[bq,EN(nD("input",{"onUpdate:modelValue":t[3]||(t[3]=function(e){return l.value.visitorEmail=e}),class:"embed-messageboard-form-input",placeholder:yd(i="".concat(Pj(oq)[e.name])).call(i,e.required?"（必填）":""),maxlength:"50",onFocus:function(t){return f(e.name)}},null,40,wq),[[$D,l.value.visitorEmail]])])):aD("",!0),"visitorAddress"===e.name?(qM(),YM("div",Aq,[_q,EN(nD("input",{"onUpdate:modelValue":t[4]||(t[4]=function(e){return l.value.visitorAddress=e}),class:"embed-messageboard-form-input",placeholder:yd(a="".concat(Pj(oq)[e.name])).call(a,e.required?"（必填）":""),maxlength:"50",onFocus:function(t){return f(e.name)}},null,40,xq),[[$D,l.value.visitorAddress]])])):aD("",!0),s.value[e.name]?(qM(),YM("div",Sq,oP(s.value[e.name]),1)):aD("",!0)],2)):aD("",!0)],64)})),256)),(qM(!0),YM(LM,null,YN(u.value,(function(e,t){var n;return qM(),YM(LM,null,[1===e.isShow?(qM(),YM("div",{key:t,class:$R(s.value[e.name]&&"embed-messageboard-error")},[e.subItems?aD("",!0):(qM(),YM("div",Cq,[kq,EN(nD("input",{"onUpdate:modelValue":function(t){return l.value[e.name]=t},class:"embed-messageboard-form-input",placeholder:yd(n="".concat(e.question)).call(n,e.required?"（必填）":""),maxlength:"50",onFocus:function(t){return f(e.name)}},null,40,Eq),[[$D,l.value[e.name]]])])),e.subItems?(qM(),YM("div",Tq,[nD("div",Iq,[Oq,nD("div",null,oP(e.question),1)]),rD(DW,{modelValue:l.value[e.name],"onUpdate:modelValue":function(t){return l.value[e.name]=t},options:e.subItems},null,8,["modelValue","onUpdate:modelValue","options"])])):aD("",!0),s.value[e.name]?(qM(),YM("div",Rq,oP(s.value[e.name]),1)):aD("",!0)],2)):aD("",!0)],64)})),256)),nD("div",Pq,[nD("div",{class:$R(["embed-messageboard-send-btn",e.loading&&"embed-messageboard-send-disable"]),onClick:v},"提交",2)])])}}}),[["__scopeId","data-v-39c4214d"]]);var Nq=function(e){return dN("data-v-b38b337f"),e=e(),pN(),e},Mq={class:"embed-messageboard-result"},Dq={class:"embed-messageboard-result-box"},Bq=Nq((function(){return nD("div",{class:"embed-messageboard-result-icon"},null,-1)})),Fq={class:"embed-messageboard-result-title"},Lq={class:"embed-messageboard-result-content"};const Uq=lF(IN({__name:"SubmitResult",props:{status:{default:nq.Success}},emits:["cancel"],setup:function(e,t){var n=t.emit,r=e;qD((function(e){return{"85fcfdfc":u.value}}));var o=tF().styleConfig,i=kD((function(){return rq[r.status]})),a=kD((function(){return o.value.noteBoard})),u=kD((function(){return a.value.btnBgColor||"#9861E6"}));function c(){n("cancel")}return function(e,t){return qM(),YM("div",Mq,[nD("div",Dq,[Bq,nD("div",Fq,oP(i.value.title),1),nD("div",Lq,oP(i.value.msg),1),nD("div",{class:"embed-messageboard-result-close",onClick:c},oP(i.value.btn),1)])])}}}),[["__scopeId","data-v-b38b337f"]]);var Vq=function(){},zq=!1;function Hq(){return!!window.sdkMachine||zq||($B("https://passport.baidu.com/static/sdk-machine/js/mkd_v2.js?_=".concat(rb()),(function(){zq=!1})),zq=!0),{machineCheck:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Vq,t=arguments.length>1?arguments[1]:void 0,n=window.sdkMachine;if("mobile"===t){var r=document.getElementById("embed-popover-mobile-form-aicode");r&&(r.innerHTML="")}if(n)var o=new n({deviceType:"mobile"===t?"wap":"pc",type:"spin",container:"mobile"===t?"embed-popover-mobile-form-aicode":document.body,ak:"s10IFr4xJh1PjUvIEkq0d6bu14VWZsaS",zIndex:2147483647,initApiSuccessFn:function(){o.init()},verifySuccessFn:function(t){o.hide(),e({ds:t.ds,tk:t.tk})},errorFn:function(e){}})}}}function Wq(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function qq(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=Wq(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=Wq(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var Qq=function(e){return dN("data-v-afdef977"),e=e(),pN(),e},Kq={key:0,class:"embed-messageboard"},Zq={class:"embed-messageboard-container"},Yq=Qq((function(){return nD("span",{class:"embed-messageboard-header-title"},"请您留言",-1)})),Gq={key:0,class:"embed-messageboard-text"},$q={class:"embed-messageboard-company"},Xq={key:0,class:"embed-messageboard-link"};const Jq=lF(IN({__name:"index",setup:function(e,t){var n=t.expose;qD((function(e){return{"1d2fba71":y.value}}));var r=Oj(!1),o=Oj(!1),i=Oj(!1),a=Oj(nq.Success),u=Oj(!1),c=Oj(!1),l=Oj(null),s=Oj(null),f=Oj(null),v=tF(),d=v.styleConfig,p=v.siteConfig,h=v.updateStyleConfig,g=Hq().machineCheck,m=kD((function(){return d.value.noteBoard})),y=kD((function(){return m.value.btnBgColor||"#9861E6"}));!function(){var e=m.value.board,t=m.value.webim;if(!e)switch(m.value.isAlwaysDisplay){case 0:case 3:e={boardOffline:1,boardOnline:0};break;case 1:e={boardOffline:1,boardOnline:1};break;case 2:e={boardOffline:0,boardOnline:0}}t||(t={webimOnline:1,webimOffline:1});if(h({noteBoard:qq(qq({},m.value),{},{board:e,webim:t})}),"true"===p.value.online)return void(u.value=!!e.boardOnline);u.value=!!e.boardOffline}();var b=GB(s),w=b.width,A=b.height,_=XB(),x=_.width,S=_.height,C=KB(s,{handle:f,onStart:function(){i.value=!1}}),k=C.x,E=C.y,T=kD((function(){if(i.value){var e=(x.value-w.value)/2,t=(S.value-A.value)/2;return k.value=e,E.value=t,{left:"".concat(e,"px"),top:"".concat(t,"px")}}if(0===k.value&&0===E.value)return"left-bottom"===m.value.position?{left:"20px",bottom:"10px"}:"right-bottom"===m.value.position?{right:"20px",bottom:"10px"}:{left:"20px",bottom:"10px"};var n=k.value,r=E.value;return n<=0&&(n=0),r<=0&&(r=0),n+w.value>=x.value&&(n=x.value-w.value),r+A.value>=S.value&&(r=S.value-A.value),{left:"".concat(n,"px"),top:"".concat(r,"px")}})),I=kD((function(){return!(null===m.value.btnBgColorType||void 0===m.value.btnBgColorType)}));function O(){r.value=!r.value}function R(e){var t,n,r,o,i,a,u,c,l,s,f,v,d,h,y=qq(qq({},(v=p.value,h={},Rd(d=["bid","csrfToken","siteToken"]).call(d,(function(e){e in v&&(h[e]=v[e])})),h)),{},{siteid:p.value.siteId,v:p.value.bid,s:p.value.siteId,e:p.value.eid,userid:p.value.eid,client:ok((s=window.navigator,f=window.screen,{language:s.language,color:"".concat(f.colorDepth),ppi:yd(t="".concat(f.width,"*")).call(t,f.height),timeZone:(a=(new Date).getTimezoneOffset(),u=Math.floor(a/60),c=a%60,l="-",(u<0||c<0)&&(l="+",u=Math.abs(u),c=Math.abs(c)),yd(n=yd(r="UTC".concat(l)).call(r,JW(o=String(u)).call(o,2,"0"),":")).call(n,JW(i=String(c)).call(i,2,"0")))})),referrer:document.referrer,origin:document.title||document.URL,needVerifyCode:m.value.needVerifyCode});1!==m.value.needVerifyCode?P(qq(qq({},e),y)):g((function(t){P(qq(qq(qq({},e),y),t))}))}function P(e){c.value||(c.value=!0,PL({url:"/bookmanage/saveBook.action",data:e}).then((function(e){var t;a.value=nq.Success,null===(t=l.value)||void 0===t||t.resetForm()})).catch((function(){a.value=nq.Fail})).finally((function(){o.value=!0,c.value=!1})))}function j(){a.value===nq.Success&&(r.value=!0),o.value=!1}function N(){r.value=!1,u.value=!0,i.value=!0}var M=p.value,D=M.online,B=M.isOpenOfflineChat;return oF({name:iF.BIZ_EVENT_ICON_CLICK,callback:function(){var e=m.value.board;"false"===D&&!B&&e.boardOffline&&N()}}),oF({name:iF.BIZ_EVENT_GROUP_ICON_CLICK,callback:function(e){var t=m.value.board;e.isSelected||B||!t.boardOffline||N()}}),oF({name:iF.BIZ_EVENT_ICON_HOTISSUE_CLICK,callback:function(){var e,t=d.value.seekIcon,n=m.value.board;t.iconType?qF(e=t.groups).call(e,(function(e){return e.isSelected}))||B||!n.boardOffline||N():"false"===D&&!B&&n.boardOffline&&N()}}),oF({name:iF.BIZ_EVENT_MESSAGE_SHOW_CENTER,callback:function(){N()}}),n({showInCenter:N}),function(e,t){return u.value?(qM(),YM("div",Kq,[nD("div",{ref_key:"el",ref:s,class:$R(["embed-messageboard-base"]),style:QR(T.value)},[nD("div",Zq,[nD("div",{ref_key:"dragHandler",ref:f,class:"embed-messageboard-header"},[Yq,nD("span",{class:$R(["embed-messageboard-header-close",r.value&&"embed-messageboard-header-max"]),onClick:O},null,2)],512),EN(nD("div",null,[1===m.value.displayCompany?(qM(),YM("div",Gq,[nD("p",$q,oP(Pj(jH)(m.value.cpyInfo)),1),I.value?aD("",!0):(qM(),YM("p",Xq,oP(Pj(jH)(m.value.cpyTel)),1))])):aD("",!0),EN(rD(jq,{ref_key:"boardForm",ref:l,loading:c.value,onSubmit:R},null,8,["loading"]),[[tB,!o.value]]),EN(rD(Uq,{status:a.value,onCancel:j},null,8,["status"]),[[tB,o.value]])],512),[[tB,!r.value]])])],4)])):aD("",!0)}}}),[["__scopeId","data-v-afdef977"]]);var eQ=function(e){return e[e.IM=1]="IM",e[e.Phone=2]="Phone",e[e.Form=3]="Form",e[e.Qrcode=4]="Qrcode",e[e.CorpQrcode=5]="CorpQrcode",e}(eQ||{}),tQ={"right-center":{right:"12px",top:"50%",transform:"translateY(-50%)"},"right-top":{right:"12px",top:"0"},"right-bottom":{right:"12px",bottom:"0"},"left-center":{left:"12px",top:"50%",transform:"translateY(-50%)"},"left-top":{left:"12px",top:"0"},"left-bottom":{left:"12px",bottom:"0"}};const nQ=lF(IN({__name:"Popover",props:{disable:{type:Boolean,default:!1},closeable:{type:Boolean,default:!0}},emits:["change"],setup:function(e,t){var n=t.expose,r=t.emit,o=e,i=Oj(!1),a=Oj(null),u=Oj(null),c=GB(u),l=XB(),s=kD((function(){var e,t=(null===(e=a.value)||void 0===e?void 0:e.getBoundingClientRect())||{},n={top:0,right:"".concat(t.width+8,"px")},r={top:"50%",right:"".concat(t.width+2,"px"),transform:"translateY(-50%)"};if(c.height.value+t.top>l.height.value){var o=l.height.value-t.top-c.height.value;n.top="".concat(o-12,"px")}return c.height.value>=l.height.value&&(n.top="-".concat(t.top,"px"),n.height="".concat(l.height.value,"px")),c.width.value>t.left&&(n.right="auto",n.left="".concat(t.width+8,"px"),r.right="auto",r.left="".concat(t.width+2,"px"),r.transform="translateY(-50%) rotate(180deg)"),c.height.value<=t.height&&(n.top="50%",n.transform="translateY(-50%)"),{content:n,arrow:r}}));function f(){i.value=!i.value}function v(){i.value=!1}return qB(u,(function(){i.value=!1}),{ignore:[a,".vcode-mask",".vcode-body",".embed-invite",".embed-chat-container"]}),_N(i,(function(e){r("change",e)})),n({close:v}),function(e,t){return qM(),YM("div",{ref_key:"popover",ref:a,class:"embed-popover",onClick:f},[GN(e.$slots,"reference",{},void 0,!0),EN(nD("div",{ref_key:"content",ref:u,class:"embed-popover-content",style:QR(s.value.content),onClick:t[0]||(t[0]=eB((function(){}),["stop"]))},[GN(e.$slots,"default",{},void 0,!0),e.closeable&&!o.disable?(qM(),YM("div",{key:0,class:"embed-popover-close",onClick:v})):aD("",!0)],4),[[tB,i.value]]),o.disable?aD("",!0):EN((qM(),YM("div",{key:0,class:"embed-popover-arrow",style:QR(s.value.arrow)},null,4)),[[tB,i.value]])],512)}}}),[["__scopeId","data-v-70766355"]]);var rQ=["value","placeholder"],oQ=["onClick"];const iQ=lF(IN({__name:"Select",props:{modelValue:{default:""},options:{default:function(){return[]}},multiple:{type:Boolean,default:!1},placeholder:{default:""}},emits:["update:modelValue"],setup:function(e,t){var n=t.emit,r=e,o=Oj(!1),i=Oj(null),a=Oj(null),u=kD((function(){var e,t=r.modelValue.split(",")||[],n=[];return Rd(e=r.options).call(e,(function(e){gp(t).call(t,e.key)&&n.push(e.value)})),n.join(",")})),c=GB(a).height,l=YB(i),s=l.top,f=l.bottom,v=l.update,d=XB().height,p=kD((function(){return s.value<c.value?"left:0px;top:auto;":d.value-f.value<c.value?"left:0px;top:0px;transform:translateY(-100%);":"left:0px;top:auto;"}));function h(e){var t;return gp(t=r.modelValue).call(t,e.key)}function g(){v(),o.value=!o.value}return qB(a,(function(){o.value=!1}),{ignore:[i]}),function(e,t){return qM(),YM("div",{ref_key:"selectEl",ref:i,class:"embed-form-select",onClick:g},[nD("input",{value:u.value,placeholder:e.placeholder},null,8,rQ),EN(nD("div",{ref_key:"optionsEl",ref:a,class:"embed-form-select-options",style:QR(p.value)},[(qM(!0),YM(LM,null,YN(e.options,(function(t,i){return qM(),YM("div",{key:i,class:"embed-form-select-option",onClick:eB((function(e){return i=t.key,u=r.modelValue.split(",")||[],void(r.multiple?gp(u).call(u,i)?n("update:modelValue",pC(u).call(u,(function(e){return e!==i})).join(",")):n("update:modelValue",yd(a=[]).call(a,dd(u),[i]).join(",")):(n("update:modelValue",i),o.value=!1));var i,a,u}),["stop"])},[e.multiple?(qM(),YM("span",{key:0,class:$R(["embed-form-select-mult-check",h(t)&&"embed-form-select-check"])},null,2)):aD("",!0),iD(" "+oP(t.value),1)],8,oQ)})),128))],4),[[tB,o.value]])],512)}}}),[["__scopeId","data-v-87d6af73"]]);function aQ(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function uQ(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=aQ(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=aQ(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}function cQ(e,t){var n=void 0!==Bs&&sf(e)||e["@@iterator"];if(!n){if(df(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return lQ(e,t);var r=tp(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return hb(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lQ(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw i}}}}function lQ(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var sQ={class:"embed-popover-form-header"},fQ={class:"embed-popover-form-header-title"},vQ={class:"embed-popover-form-header-desc"},dQ={class:"embed-popover-form-main"},pQ=["onUpdate:modelValue","placeholder"],hQ=["onUpdate:modelValue","placeholder"],gQ={key:3,class:"embed-popover-error-tip"},mQ={class:"embed-popover-form-footer"},yQ={key:1,class:"embed-popover-form-result"},bQ={class:"embed-popover-result-title"},wQ={class:"embed-popover-result-subtitle"},AQ={class:"embed-popover-result-footer"};const _Q=IN({__name:"PopoverForm",props:{data:{},machineCheck:{}},emits:["close"],setup:function(e,t){var n=t.emit,r=e,o=Dj(r).data,i=tF().siteConfig,a=Oj({}),u=Oj({}),c=Oj("pendding"),l=Oj(!1);function s(){var e,t=cQ(o.value.fieldSetting||[]);try{for(t.s();!(e=t.n()).done;){var n=e.value;a.value[n.fieldName]=""}}catch(r){t.e(r)}finally{t.f()}}function f(e,t){try{return new RegExp(e).test(t)}catch(n){return!0}}function v(){var e,t=!0,n={},r=cQ(o.value.fieldSetting||[]);try{for(r.s();!(e=r.n()).done;){var i,c=e.value,l=PC(i=a.value[c.fieldName]).call(i);if(!c.required||l){if(c.maxLength||c.minLength&&l){var s=Number(c.maxLength||0),v=Number(c.minLength||0);if(s&&v&&(l.length<v||l.length>s)){var d;t=!1,n[c.fieldName]=yd(d="字数限制在 ".concat(v," ~ ")).call(d,s," 之间");continue}if(s&&l.length>s){t=!1,n[c.fieldName]="最大字数限制 ".concat(s," 字");continue}if(v&&l.length<v){t=!1,n[c.fieldName]="最小字数限制 ".concat(s," 字");continue}}var p={mobilePhone:{pattern:"^1[3456789]\\d{9}$",errorMessage:"请输入正确格式的手机"},companyPhone:{pattern:"(^((0\\d{2,3})-)(\\d{7,8})(-(\\d{3,}))?$)|(^1[3456789]\\d{9}$)",errorMessage:"请输入正确格式的手机或座机"}};if(p[c.fieldName]&&l){var h=p[c.fieldName];if(!f(h.pattern,l)){t=!1,n[c.fieldName]=h.errorMessage;continue}}if(c.rules&&c.rules.length&&l)for(var g=0;g<c.rules.length;g++){if(!f(c.rules[g].pattern,l)){t=!1,n[c.fieldName]="请输入正确格式的".concat(c.displayName);break}}}else t=!1,n[c.fieldName]="".concat(c.displayName,"不能为空")}}catch(m){r.e(m)}finally{r.f()}return u.value=n,t}function d(){var e,t;if(v()){var n={bid:i.value.bid||"",orgId:i.value.eid||"",xst:pW(),bdBxst:hW(),bdVid:gW(),compType:o.value.compType},u={};Rd(e=IH(a.value)).call(e,(function(e){var t=vd(e,2),n=t[0],r=t[1];hk(n).call(n,"dynamic")>=0&&hk(n).call(n,"_")>=0?(u.extFields||(u.extFields={}),u.extFields[n]=r):u[n]=r})),null===(t=r.machineCheck)||void 0===t||t.call(r,(function(e){!function(e){if(l.value)return;l.value=!0;var t="/http/generic/selfbuildsite/saveform",n={params:ok(e.params),formData:ok(e.formData)};PL({url:t,data:n},"crm").then((function(e){c.value="success"})).catch((function(){c.value="error"})).finally((function(){l.value=!1}))}({formData:u,params:uQ(uQ({},n),e)})}))}}function p(){var e;return lb(e=IH(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})).call(e,(function(e){var t=vd(e,2);return{key:t[0],value:t[1]}}))}function h(){n("close"),c.value="pendding",s()}return LN((function(){s()})),function(e,t){return qM(),YM(LM,null,["pendding"===c.value?(qM(),YM(LM,{key:0},[nD("div",sQ,[nD("div",fQ,oP(Pj(o).title),1),nD("div",vQ,oP(Pj(o).desc),1)]),nD("div",dQ,[(qM(!0),YM(LM,null,YN(Pj(o).fieldSetting,(function(e,t){return qM(),YM("div",{key:t,class:"embed-popover-form-item"},[nD("div",{class:$R(["embed-popover-form-label",e.required&&"embed-popover-form-label-required"])},oP(e.displayName),3),nD("div",{class:$R(["embed-popover-form-content",u.value[e.fieldName]&&"embed-popover-form-content-error"])},["Singleline"===e.fieldType||"Phone"===e.fieldType||"Suggest"===e.fieldType?EN((qM(),YM("input",{key:0,"onUpdate:modelValue":function(t){return a.value[e.fieldName]=t},placeholder:e.placeholder,onBlur:v},null,40,pQ)),[[$D,a.value[e.fieldName]]]):aD("",!0),"Multiline"===e.fieldType?EN((qM(),YM("textarea",{key:1,"onUpdate:modelValue":function(t){return a.value[e.fieldName]=t},placeholder:e.placeholder,onBlur:v},null,40,hQ)),[[$D,a.value[e.fieldName]]]):aD("",!0),"Multiselect"===e.fieldType||"Select"===e.fieldType?(qM(),GM(iQ,{key:2,modelValue:a.value[e.fieldName],"onUpdate:modelValue":function(t){return a.value[e.fieldName]=t},options:p(e.options),multiple:"Multiselect"===e.fieldType,placeholder:e.placeholder},null,8,["modelValue","onUpdate:modelValue","options","multiple","placeholder"])):aD("",!0),u.value[e.fieldName]?(qM(),YM("div",gQ,oP(u.value[e.fieldName]),1)):aD("",!0)],2)])})),128))]),nD("div",mQ,[nD("button",{type:"button",onClick:h},"取消"),nD("button",{type:"button",class:"embed-popover-form-footer-submit",onClick:d},oP(Pj(o).submitBtnName),1)])],64)):aD("",!0),"pendding"!==c.value?(qM(),YM("div",yQ,[nD("div",{class:$R(["embed-popover-result-icon","error"===c.value&&"embed-popover-error-icon"])},null,2),nD("div",bQ,oP("success"===c.value?"提交成功!":"提交失败！"),1),nD("div",wQ,oP("success"===c.value?"我们已经收到您提交的信息，相关顾问会尽快与您电话联系。":"抱歉，您的信息提交失败，请检查网络后再次提交。"),1),nD("div",AQ,["success"===c.value?(qM(),YM("button",{key:0,type:"button",class:"embed-popover-result-submit",onClick:h},"我知道了")):aD("",!0),"error"===c.value?(qM(),YM("button",{key:1,type:"button",onClick:h},"取消")):aD("",!0),"error"===c.value?(qM(),YM("button",{key:2,type:"button",class:"embed-popover-result-submit",onClick:d},"再次提交")):aD("",!0)])])):aD("",!0)],64)}}}),xQ=lF(_Q,[["__scopeId","data-v-0a0a659f"]]);var SQ={key:0,class:"embed-popover-panel"},CQ={class:"embed-popover-panel-phone"},kQ={key:1,class:"embed-popover-panel"},EQ={key:2,class:"embed-popover-panel"},TQ=["src"],IQ={class:"embed-popover-panel-qr-code"},OQ={key:3,class:"embed-popover-panel",style:{width:"192px"}},RQ=["src"],PQ={key:1,class:"embed-popover-panel-loading-wrapper"},jQ={key:0,class:"embed-popover-panel-loading"},NQ={class:"embed-popover-panel-qr-code"};const MQ=lF(IN({__name:"PopoverPanel",props:{data:{},disable:{type:Boolean,default:!1},machineCheck:{}},setup:function(e){var t=e,n=Dj(t).data,r=Oj({}),o=Oj(!0),i=Oj(null),a=tF().siteConfig,u=kD((function(){if(n.value.compType===eQ.Phone){for(var e=n.value.phoneList||[],t=[],r=0;r<e.length;r++){var o=e[r]||{};if(o.number){var i=void 0;i=/^[4][0][0][0-9]{7}$/.test(o.number)?o.number.substr(0,3)+"-"+o.number.substr(3,3)+"-"+o.number.substr(6,4):/^[1][0-9]{10}$/.test(o.number)?o.number.substr(0,3)+"-"+o.number.substr(3,4)+"-"+o.number.substr(7,4):o.number,o.desc?t.push(o.desc+"："+i):t.push(i)}}return t}return[]}));function c(e){var t;e&&(t={orgId:a.value.eid,bid:a.value.bid,bdBxst:hW(),bdVid:gW(),xst:pW(),compType:n.value.compType},PL({url:"/http/generic/selfbuildsite/click",data:{params:ok(t)}},"crm").then((function(e){})).catch((function(){})).finally((function(){}))),n.value.compType===eQ.CorpQrcode&&e&&s()}function l(){var e;null===(e=i.value)||void 0===e||e.close()}function s(){var e={qrCodeId:n.value.id,identityInfo:ok({orgId:a.value.eid,bid:a.value.bid}),traceInfo:""};o.value=!0,PL({url:"/http/generic/corpwechatqrcode/createforvisitor",data:{params:ok(e)}},"crm").then((function(e){r.value.qrCodeUrl=e.qrCodeUrl,r.value.stateId=e.stateId,Gx((function(){r.value.qrCodeUrl="",function(){var e={orgId:a.value.eid,stateId:r.value.qrCodeUrl,qrCodeId:n.value.id};PL({url:"/http/generic/corpwechatqrcode/releaseforvisitor",data:{params:ok(e)}},"crm").then((function(){}))}()}),3e5)})).catch((function(){r.value.qrCodeUrl=""})).finally((function(){o.value=!1}))}return function(e,a){return qM(),GM(nQ,{ref_key:"popover",ref:i,disable:t.disable,closeable:Pj(n).compType===Pj(eQ).Form,onChange:c},{reference:hN((function(){return[GN(e.$slots,"default",{},void 0,!0)]})),default:hN((function(){return[Pj(n).compType===Pj(eQ).Phone?(qM(),YM("div",SQ,[nD("div",CQ,[(qM(!0),YM(LM,null,YN(u.value,(function(e,t){return qM(),YM("div",{key:t},oP(e),1)})),128))])])):aD("",!0),Pj(n).compType===Pj(eQ).Form?(qM(),YM("div",kQ,[rD(xQ,{data:Pj(n),"machine-check":e.machineCheck,onClose:l},null,8,["data","machine-check"])])):aD("",!0),Pj(n).compType===Pj(eQ).Qrcode?(qM(),YM("div",EQ,[nD("img",{class:"embed-popover-panel-qr-code-img",src:Pj(n).qrCodeUrl,alt:"微信二维码"},null,8,TQ),nD("div",IQ,oP(Pj(n).officialAccount),1)])):aD("",!0),Pj(n).compType===Pj(eQ).CorpQrcode?(qM(),YM("div",OQ,[r.value.qrCodeUrl?(qM(),YM("img",{key:0,class:"embed-popover-panel-qr-code-img",src:r.value.qrCodeUrl},null,8,RQ)):(qM(),YM("div",PQ,[nD("img",{class:"embed-popover-panel-qr-code-img",src:"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/comp-icon-corp-wechat-qr-code-invalid.png",onClick:s}),o.value?(qM(),YM("div",jQ,[nD("img",{src:"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/comp-icon-loading.png"})])):aD("",!0)])),nD("div",NQ,oP(Pj(n).qrCodeName),1)])):aD("",!0)]})),_:3},8,["disable","closeable"])}}}),[["__scopeId","data-v-aa327ef2"]]);function DQ(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function BQ(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=DQ(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=DQ(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var FQ=["onClick"],LQ={class:"embed-components-item-text"};const UQ=IN({__name:"index",setup:function(e){var t,n=tF(),r=n.styleConfig,o=n.siteConfig,i=kD((function(){return r.value.components||{}})),a=kD((function(){var e;return pC(e=i.value.comps||[]).call(e,(function(e){var t=r.value.noteBoard||{};return!(1===e.compType&&"false"===o.value.online&&!t.webim.webimOffline&&!t.board.boardOffline)&&(2!==e.compType&&e.activate)}))})),u=Oj(!1),c=Oj(null),l=oF().emitter,s=Hq().machineCheck;a.value.length>0&&0===(null===(t=r.value.seekIcon)||void 0===t?void 0:t.iconType)&&(u.value=!0);var f=GB(c),v=f.width,d=f.height,p=XB(),h=p.width,g=p.height,m=kD((function(){var e,t=i.value,n=t.position,r=t.positionType,o=t.marginLeft,a=t.marginTop,u=i.value.compSize,c=i.value.iconColor||"#fff",l=i.value.backgroundColor||"#4E6EF2",s={};if(0===r)s=tQ[n];else{var f=v.value+o>h.value?h.value-v.value:o,p=d.value+a>g.value?g.value-d.value:a;s={left:"".concat(f,"px"),top:"".concat(p,"px")}}return{wrap:BQ(BQ({},s),{},{width:"".concat(i.value.width,"px"),backgroundColor:l}),item:{backgroundColor:l,color:c,fontSize:y(14,i.value.compSize)+"px"},iconWrap:{height:y(20,u)+"px",width:y(20,u)+"px",overflow:"hidden",margin:"auto auto 3px"},icon:{filter:yd(e="drop-shadow(".concat(c," ")).call(e,y(20,u),"px 0px 0px)")}}}));function y(e,t){return e*(t?1+t/100:1)}function b(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=i.value.compSize,n=y(12,t)+"px "+y(8,t)+"px "+y(8,t)+"px",r=y(8,t)+"px "+y(8,t)+"px "+y(12,t)+"px",o=y(8,t)+"px";return 0===e?n:e===a.value.length-1?r:o}function w(e){e.compType===eQ.IM&&(!function(){var e;window.sensors&&(e=window.sensors).track.apply(e,arguments)}("site_component_client_click",{button_name:"沟通工具",site_id:o.value.siteId||"-1"}),l.emit(iF.BIZ_EVENT_ICON_CLICK))}return function(e,t){return u.value?(qM(),YM("div",{key:0,ref_key:"el",ref:c,class:"embed-components",style:QR(m.value.wrap)},[(qM(!0),YM(LM,null,YN(a.value,(function(e,t){return qM(),GM(MQ,{key:t,data:e,disable:e.compType===Pj(eQ).IM,"machine-check":Pj(s)},{default:hN((function(){return[nD("div",{class:"embed-components-item",style:QR(BQ(BQ({},m.value.item),{},{padding:b(t)})),onClick:function(t){return w(e)}},[nD("span",{style:QR(m.value.iconWrap)},[nD("i",{class:$R(["embed-components-item-icon","embed-components-icon-".concat(e.compType)]),style:QR(m.value.icon)},null,6)],4),nD("span",LQ,oP(e.iconName),1)],12,FQ)]})),_:2},1032,["data","disable","machine-check"])})),128))],4)):aD("",!0)}}}),VQ=lF(UQ,[["__scopeId","data-v-51508b36"]]);
/*! js-cookie v3.0.5 | MIT */
function zQ(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var HQ=function e(t,n){function r(e,r,o){if("undefined"!=typeof document){"number"==typeof(o=zQ({},n,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var a in o)o[a]&&(i+="; "+a,!0!==o[a]&&(i+="="+o[a].split(";")[0]));return document.cookie=e+"="+t.write(r,e)+i}}return Object.create({set:r,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],r={},o=0;o<n.length;o++){var i=n[o].split("="),a=i.slice(1).join("=");try{var u=decodeURIComponent(i[0]);if(r[u]=t.read(a,u),e===u)break}catch(c){}}return e?r[e]:r}},remove:function(e,t){r(e,"",zQ({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,zQ({},this.attributes,t))},withConverter:function(t){return e(zQ({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});function WQ(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function qQ(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=WQ(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=WQ(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var QQ="nb-referrer-hostname",KQ="nb-start-page-url";function ZQ(e){var t,n=tF(),r=n.styleConfig,o=n.siteConfig,i=kD((function(){return r.value.inviteBox})),a=kD((function(){return r.value.noteBoard||{}})),u=xj(r.value.inviteBox).maxInviteNum,c=0,l=null===(t=o.value.webimConfig)||void 0===t?void 0:t.isOpenAutoDirectCom,s=0,f=oF().emitter;function v(t){if(t&&!e.isShow.value)e.isShow.value=!0,1===i.value.autoHide&&function(){clearTimeout(c);var e=i.value.closeTime;1===i.value.autoHide&&!isNaN(e)&&+e>=0&&(c=Gx((function(){v(!1)}),1e3*e))}(),i.value.autoChat&&(s&&clearTimeout(s),s=Gx((function(){f.emit(iF.BIZ_EVENT_INVITE_ACCEPT,!0)}),1e3*i.value.toChatTime));else{if(2!==o.value.fromType&&DH(u)&&u--,e.isShow.value=!1,0===i.value.likeCrm&&1===l)return!1;1===i.value.reInvite&&1===i.value.autoInvite&&(DH(u)&&u>0&&d(),void 0===u&&d())}}function d(){clearTimeout(c);var e=i.value.inviteInterval;1===i.value.reInvite&&!isNaN(e)&&e>=0&&(c=Gx((function(){v(!0)}),1e3*e))}function p(){s&&clearTimeout(s)}return oF({name:iF.BIZ_EVENT_INVITE_LOOP_SHOW,callback:function(e){if(1!==o.value.blocked&&("false"!==o.value.online||a.value.webim.webimOffline||a.value.board.boardOffline)){var t,n=!1;if(o.value.isWebim,!!i.value.autoInvite){if(1===i.value.startPage&&(n=!0),0===i.value.startPage){var r=HQ.get(KQ);if(r)if(r===location.href)n=!0;else if(document.referrer){(t=document.referrer,new EH(t).hostname)!==location.hostname&&(n=!0)}else{var u=HQ.get(QQ);u&&u!==location.hostname&&(n=!0),u||(n=!0)}else n=!0}if(n&&0===i.value.likeCrm&&l&&!o.value.isWebim)return void(n=!1)}n&&function(e){var t=i.value.stayTime;"number"!=typeof t||t<0?t=0:t*=1e3;HQ.set(QQ,location.hostname,{path:"/"}),HQ.set(KQ,location.href,{path:"/"}),Gx((function(){!function(e){e&&4!==e.status||clearTimeout(c),v(!0)}(e)}),t)}(e)}}}),oF({name:iF.BIZ_EVENT_CLEAR_AUTO_CHAT,callback:function(){p()}}),{visibleControl:v,clearAutoChatTimer:p}}var YQ={key:0,class:"embed-invite-avatar"},GQ={class:"embed-invite-body"},$Q={class:"embed-invite-content"},XQ={class:"embed-invite-welcome"},JQ={class:"embed-invite-footer"};const eK=IN({__name:"index",setup:function(e){qD((function(e){return{"00651164":u.value,fd4c41ae:v.value,"03efa0c0":l.value,ada3b1a8:f.value,"1f3a7b17":d.value,addee264:s.value,"9951f2ba":c.value}}));var t=Oj(!1),n=tF(),r=n.styleConfig,o=n.siteConfig,i=n.updateSiteConfig,a=kD((function(){return r.value.inviteBox})),u=kD((function(){return a.value.btnBgColor||"#9861E6"})),c=kD((function(){return void 0===a.value.btnBgColorType?"flex-end":{left:"flex-start",center:"center",right:"flex-end"}[a.value.customerStyle.buttonPosition||"center"]})),l=kD((function(){return{left:"flex-start",center:"center",right:"flex-end"}[a.value.fontPosition||"center"]})),s=kD((function(){return"center"===a.value.fontPosition?"center":"left"})),f=kD((function(){return a.value.fontColor||"#333333"})),v=kD((function(){return"transparent url(".concat(a.value.inviteHeadImg||"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/invite-avatar.png",") no-repeat")})),d=kD((function(){return"".concat((a.value.width||400)/2,"px")})),p=kD((function(){return void 0!==a.value.btnBgColorType&&0===a.value.inviteWinType})),h=kD((function(){return function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/<[^>]*>/g,"")}(a.value.welcome)})),g=oF().emitter,m=ZQ({isShow:t}),y=m.visibleControl,b=m.clearAutoChatTimer,w=function(){var e=tF().styleConfig,t=kD((function(){return e.value.inviteBox}));return{wrapStyle:kD((function(){var e=t.value.customerStylePro,n=t.value.btnBgColorType,r=t.value.position,o={top:"50%",left:"50%",bottom:"auto",right:"auto",transform:"translate(-50%, -50%)"};if("left-bottom"===r?o={top:"auto",left:0,bottom:0,right:"auto",transform:"none"}:"right-bottom"===r&&(o={top:"auto",left:"auto",right:0,bottom:0,transform:"none"}),e&&0===t.value.customerStylePro.isInviteFixed)return{width:"".concat(t.value.width||400,"px"),left:"".concat(e.inviteLeft,"px"),top:"".concat(e.inviteTop,"px")};if(void 0!==n){if(1===t.value.inviteWinPos){var i=t.value.customerStyle.horizontalPosition,a=t.value.customerStyle.portraitPosition;void 0===i&&(i="left"),void 0===a&&(a="top");var u=t.value.width||400,c=t.value.inviteWinType?t.value.height:t.value.height+30,l=t.value.customerStyle.marginLeft,s=t.value.inviteWinType?t.value.customerStyle.marginTop:t.value.customerStyle.marginTop<30?30:t.value.customerStyle.marginTop,f="left",v="top",d=0,p=0;return l>window.innerWidth-u?("left"===t.value.customerStyle.horizontalPosition&&(v="right",p=0),"right"===t.value.customerStyle.horizontalPosition&&(v="left",p=0)):(v=i,p=l),s>window.innerHeight-c?("top"===t.value.customerStyle.portraitPosition&&(f="bottom",d=0),"bottom"===t.value.customerStyle.portraitPosition&&(f="top",d=t.value.inviteWinType?0:30)):(f=a,d=s),ek(ek(ek({left:"auto",top:"auto",width:"".concat(t.value.width||400,"px")},f,"".concat(d,"px")),v,"".concat(p,"px")),"transform","none")}return qQ({width:"".concat(t.value.width||400,"px")},o)}return qQ({width:"".concat(t.value.width||400,"px")},o)})),bodyStyle:kD((function(){return 2===t.value.isCustomerStyle?{}:void 0===t.value.btnBgColorType?{background:"#0085DA url(".concat("https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/invite-bg0.png",") no-repeat"),height:"".concat(t.value.height,"px")}:0===t.value.inviteWinType?{height:"".concat(t.value.height,"px")}:{height:"".concat(t.value.height,"px"),background:"url(".concat(t.value.customerStyle.inviteBackImg,") no-repeat center center"),backgroundColor:"#ffffff",backgroundSize:"100% 100%"}}))}}(),A=w.wrapStyle,_=w.bodyStyle;function x(){var e=r.value.noteBoard;if(1==+o.value.bcpAiAgentEnable&&o.value.bcpAiAgentUrl){y(!1),b();var t=mW(!0);window.open(t)}else"false"===o.value.online?("0"===r.value.styleVersion&&g.emit(iF.BIZ_EVENT_MESSAGE_SHOW_CENTER),e.board.boardOffline&&!e.webim.webimOffline&&g.emit(iF.BIZ_EVENT_MESSAGE_SHOW_CENTER),e.webim.webimOffline&&g.emit(iF.BIZ_EVENT_INVITE_ACCEPT)):g.emit(iF.BIZ_EVENT_INVITE_ACCEPT),y(!1),b()}function S(){2===o.value.fromType&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return PL({url:"/chat/push",data:iW({sign:"",e:uW.value.eid,isAFF:"1",filterAdvertisement:uW.value.filterAdvertisement,auth:cW(),AFDto:uW.value.AFDto,AFDvw:uW.value.AFDvw},e)})}({v:o.value.bid,s:o.value.siteId,dev:"0",type:1,sid:o.value.sid,tid:o.value.tid,tid_authtype:4}).then((function(e){i({fromType:1})})).catch((function(){})),i({sid:"-100",tid:"-1",fromType:1,ttype:1}),y(!1),b()}return oF({name:iF.BIZ_EVENT_INVITE_SHOW,callback:function(e){t.value=e}}),oF({name:zL,callback:function(e){var n=e;3===n.status&&"chat"!==n.reason?t.value=!0:t.value=!1}}),oF({name:VL,callback:function(){}}),function(e,n){return t.value?(qM(),YM("div",{key:0,class:"embed-invite",style:QR(Pj(A))},[nD("div",{class:"embed-invite-wrap",style:QR(Pj(_))},[p.value?(qM(),YM("div",YQ)):aD("",!0),nD("div",{class:"embed-invite-close",onClick:S}),nD("div",GQ,[nD("div",$Q,[nD("div",XQ,oP(h.value),1)]),nD("div",JQ,[void 0!==a.value.btnBgColorType?(qM(),YM("div",{key:0,class:"embed-invite-chat-button",onClick:x},oP(a.value.customerStyle.confirmBtnText),1)):(qM(),YM(LM,{key:1},[nD("div",{class:"embed-invite-lagency-button",onClick:S},"稍后再说"),nD("div",{class:"embed-invite-lagency-button embed-invite-lagency-ok",onClick:x},"现在咨询")],64))])])],4)],4)):aD("",!0)}}}),tK=lF(eK,[["__scopeId","data-v-c2e74e82"]]),nK=IN({__name:"pc",setup:function(e){var t=tF(),n=(t.styleConfig,t.siteConfig),r=t.updateImConfig,o=Oj(!1);return r(window.affImConfig||{}),RH.logStatic({t:n.value.startTime,logType:"START"}),RH.logStatic({logType:"END"}),o.value=tW(),tW()||nW(),function(e,t){return o.value?aD("",!0):(qM(),YM(LM,{key:0},[rD(DF),rD(Jq),rD(VQ),rD(OW),rD(tK)],64))}}});var rK=function(e){return e[e.CS=1]="CS",e[e.DYNAMIC=2]="DYNAMIC",e[e.STATIC=3]="STATIC",e[e.CUSTOM=4]="CUSTOM",e}(rK||{}),oK=2147483647;function iK(e){var t;return lb(t=IH(e)).call(t,(function(e){var t,n=vd(e,2),r=n[0],o=n[1],i=r.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();return yd(t="".concat(i,": ")).call(t,o,";")})).join(" ")}function aK(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function uK(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=aK(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=aK(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var cK=function(e){return'<svg width="60" height="60" fill="none" viewBox="0 0 60 60" style="'.concat(e,'">\n    <path d="M27 2.732a6 6 0 0 1 6 0l19.98 11.536a6 6 0 0 1 3 5.196v23.072a6 6 0 0 1-3 5.196L33 59.268a6 6 0 0 1-6 0L7.02 47.732a6 6 0 0 1-3-5.196V19.464a6 6 0 0 1 3-5.196L27 2.732Z" fill="white"/>\n    <path d="M27 2.732a6 6 0 0 1 6 0l19.98 11.536a6 6 0 0 1 3 5.196v23.072a6 6 0 0 1-3 5.196L33 59.268a6 6 0 0 1-6 0L7.02 47.732a6 6 0 0 1-3-5.196V19.464a6 6 0 0 1 3-5.196L27 2.732Z" stroke="currentColor" stroke-width="2"/>\n</svg>')},lK=function(e){return'<svg width="60" height="60" fill="none" viewBox="0 0 60 60" style="'.concat(e,'">\n    <circle cx="30" cy="30" r="28" fill="white"/>\n    <circle cx="30" cy="30" r="28" stroke="currentColor" stroke-width="2"/>\n</svg>')},sK="https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/agent-icon-chat.png",fK={1:"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/agent-icon-1.png",2:"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/agent-icon-2.png",3:"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/agent-icon-3.png",4:"https://aff-im.bj.bcebos.com/onlineEnv/imsdk/assets/agent-icon-4.png"},vK=ek(ek(ek({},rK.CS,{1:{width:48},2:{width:134}}),rK.DYNAMIC,{1:{width:104},2:{width:104},3:{width:34},4:{width:34}}),rK.STATIC,{1:{width:60},2:{width:60},3:{width:60},4:{width:60}}),dK=ek(ek(ek({},rK.CS,{1:{width:60},2:{width:60}}),rK.DYNAMIC,{1:{width:60},2:{width:60}}),rK.STATIC,{1:{width:60},2:{width:60},3:{width:60},4:{width:60}});function pK(e){var t={borderRadius:"6px"};return"right"===e&&(t={borderTopLeftRadius:"6px",borderBottomLeftRadius:"6px"}),"left"===e&&(t={borderTopRightRadius:"6px",borderBottomRightRadius:"6px"}),t}var hK=ek(ek(ek(ek({},rK.CS,(function(e,t){var n,r,o,i,a=pK(t.positionX),u={position:"absolute",top:"-20px",left:"-6px",width:"60px",height:"60px"},c={position:"relative",width:"48px",height:"90px",backgroundColor:e.backgroundColor,boxSizing:"border-box",paddingTop:"46px"},l={color:"#FFF",fontSize:"14px",lineHeight:"18px",textAlign:"center",width:"2em",margin:"0 auto",userSelect:"none"};return 2===e.skinIndex&&(u={position:"absolute",bottom:0,left:"6px",width:"60px",height:"60px"},c={position:"relative",width:"134px",height:"38px",backgroundColor:e.backgroundColor,display:"flex",alignItems:"center"},l={color:"#FFF",fontSize:"14px",marginLeft:"66px",userSelect:"none"}),yd(n=yd(r=yd(o=yd(i='<div style="'.concat(iK(uK(uK({},c),a)),'">\n    <img style="')).call(i,iK(u),'" src="')).call(o,e.frontImage,'" alt="icon" />\n    <div style="')).call(r,iK(l),'">')).call(n,e.iconContent,"</div>\n</div>")})),rK.DYNAMIC,(function(e,t){var n,r,o,i,a=pK(t.positionX),u={},c={width:"104px",height:"32px",backgroundColor:e.backgroundColor,boxSizing:"border-box",display:"flex",justifyContent:"center",alignItems:"center"},l={color:"#FFF",fontSize:"14px",lineHeight:"18px",userSelect:"none"};2===e.skinIndex&&(u={width:"20px",height:"20px",marginRight:"6px"},l={color:"#FFF",fontSize:"14px",userSelect:"none"}),3===e.skinIndex&&(c={width:"34px",height:"96px",backgroundColor:e.backgroundColor,boxSizing:"border-box",display:"flex",justifyContent:"center",alignItems:"center"},l={color:"#FFF",fontSize:"14px",lineHeight:"20px",width:"1em",userSelect:"none"}),4===e.skinIndex&&(c={width:"52px",height:"96px",backgroundColor:e.backgroundColor,boxSizing:"border-box",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},u={width:"24px",height:"24px",marginBottom:"12px"},l={color:"#FFF",fontSize:"14px",lineHeight:"18px",width:"2em",userSelect:"none"});var s=2===e.skinIndex||4===e.skinIndex?yd(n='<img style="'.concat(iK(u),'" src="')).call(n,sK,'" alt="icon" />'):"";return yd(r=yd(o=yd(i='<div style="'.concat(iK(uK(uK({},c),a)),'">\n    ')).call(i,s,'\n    <div style="')).call(o,iK(l),'">')).call(r,e.iconContent,"</div>\n</div>")})),rK.STATIC,(function(e,t){var n,r=fK[e.skinIndex];return yd(n='<img style="'.concat(iK({width:"60px"}),'" src="')).call(n,r,'" alt="icon" />')})),rK.CUSTOM,(function(e,t){var n;return yd(n='<img style="'.concat(iK({width:"60px"}),'" src="')).call(n,e.backgroundImage,'" alt="icon" />')})),gK=ek(ek(ek(ek({},rK.CS,(function(e,t){var n,r,o,i,a,u={color:e.backgroundColor,width:"100%",height:"100%"},c={fontSize:"12px",lineHeight:"24px",textAlign:"center",backgroundColor:e.backgroundColor,color:"#fff",height:"24px",width:"100%",borderRadius:"6px",position:"absolute",left:0,bottom:"-5px",zIndex:2,userSelect:"none"},l=1===e.skinIndex?cK:lK;return yd(n=yd(r=yd(o=yd(i=yd(a='<div style="'.concat(iK({width:"60px",height:"60px",textAlign:"center",position:"relative"}),'">\n    ')).call(a,l(iK(u)),'\n    <img style="')).call(i,iK({position:"absolute",width:"100%",height:"100%",left:0,top:0,zIndex:1}),'" src="')).call(o,e.frontImage,'" alt="icon" />\n    <div style="')).call(r,iK(c),'">')).call(n,e.iconContent,"</div>\n</div>")})),rK.DYNAMIC,(function(e,t){var n,r,o,i,a=qO(((e.borderRadius||0)*t.mobileScale).toFixed(6)),u={width:"60px",height:"60px",backgroundColor:e.backgroundColor,borderRadius:"".concat(a,"PX"),display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},c={color:"#fff",fontSize:"12px",lineHeight:"18px",userSelect:"none",width:"2em"};2===e.skinIndex&&(c={color:"#fff",fontSize:"12px",lineHeight:"14px",userSelect:"none"});var l=2===e.skinIndex?yd(n='<img style="'.concat(iK({width:"22px",height:"22px",marginBottom:"4px"}),'" src="')).call(n,sK,'" alt="icon" />'):"";return yd(r=yd(o=yd(i='<div style="'.concat(iK(u),'">\n    ')).call(i,l,'\n    <div style="')).call(o,iK(c),'">')).call(r,e.iconContent,"</div>\n</div>")})),rK.STATIC,(function(e,t){var n,r=fK[e.skinIndex];return yd(n='<img style="'.concat(iK({width:"60px"}),'" src="')).call(n,r,'" alt="icon" />')})),rK.CUSTOM,(function(e,t){var n;return yd(n='<img style="'.concat(iK({width:"60px"}),'" src="')).call(n,e.backgroundImage,'" alt="icon" />')})),mK=function(){function e(t){var n=this;If(this,e),this.config={},this.iframeDom=null,this.receiveMessage=function(e){"mbotDialogCPcMessage"===e.data.from&&"pageHide"===e.data.event&&n.toggleIframe()},this.config=t,this.target=document.getElementById("aff-im-root"),this.removeExistingIcon(),this.createIframeIcon(),window.addEventListener("message",this.receiveMessage,!1)}return Tf(e,[{key:"removeExistingIcon",value:function(){var e,t=document.getElementById("aff-ai-agent-iframe");t&&(null===(e=t.parentNode)||void 0===e||e.removeChild(t))}},{key:"createIframeIcon",value:function(){var e=this;this.iframeDom=document.createElement("iframe"),this.iframeDom.id="aff-ai-agent-iframe",this.iframeDom.src="".concat(this.config.chatUrl,"&openType=dialog"),this.iframeDom.className="embed-chat-iframe";var t={position:"fixed",zIndex:oK,width:"414px",height:"471px",right:"16px",bottom:"16px",border:"none",overflow:"hidden",display:"none",borderRadius:"6px",boxShadow:"0px 6px 32px 2px rgba(0, 0, 0, 0.06), 0px 5px 30px 1px rgba(0, 0, 0, 0.05), 0px 4px 28px 1px rgba(0, 0, 0, 0.04)"};jO(this.iframeDom.style,t),this.target&&this.target.appendChild(this.iframeDom),this.config.autoOpen&&Gx((function(){e.toggleIframe(!0)}),1e3*this.config.autoOpenDelay)}},{key:"toggleIframe",value:function(e){!this.iframeDom||e&&"none"!==this.iframeDom.style.display||(this.iframeDom.style.display="none"===this.iframeDom.style.display?"":"none")}}]),e}();function yK(e,t){var n=db(e);if(cB){var r=cB(e);t&&(r=pC(r).call(r,(function(t){return mB(e,t).enumerable}))),n.push.apply(n,r)}return n}function bK(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?Rd(n=yK(Object(o),!0)).call(n,(function(t){ek(e,t,o[t])})):_B?IB(e,_B(o)):Rd(r=yK(Object(o))).call(r,(function(t){pb(e,t,mB(o,t))}))}return e}var wK={"left-center":{top:"50%",left:0,transform:"translateY(-50%)"},"left-bottom":{bottom:"180px",left:0},"right-center":{top:"50%",right:0,transform:"translateY(-50%)"},"right-bottom":{bottom:"180px",right:0}},AK={"left-center":{top:"50%",left:0,transform:"translateY(-50%)"},"left-bottom":{bottom:0,left:0},"right-center":{top:"50%",right:0,transform:"translateY(-50%)"},"right-bottom":{bottom:0,right:0}},_K=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};If(this,e),this.config={},this.options={},this.isMobile=!1,this.winWidth=0,this.winHeight=0,this.target=document.getElementById("aff-im-root"),this.fixedPosition=!0,this.scale=1,this.mobileScale=1,this.config=t,this.options=n,this.winWidth=n.winWidth||window.innerWidth||document.documentElement.clientWidth,this.winHeight=n.winHeight||window.innerHeight||document.documentElement.clientHeight,this.isMobile=n.isMobile||!1,this.fixedPosition=!n.target,this.target=n.target||document.getElementById("aff-im-root");var r=this.getScale(),o=r.scale,i=r.mobileScale;this.scale=o,this.mobileScale=i,this.render()}return Tf(e,[{key:"getDefaultSize",value:function(){var e=this.config,t=e.type,n=e.skinIndex;return t===rK.CUSTOM?{width:60}:this.isMobile?dK[t][n]:vK[t][n]}},{key:"getScale",value:function(){var e=this.config.width,t=this.getDefaultSize().width,n=this.isMobile?this.winWidth/375:1;return{mobileScale:n,scale:e/t*n}}},{key:"getPosition",value:function(){var e={};if(this.config.isFixedPosition)return e=this.isMobile?AK[this.config.position]:wK[this.config.position];var t=this.config.marginLeft||0,n=this.config.marginTop||0,r=this.config.horizontalPosition||"left",o=this.config.portraitPosition||"top";if(t>this.winWidth?e["left"===r?"right":"left"]=0:e[r]="".concat(this.config.marginLeft,"px"),n>this.winHeight?e["top"===o?"bottom":"top"]=0:e[o]="".concat(this.config.marginTop,"px"),!this.isMobile&&(0===e.top||"0px"===e.top)&&this.config.type===rK.CS){var i=Math.ceil(16*this.scale);e.top="".concat(i,"px")}return e}},{key:"getPositionX",value:function(){var e=this.getPosition();return 0===e.left||"0px"===e.left?"left":0===e.right||"0px"===e.right?"right":void 0}},{key:"getInnerHTML",value:function(){var e=this.config.type,t=this.getPositionX();return(this.isMobile?gK:hK)[e](this.config,{positionX:t,mobileScale:this.mobileScale})}},{key:"render",value:function(){var e,t=this,n=this.target,r=document.getElementById("aff-ai-agent-icon");r&&(null===(e=r.parentNode)||void 0===e||e.removeChild(r));(r=document.createElement("div")).id="aff-ai-agent-icon";var o=bK(bK({position:this.fixedPosition?"fixed":"absolute",zIndex:this.fixedPosition?oK:"auto"},this.getPosition()),{},{cursor:"pointer"});jO(r.style,o);var i=this.getInnerHTML();if(i=i.replace(/(\d+)px/g,(function(e,n){return"".concat(qO((qO(n)*t.scale).toFixed(6)),"px")})),r.innerHTML=i,null==n||n.append(r),this.isMobile||"dialog"!==this.config.openType)r.onclick=this.options.onClick||function(){};else{var a=new mK(this.config);r.onclick=function(){return a.toggleIframe()}}}}]),e}();function xK(e){return new _K(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}function SK(){return(SK=yF(jF.mark((function e(){var t,n,r,o,i,a,u,c,l,s,f;return jF.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=window.affImConfig,n=t.styleConfig,r=void 0===n?{}:n,o=t.siteConfig,i=r.iconPcSetting,a=r.iconMobileSetting,u=1===(null==o?void 0:o.platform),(c=u?a:i).chatUrl=(null==o?void 0:o.chatUrl)||"",l=tF(),(0,l.updateImConfig)(window.affImConfig||{}),s={},f=(null==o?void 0:o.chatUrl)||"",rW().then((function(e){var t,n=((null==e?void 0:e[0])||{}).msg||{};s=FH(n.biz_data)||{},f=LH(f,{xst:pW(),bd_bxst:hW(),bd_vid:gW(),ch:"90",sch:null!==(t=s.fromEngineCode)&&void 0!==t?t:""}),c.chatUrl=f,xK(c,{isMobile:u,onClick:function(){f&&(RH.logVisiterOpenBcpAiAgent({logType:"IMMEDIATELY"}),window.open(f))}})})).catch((function(){}));case 10:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function CK(e){var t,n=document.createElement("ins");(n.id="aff-im-root",document.getElementById(n.id))||(document.body.appendChild(n),null!=(t=window.affImConfig.siteConfig)&&t.isAiAgent?function(){SK.apply(this,arguments)}():(iB(nK).mount("#aff-im-root"),function(){if(function(e){var t=e.sdk_url,n=e.name,r=window,o=document,i="script",a=null,u=null;if(void 0!==r.sensorsDataAnalytic201505)return!1;r.sensorsDataAnalytic201505=n,r[n]=r[n]||function(e){return function(){(r[n]._q=r[n]._q||[]).push([e,arguments])}};for(var c=["track","quick","register","registerPage","registerOnce","trackSignup","trackAbtest","setProfile","setOnceProfile","appendProfile","incrementProfile","deleteProfile","unsetProfile","identify","login","logout","trackLink","clearAllRegister","getAppStatus"],l=0;l<c.length;l++)r[n][c[l]]=r[n].call(null,c[l]);r[n]._t||(a=o.createElement(i),u=o.getElementsByTagName(i)[0],a.async=1,a.src=t,a.setAttribute("charset","UTF-8"),r[n].para=e,u.parentNode.insertBefore(a,u))}({sdk_url:"https://aiff.cdn.bcebos.com/sensors%2Fonline%2Fsa-sdk-javascript-1.26.2%2Fsensorsdata.min.js",heatmap_url:"https://aiff.cdn.bcebos.com/sensors%2Fonline%2Fsa-sdk-javascript-1.26.%2Fheatmap.min.js",name:"sensors",server_url:"https://affprism.baidu.com/sa?project=production",send_type:"ajax",heatmap:{scroll_notice_map:"not_collect",clickmap:"not_collect"},is_track_single_page:!0,show_log:!1}),sensors.registerPage({platform_type:"JavaScript"}),!localStorage.getItem("sensors_logout")){localStorage.setItem("sensors_logout",1);var e,t=new RegExp("(^| )login_id=([^;]*)(;|$)"),n=(e=document.cookie.match(t))?unescape(e[2]):"";n&&(sensors.logout(),sensors.login(n))}}()))}return CK(),e.init=CK,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e}({});
